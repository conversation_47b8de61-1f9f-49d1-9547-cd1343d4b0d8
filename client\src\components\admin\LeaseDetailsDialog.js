import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Divider,
  Box,
  Chip,
  Stepper,
  Step,
  StepLabel,
  Paper
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';

const LeaseDetailsDialog = ({ open, onClose, lease }) => {
  const { translate } = useLanguage();

  if (!lease) return null;

  // Define the lease workflow steps
  const leaseSteps = [
    { label: translate('Requested'), completed: true },
    { label: translate('Approved'), completed: lease.approval_status === 'approved' || lease.status === 'active' || lease.status === 'returned' },
    { label: translate('Active'), completed: lease.status === 'active' || lease.status === 'returned' },
    { label: translate('Returned'), completed: lease.status === 'returned' }
  ];

  // Determine active step
  let activeStep = 0;
  if (lease.approval_status === 'rejected') {
    activeStep = 0; // Rejected at first step
  } else if (lease.approval_status === 'approved' && lease.status === 'pending') {
    activeStep = 1; // Approved but not yet active
  } else if (lease.status === 'active') {
    activeStep = 2; // Active lease
  } else if (lease.status === 'returned') {
    activeStep = 3; // Returned
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">{translate('Lease Details')}</Typography>
          <Button
            color="inherit"
            onClick={onClose}
            startIcon={<CloseIcon />}
          >
            {translate('Close')}
          </Button>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box mb={3}>
          <Stepper activeStep={activeStep}>
            {leaseSteps.map((step, index) => (
              <Step key={step.label} completed={step.completed}>
                <StepLabel>{step.label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>{translate('Book Information')}</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('Title')}</Typography>
              <Typography variant="body1">{lease.title}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('Author')}</Typography>
              <Typography variant="body1">{lease.author}</Typography>
            </Grid>
          </Grid>
        </Paper>

        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>{translate('Lease Information')}</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('User')}</Typography>
              <Typography variant="body1">{lease.username}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('Status')}</Typography>
              <Chip
                label={lease.status === 'pending' ? lease.approval_status : lease.status}
                color={
                  lease.approval_status === 'pending' ? 'warning' :
                  lease.approval_status === 'rejected' ? 'error' :
                  lease.status === 'active' ? 'info' :
                  lease.status === 'returned' ? 'success' : 'default'
                }
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('Request Date')}</Typography>
              <Typography variant="body1">
                {new Date(lease.lease_date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">{translate('Due Date')}</Typography>
              <Typography variant="body1">
                {new Date(lease.due_date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
            </Grid>
            {lease.approval_date && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">{translate('Approval/Rejection Date')}</Typography>
                <Typography variant="body1">
                  {new Date(lease.approval_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Typography>
              </Grid>
            )}
            {lease.return_date && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">{translate('Return Date')}</Typography>
                <Typography variant="body1">
                  {new Date(lease.return_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Typography>
              </Grid>
            )}
            {lease.rejection_reason && (
              <Grid item xs={12}>
                <Typography variant="subtitle2">{translate('Rejection Reason')}</Typography>
                <Typography variant="body1">{lease.rejection_reason}</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>
      </DialogContent>
    </Dialog>
  );
};

export default LeaseDetailsDialog;