import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  TextField,
  InputAdornment,
  Box,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Pagination,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Tooltip,
  FormControlLabel,
  Switch
} from '@mui/material';
import { Search as SearchIcon, Add as AddIcon, Info as InfoIcon, FilterList as FilterIcon, Close as CloseIcon } from '@mui/icons-material';
import { motion } from 'framer-motion';
import BookCover from '../components/books/BookCover';
import { useLanguage } from '../context/LanguageContext';
import { AuthContext } from '../context/AuthContext';
import api from '../utils/api';

const BookCatalog = () => {
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const { isAuthenticated, user } = useContext(AuthContext);

  // State for books and filtering
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [availableOnly, setAvailableOnly] = useState(false);

  // Pagination state
  const [page, setPage] = useState(1);
  const [booksPerPage] = useState(12);

  // Book suggestion state
  const [openSuggestDialog, setOpenSuggestDialog] = useState(false);
  const [suggestion, setSuggestion] = useState({
    isbn: '',
    title: '',
    author: '',
    description: ''
  });

  const [suggestLoading, setSuggestLoading] = useState(false);
  const [suggestError, setSuggestError] = useState('');
  const [suggestSuccess, setSuggestSuccess] = useState(false);
  const [remainingSuggestions, setRemainingSuggestions] = useState(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.3
      }
    }
  };

  // Fetch books and categories on component mount
  useEffect(() => {
    fetchData();

    // Only check suggestion limits if user is authenticated
    if (isAuthenticated) {
      checkRemainingSuggestions();
    } else {
      // Set default value for unauthenticated users
      setRemainingSuggestions(5);
    }
  }, [isAuthenticated, user]); // Add user to dependencies to re-check when profile is updated

  // Check if the user can make suggestions (authenticated only)
  const checkRemainingSuggestions = async () => {
    if (!isAuthenticated) {
      // Set default value for unauthenticated users
      setRemainingSuggestions(5);
      return;
    }

    // Check if user has completed their profile (teacher and grade)
    if (!user || !user.teacher || !user.grade) {
      // User hasn't completed profile, don't check suggestions
      // This prevents 403 errors for incomplete profiles
      setRemainingSuggestions(5); // Default to max weekly suggestions
      return;
    }

    try {
      // Use the direct API call with correct path
      const response = await api.direct.get('/api/suggestions/remaining');
      console.log('Suggestion limit response:', response.data);
      setRemainingSuggestions(response.data.remaining);
    } catch (error) {
      console.error('Error checking suggestion limit:', error);
      // Set a default value to prevent incorrect limit messages
      setRemainingSuggestions(5); // Default to max weekly suggestions
    }
  };

  // Fetch books and categories
  const fetchData = async () => {
    setLoading(true);
    setError('');

    try {
      // Use direct API call that bypasses the proxy
      const booksResponse = await api.direct.get('/api/books');

      // With updated API, response should be an array directly
      const booksData = Array.isArray(booksResponse.data) ?
        booksResponse.data : [];

      setBooks(booksData);
      setFilteredBooks(booksData);

      // Use direct API for categories too
      const categoriesResponse = await api.direct.get('/api/books/categories');

      // With updated API, response should be an array directly
      const categoriesData = Array.isArray(categoriesResponse.data) ?
        categoriesResponse.data : [];

      setCategories(categoriesData);

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load books. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Apply filtering when search term, category, or availability changes
  useEffect(() => {
    let filtered = [...books];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(book =>
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(book => book.category === selectedCategory);
    }

    // Filter by availability
    if (availableOnly) {
      filtered = filtered.filter(book => book.available_copies > 0);
    }

    setFilteredBooks(filtered);
    setPage(1); // Reset to first page when filters change
  }, [searchTerm, selectedCategory, availableOnly, books]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle category filter change
  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
  };

  // Toggle availability filter
  const handleAvailabilityToggle = () => {
    setAvailableOnly(!availableOnly);
  };

  // Handle pagination change
  const handlePageChange = (event, value) => {
    setPage(value);
  };

  // Navigate to book details page
  const handleBookClick = (bookId) => {
    navigate(`/books/${bookId}`);
  };

  // Open suggest book dialog
  const handleOpenSuggestDialog = () => {
    if (!isAuthenticated) {
      // Redirect to login if trying to suggest a book while not authenticated
      handleLoginRedirect();
      return;
    }

    // Check if user's email is verified
    if (user && user.email_verified === 0) {
      // Redirect to email verification page if not verified
      navigate('/verify-email');
      return;
    }

    // Check if user has completed their profile
    if (!user || !user.teacher || !user.grade) {
      // Redirect to profile completion if profile is incomplete
      navigate('/profile');
      return;
    }

    setOpenSuggestDialog(true);
  };

  // Close suggest book dialog
  const handleCloseSuggestDialog = () => {
    setOpenSuggestDialog(false);
    setSuggestion({
      isbn: '',
      title: '',
      author: '',
      description: ''
    });
  };

  // Handle suggestion form input change
  const handleSuggestionChange = (e) => {
    setSuggestion({
      ...suggestion,
      [e.target.name]: e.target.value
    });
  };

  // Submit book suggestion
  const handleSubmitSuggestion = async () => {
    if (!suggestion.title || !suggestion.author) {
      setSuggestError(translate('Title and author are required'));
      return;
    }

    // Check if user has remaining suggestions
    if (remainingSuggestions <= 0) {
      setSuggestError(translate('You have reached your weekly suggestion limit. You can make more suggestions next week.'));
      return;
    }

    setSuggestLoading(true);

    try {
      console.log('Submitting suggestion:', suggestion);

      // Use direct API for submitting
      const response = await api.direct.post('/api/suggestions', suggestion);

      console.log('Suggestion response:', response.data);
      setSuggestSuccess(true);

      // Update remaining suggestions count directly from the response
      if (response.data.remaining !== undefined) {
        setRemainingSuggestions(response.data.remaining);
      } else {
        // Fallback to decrementing the current count
        const newRemainingCount = Math.max(0, remainingSuggestions - 1);
        setRemainingSuggestions(newRemainingCount);
      }

      // Close dialog after a delay to show success message
      setTimeout(() => {
        handleCloseSuggestDialog();
        setSuggestSuccess(false);
      }, 2000);
    } catch (error) {
      console.error('Error submitting suggestion:', error);
      setSuggestError(translate('Failed to submit book suggestion. Please try again.'));
    } finally {
      setSuggestLoading(false);
    }
  };

  // Add a function to handle login redirect
  const handleLoginRedirect = () => {
    navigate('/login', { state: { from: '/books' } });
  };

  // Calculate pagination
  const indexOfLastBook = page * booksPerPage;
  const indexOfFirstBook = indexOfLastBook - booksPerPage;
  // Ensure filteredBooks is always an array before calling slice
  const currentBooks = Array.isArray(filteredBooks) ?
    filteredBooks.slice(indexOfFirstBook, indexOfLastBook) : [];
  const pageCount = Array.isArray(filteredBooks) ?
    Math.ceil(filteredBooks.length / booksPerPage) : 0;

  // Render loading state
  if (loading) {
    return (
      <Container sx={{ py: 5, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          {translate('Loading books...')}
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {translate('Book Catalog')}
      </Typography>

      <Box sx={{ mb: 4, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, justifyContent: 'space-between', alignItems: { xs: 'stretch', sm: 'center' }, gap: 2 }}>
        <TextField
          label={translate('Search Books')}
          variant="outlined"
          size="small"
          fullWidth
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: { sm: '350px' } }}
        />

        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: '150px' }}>
            <InputLabel id="category-select-label">{translate('Category')}</InputLabel>
            <Select
              labelId="category-select-label"
              value={selectedCategory}
              label={translate('Category')}
              onChange={handleCategoryChange}
            >
              <MenuItem value="">{translate('All Categories')}</MenuItem>
              {categories.map((category) => (
                <MenuItem key={category.id} value={category.name}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenSuggestDialog}
          >
            {translate('Suggest Book')}
          </Button>

          {!isAuthenticated && (
            <Tooltip title={translate('Sign in to borrow or suggest books')}>
              <InfoIcon color="info" />
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* No results message */}
      {!loading && filteredBooks.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="textSecondary">
            {translate('No books found matching your criteria')}
          </Typography>
        </Box>
      )}

      {/* Add a login prompt for unauthenticated users */}
      {!isAuthenticated && (
        <Alert
          severity="info"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleLoginRedirect}>
              {translate('Sign In')}
            </Button>
          }
        >
          {translate('Sign in to borrow books or make suggestions.')}
        </Alert>
      )}

      {/* Book grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={4}>
          {currentBooks.map((book) => (
            <Grid item xs={6} sm={6} md={4} key={book.id}>
              <motion.div variants={itemVariants}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                  }}
                  elevation={3}
                >
                  <CardActionArea onClick={() => handleBookClick(book.id)}>
                    <Box sx={{
                      pt: 2,
                      px: 2,
                      height: { xs: 180, sm: 200, md: 240 },
                      display: 'flex',
                      justifyContent: 'center'
                    }}>
                      <BookCover
                        imageUrl={book.cover_image}
                        title={book.title}
                        sx={{
                          maxHeight: '100%',
                          width: 'auto',
                          maxWidth: '100%',
                          objectFit: 'contain',
                          boxShadow: 'none'
                        }}
                      />
                    </Box>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography gutterBottom variant="h6" component="h2" noWrap>
                        {book.title}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" component="p" noWrap>
                        {book.author}
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1,
                        justifyContent: 'space-between',
                        mt: 2
                      }}>
                        {book.category && (
                          <Chip
                            label={book.category}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ maxWidth: '100%', overflow: 'hidden' }}
                          />
                        )}
                        <Chip
                          label={book.available_copies > 0 ?
                            `${book.available_copies} ${translate('Available')}` :
                            translate('Unavailable')}
                          size="small"
                          color={book.available_copies > 0 ? 'success' : 'error'}
                        />
                      </Box>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </motion.div>

      {/* Pagination */}
      {pageCount > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={pageCount}
            page={page}
            onChange={handlePageChange}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Suggest Book Dialog */}
      <Dialog open={openSuggestDialog} onClose={handleCloseSuggestDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{translate('Suggest a Book')}</DialogTitle>
        <DialogContent>
          {suggestSuccess ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              {translate('Thank you! Your book suggestion has been submitted and will be reviewed by our team.')}
            </Alert>
          ) : null}

          {suggestError ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {suggestError}
            </Alert>
          ) : null}

          {!suggestSuccess && (
            <>
              {remainingSuggestions !== null && remainingSuggestions > 0 ? (
                <DialogContentText sx={{ mb: 2 }}>
                  {translate('Suggest a book you would like to see in our library. You have')} {remainingSuggestions} {translate('suggestions remaining this week.')}
                </DialogContentText>
              ) : remainingSuggestions !== null && remainingSuggestions <= 0 ? (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  {translate('You have reached your weekly suggestion limit. You can make more suggestions next week.')}
                </Alert>
              ) : (
                <DialogContentText sx={{ mb: 2 }}>
                  {translate('Suggest a book you would like to see in our library.')}
                </DialogContentText>
              )}

              <TextField
                autoFocus
                margin="dense"
                id="title"
                name="title"
                label={translate('Book Title')}
                type="text"
                fullWidth
                value={suggestion.title}
                onChange={handleSuggestionChange}
                variant="outlined"
                required
                sx={{ mb: 2 }}
                disabled={suggestLoading}
              />

              <TextField
                margin="dense"
                id="author"
                name="author"
                label={translate('Author')}
                type="text"
                fullWidth
                value={suggestion.author}
                onChange={handleSuggestionChange}
                variant="outlined"
                required
                sx={{ mb: 2 }}
                disabled={suggestLoading}
              />

              <TextField
                margin="dense"
                id="isbn"
                name="isbn"
                label={translate('ISBN (optional)')}
                type="text"
                fullWidth
                value={suggestion.isbn}
                onChange={handleSuggestionChange}
                variant="outlined"
                sx={{ mb: 2 }}
                disabled={suggestLoading}
              />

              <TextField
                margin="dense"
                id="description"
                name="description"
                label={translate('Description (optional)')}
                multiline
                rows={3}
                fullWidth
                value={suggestion.description}
                onChange={handleSuggestionChange}
                variant="outlined"
                disabled={suggestLoading}
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSuggestDialog} color="secondary">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleSubmitSuggestion}
            color="primary"
            variant="contained"
            disabled={
              suggestLoading ||
              !suggestion.title ||
              !suggestion.author ||
              (remainingSuggestions !== null && remainingSuggestions <= 0) ||
              suggestSuccess
            }
            startIcon={suggestLoading ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {translate('Submit')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      {suggestError && (
        <Snackbar
          open={true}
          autoHideDuration={6000}
          onClose={() => {
            setSuggestError('');
            setSuggestSuccess(false);
          }}
        >
          <Alert onClose={() => {
            setSuggestError('');
            setSuggestSuccess(false);
          }} severity="error" sx={{ width: '100%' }}>
            {suggestError}
          </Alert>
        </Snackbar>
      )}

      {suggestSuccess && (
        <Snackbar
          open={true}
          autoHideDuration={6000}
          onClose={() => {
            setSuggestSuccess(false);
          }}
        >
          <Alert onClose={() => {
            setSuggestSuccess(false);
          }} severity="success" sx={{ width: '100%' }}>
            {translate('Book suggestion submitted successfully')}
          </Alert>
        </Snackbar>
      )}
    </Container>
  );
};

export default BookCatalog;