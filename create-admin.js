// <PERSON>ript to create an admin user in the book leasing application
require('dotenv').config();
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');

// Database connection
const dbPath = path.resolve(__dirname, 'server/database/bookleasing.db');
console.log('Database path:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('Connected to the SQLite database');
  
  // Admin user credentials
  const username = 'adminuser';
  const email = '<EMAIL>';
  const password = 'AdminPass123!';
  const role = 'admin';
  
  // Hash the password
  bcrypt.hash(password, 10, (err, hash) => {
    if (err) {
      console.error('Error hashing password:', err.message);
      db.close();
      process.exit(1);
    }
    
    // Check if admin user already exists
    db.get('SELECT * FROM users WHERE email = ? OR username = ?', [email, username], (err, user) => {
      if (err) {
        console.error('Error checking for existing user:', err.message);
        db.close();
        process.exit(1);
      }
      
      if (user) {
        console.log('Admin user already exists!');
        console.log('Username:', user.username);
        console.log('Email:', user.email);
        console.log('Role:', user.role);
        db.close();
        return;
      }
      
      // Insert the admin user
      db.run(
        'INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)',
        [username, email, hash, role],
        function(err) {
          if (err) {
            console.error('Error creating admin user:', err.message);
          } else {
            console.log('Admin user created successfully!');
            console.log('Username:', username);
            console.log('Password:', password);
            console.log('Email:', email);
            console.log('Role:', role);
          }
          db.close();
        }
      );
    });
  });
});