const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  console.log('[PROXY] Setting up API proxy from http://localhost:80 to http://localhost:8080');
  
  // Wildcard proxy for all API requests
  const apiProxy = createProxyMiddleware({
    target: 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
    logLevel: 'debug',
    onProxyReq: (proxyReq, req, res) => {
      console.log(`[PROXY] ${req.method} ${req.url} → ${proxyReq.method} ${proxyReq.protocol}//${proxyReq.host}${proxyReq.path}`);
    },
    onProxyRes: (proxyRes, req, res) => {
      console.log(`[PROXY] Response ${proxyRes.statusCode} for ${req.method} ${req.url}`);
    },
    onError: (err, req, res) => {
      console.error('[PROXY] Error:', err);
      if (!res.headersSent) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          error: 'Proxy error', 
          message: `Failed to connect to API server at ${err.target}`,
          details: err.message 
        }));
      }
    },
    // Removing pathRewrite to ensure the /api prefix is preserved
    pathRewrite: undefined
  });
  
  // Apply proxy to API routes
  app.use('/api', apiProxy);
  
  console.log('[PROXY] Proxy setup complete - routing /api/* to http://localhost:8080/api/*');
}; 