import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import api from '../utils/api';
import { AuthContext } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import {
  Container,
  Grid,
  Typography,
  Button,
  Paper,
  Box,
  Chip,
  Divider,
  Rating,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { LocalLibrary, CalendarToday, Person, Category, Language } from '@mui/icons-material';
import { motion } from 'framer-motion';
import BookCover from '../components/books/BookCover';

const BookDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const { isAuthenticated, user } = useContext(AuthContext);

  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [leaseDialogOpen, setLeaseDialogOpen] = useState(false);
  const [leaseData, setLeaseData] = useState({
    duration: 14,
    custom_message: ''
  });
  const [leaseLoading, setLeaseLoading] = useState(false);
  const [leaseSuccess, setLeaseSuccess] = useState(false);
  const [leaseError, setLeaseError] = useState('');

  useEffect(() => {
    fetchBookDetails();
  }, [id]);

  const fetchBookDetails = async () => {
    setLoading(true);
    setError('');

    try {
      // Use the configured API utility instead of direct axios
      const response = await api.direct.get(`/api/books/${id}`);

      // With updated API, the response is the book object directly
      const bookData = response.data;

      if (!bookData) {
        setError('Book not found');
        return;
      }

      setBook(bookData);
    } catch (err) {
      console.error('Error fetching book details:', err);
      setError('Failed to load book details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleLeaseDialogOpen = () => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: `/books/${id}` } });
      return;
    }

    // Check if user's email is verified
    if (user && user.email_verified === 0) {
      // Redirect to email verification page if not verified
      navigate('/verify-email');
      return;
    }

    setLeaseDialogOpen(true);
  };

  const handleLeaseDialogClose = () => {
    setLeaseDialogOpen(false);
  };

  const handleLeaseDataChange = (e) => {
    setLeaseData({
      ...leaseData,
      [e.target.name]: e.target.value
    });
  };

  const handleLeaseRequest = async () => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: `/books/${id}` } });
      return;
    }

    // Check if user's email is verified
    if (user && user.email_verified === 0) {
      navigate('/verify-email');
      return;
    }

    setLeaseLoading(true);
    setLeaseError('');

    try {
      console.log('Submitting lease request with data:', {
        book_id: id,
        days: leaseData.duration,
        custom_message: leaseData.custom_message
      });

      // Use direct API for lease requests with improved error handling
      const response = await api.direct.post('/api/leases', {
        book_id: id,
        days: leaseData.duration,
        custom_message: leaseData.custom_message
      });

      console.log('Lease request response:', response.data);

      // Set success state
      setLeaseSuccess(true);
      setLeaseError('');

      // Close dialog and reset state after a delay
      setTimeout(() => {
        setLeaseDialogOpen(false);
        setLeaseSuccess(false);

        // Refresh book details to update availability
        fetchBookDetails();
      }, 3000);
    } catch (err) {
      console.error('Error requesting lease:', err);
      // Extract more detailed error information
      let errorMessage = 'Failed to request lease. Please try again later.';
      if (err.response) {
        console.log('Error response from server:', err.response.data);
        // Handle specific errors
        if (err.response.status === 429 && err.response.data) {
          const timeLeft = err.response.data.timeLeft;
          errorMessage = timeLeft
            ? `Too many requests. Please wait ${timeLeft} seconds before trying again.`
            : 'Too many requests. Please wait a moment before trying again.';
        } else if (err.response.status === 403 && err.response.data?.profile_incomplete) {
          // Handle profile incomplete error
          errorMessage = err.response.data.message || 'Please complete your profile to rent books.';
          // Close the dialog and let the global profile completion modal handle it
          setLeaseDialogOpen(false);
        } else {
          // Use the server's message if available, otherwise use the default
          errorMessage = err.response.data?.message || errorMessage;
        }
      }
      setLeaseError(errorMessage);
    } finally {
      setLeaseLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        <Button variant="outlined" onClick={() => navigate('/books')}>
          {translate('Back to Book Catalog')}
        </Button>
      </Container>
    );
  }

  if (!book) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="info" sx={{ mb: 2 }}>{translate('Book not found')}</Alert>
        <Button variant="outlined" onClick={() => navigate('/books')}>
          {translate('Back to Book Catalog')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Grid container spacing={4}>
            <Grid xs={12} md={4}>
              <Box sx={{ maxWidth: '100%', height: 'auto', display: 'flex', justifyContent: 'center' }}>
                <BookCover
                  imageUrl={book.cover_image}
                  title={book.title}
                  sx={{
                    width: "100%",
                    height: "auto",
                    maxHeight: "400px"
                  }}
                />
              </Box>
            </Grid>

            <Grid xs={12} md={8}>
              <Typography variant="h4" component="h1" gutterBottom>
                {book.title}
              </Typography>

              <Typography variant="h6" color="textSecondary" gutterBottom>
                {translate('By')} {book.author}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Chip
                  label={book.available_copies > 0 ? translate('Available') : translate('Unavailable')}
                  color={book.available_copies > 0 ? 'success' : 'error'}
                  variant="outlined"
                />

                <Chip
                  label={`${book.available_copies}/${book.total_copies} ${translate('copies')}`}
                  color="primary"
                  variant="outlined"
                />

                {book.category && (
                  <Chip
                    icon={<Category fontSize="small" />}
                    label={book.category}
                    variant="outlined"
                  />
                )}
              </Box>

              <Box sx={{ my: 3 }}>
                <Typography variant="body1" paragraph sx={{ whiteSpace: 'pre-line' }}>
                  {book.description}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Grid container spacing={2} sx={{ mb: 3 }}>
                {book.isbn && (
                  <Grid xs={12} sm={6}>
                    <Typography variant="body2" color="textSecondary">
                      <strong>ISBN:</strong> {book.isbn}
                    </Typography>
                  </Grid>
                )}

                {book.published_year && (
                  <Grid xs={12} sm={6}>
                    <Typography variant="body2" color="textSecondary">
                      <CalendarToday fontSize="small" sx={{ mr: 1, verticalAlign: 'middle' }} />
                      <strong>{translate('Published')}:</strong> {book.published_year}
                    </Typography>
                  </Grid>
                )}

                {book.publisher && (
                  <Grid xs={12} sm={6}>
                    <Typography variant="body2" color="textSecondary">
                      <strong>{translate('Publisher')}:</strong> {book.publisher}
                    </Typography>
                  </Grid>
                )}
              </Grid>

              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  disabled={book.available_copies <= 0}
                  onClick={handleLeaseDialogOpen}
                >
                  {translate('Borrow This Book')}
                </Button>

                <Button
                  variant="outlined"
                  onClick={() => navigate('/books')}
                >
                  {translate('Back to Catalog')}
                </Button>
              </Box>

              {!isAuthenticated && (
                <Alert severity="info" sx={{ mt: 3 }}>
                  {translate('You need to sign in to borrow books.')}
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => navigate('/login', { state: { from: `/books/${id}` } })}
                    sx={{ ml: 1 }}
                  >
                    {translate('Sign In')}
                  </Button>
                </Alert>
              )}
            </Grid>
          </Grid>
        </Paper>
      </motion.div>

      {/* Lease Request Dialog */}
      <Dialog open={leaseDialogOpen} onClose={handleLeaseDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>{translate('Borrow')} "{book.title}"</DialogTitle>
        <DialogContent>
          {leaseSuccess ? (
            <Alert severity="success" sx={{ my: 2 }}>
              {translate('Your borrowing request has been submitted successfully! We will notify you when it is approved.')}
            </Alert>
          ) : (
            <>
              <DialogContentText>
                {translate('Please confirm that you would like to borrow this book and specify for how long:')}
              </DialogContentText>

              <FormControl fullWidth margin="normal">
                <InputLabel>{translate('Duration (Days)')}</InputLabel>
                <Select
                  name="duration"
                  value={leaseData.duration}
                  onChange={handleLeaseDataChange}
                  label={translate('Duration (Days)')}
                >
                  <MenuItem value={7}>7 {translate('days')}</MenuItem>
                  <MenuItem value={14}>14 {translate('days')}</MenuItem>
                  <MenuItem value={21}>21 {translate('days')}</MenuItem>
                  <MenuItem value={30}>30 {translate('days')}</MenuItem>
                </Select>
              </FormControl>

              <TextField
                margin="normal"
                fullWidth
                name="custom_message"
                label={translate('Optional Message')}
                multiline
                rows={3}
                value={leaseData.custom_message}
                onChange={handleLeaseDataChange}
                helperText={translate('Provide any additional details about your borrowing request')}
              />

              {leaseError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {leaseError}
                </Alert>
              )}
            </>
          )}
        </DialogContent>

        {!leaseSuccess && (
          <DialogActions>
            <Button onClick={handleLeaseDialogClose}>{translate('Cancel')}</Button>
            <Button
              onClick={handleLeaseRequest}
              color="primary"
              variant="contained"
              disabled={leaseLoading}
            >
              {leaseLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                translate('Submit Request')
              )}
            </Button>
          </DialogActions>
        )}
      </Dialog>
    </Container>
  );
};

export default BookDetails;