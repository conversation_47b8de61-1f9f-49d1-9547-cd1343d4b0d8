// Middleware to check if user has completed their profile
const requireCompleteProfile = (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const userId = req.user.id;
    
    // Check if user's profile is complete
    db.get('SELECT profile_completed, class_teacher, grade FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      // Check if profile is complete
      if (!user.profile_completed || !user.class_teacher || !user.grade) {
        return res.status(403).json({ 
          message: 'Profile incomplete. Please complete your profile by providing your class teacher and grade information.',
          profile_incomplete: true
        });
      }
      
      next();
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  requireCompleteProfile
};
