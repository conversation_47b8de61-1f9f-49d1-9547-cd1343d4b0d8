import { createTheme } from '@mui/material/styles';

const darkTheme = createTheme({
  palette: {
    mode: 'dark', // Enable Material UI's dark mode logic
    primary: {
      main: '#ffffff', // White for primary elements (buttons, links)
      contrastText: '#000000', // Black text on primary elements
    },
    secondary: {
      main: '#888888', // Grey for secondary elements
      contrastText: '#ffffff',
    },
    background: {
      default: '#000000', // Black background
      paper: '#111111', // Slightly lighter black for paper surfaces (cards, dialogs)
    },
    text: {
      primary: '#ffffff', // White primary text
      secondary: '#bbbbbb', // Lighter grey secondary text
      disabled: '#555555',
    },
    divider: '#333333', // Dark grey divider
    success: {
      main: '#4caf50', // Keep success green
      contrastText: '#000000',
    },
    error: {
      main: '#f44336', // Keep error red
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ff9800', // Keep warning orange
      contrastText: '#000000',
    },
    info: {
      main: '#2196f3', // Keep info blue
      contrastText: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif', // Use Inter font if available
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 700,
      fontSize: '2rem',
      lineHeight: 1.2,
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
      lineHeight: 1.2,
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.2,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.2,
    },
    h6: {
      fontWeight: 600,
      fontSize: '1rem',
      lineHeight: 1.2,
    },
    button: {
      textTransform: 'none',
      fontWeight: 600, // Slightly bolder buttons
    },
  },
  components: {
    // Reset/Adjust specific component styles for the dark theme
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 6, // Sharper corners
          padding: '8px 16px',
          transition: 'background-color 0.2s ease, color 0.2s ease, transform 0.1s ease',
          boxShadow: 'none',
          '&:hover': {
            transform: 'translateY(-1px)', // Subtle hover lift
            boxShadow: 'none', // No shadow on hover by default
          },
          '&:focus': {
            outline: 'none',
            boxShadow: 'none',
          },
          '&.MuiButton-containedSecondary': {
            boxShadow: 'none',
            '&:hover': {
              boxShadow: 'none',
            },
            '&:focus': {
              boxShadow: 'none',
            },
          },
          // Remove any purple glow or focus styles
          '&::after': {
            display: 'none',
          },
        },
        containedPrimary: {
          backgroundColor: '#ffffff',
          color: '#000000',
          '&:hover': {
            backgroundColor: '#dddddd', // Slightly grey on hover
          },
        },
        containedSecondary: {
          backgroundColor: '#333333',
          color: '#ffffff',
          '&:hover': {
            backgroundColor: '#444444',
          },
        },
        outlinedPrimary: {
          borderColor: '#ffffff',
          color: '#ffffff',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderColor: '#ffffff',
          },
        },
        outlinedSecondary: {
          borderColor: '#888888',
          color: '#888888',
          '&:hover': {
            backgroundColor: 'rgba(136, 136, 136, 0.1)',
            borderColor: '#aaaaaa',
            color: '#aaaaaa',
          },
        },
        textPrimary: {
          color: '#ffffff',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          },
        },
        textSecondary: {
          color: '#888888',
          '&:hover': {
            backgroundColor: 'rgba(136, 136, 136, 0.1)',
            color: '#aaaaaa',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: 'none', // Remove default shadows
          border: '1px solid #333333', // Add border instead of shadow
          backgroundImage: 'none', // Remove gradients
          backgroundColor: '#111111',
          transition: 'border-color 0.2s ease',
          '&:hover': {
            borderColor: '#555555', // Subtle border highlight on hover
            transform: 'none', // Remove hover lift/shadow changes
            boxShadow: 'none',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none', // Remove gradients
          borderRadius: 0, // Reset border radius if needed, or set globally in shape
           backgroundColor: '#111111', // Ensure paper background is set
        },
        elevation1: { boxShadow: 'none', border: '1px solid #333333' }, // Replace elevation with border
        elevation2: { boxShadow: 'none', border: '1px solid #333333' },
        elevation3: { boxShadow: 'none', border: '1px solid #333333' },
        // Add more elevation levels if used
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'none', // Remove gradients
          backgroundColor: '#000000', // Solid black app bar
          boxShadow: 'none', // Remove shadow
          borderBottom: '1px solid #333333', // Add subtle border
          backdropFilter: 'none', // Remove blur
        },
        colorDefault: {
          backgroundColor: '#000000',
        },
        colorPrimary: {
          backgroundColor: '#000000',
        },
      },
    },
    MuiBottomNavigation: {
       styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#000000',
          borderTop: '1px solid #333333',
          boxShadow: 'none',
        },
      },
    },
    MuiBottomNavigationAction: {
       styleOverrides: {
        root: {
          color: '#888888', // Grey inactive icons
          '&.Mui-selected': {
            color: '#ffffff', // White selected icons
          },
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#222222', // Darker header background
          borderBottom: '1px solid #444444',
        },
      },
    },
     MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#282828', // Subtle hover for table rows
          },
          // Remove border bottom from rows if MuiTable has borderCollapse: 'collapse'
          '& td, & th': {
             borderBottom: '1px solid #333333',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          color: '#ffffff',
          fontWeight: 600,
        },
        body: {
           color: '#ffffff',
        },
      }
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
          border: '1px solid #555555',
           backgroundColor: '#333333', // Dark grey chip background
        },
        // Add overrides for specific colors if needed
        colorPrimary: {
          backgroundColor: '#ffffff',
          color: '#000000',
          border: 'none',
        },
        colorSecondary: {
          backgroundColor: '#555555',
          color: '#ffffff',
          border: 'none',
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 8,
          border: '1px solid #333333',
          boxShadow: 'none',
          backgroundImage: 'none',
          backgroundColor: '#111111',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '&.Mui-focused fieldset': {
              borderColor: '#555555', // Dark gray border instead of blue when focused
            },
            '&:hover fieldset': {
              borderColor: '#444444', // Slightly lighter gray on hover
            },
          },
          '& .MuiInputLabel-root.Mui-focused': {
            color: '#777777', // Dark gray label instead of blue when focused
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#555555', // Dark gray border when focused
            borderWidth: '1px',
          },
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          '&.Mui-focused': {
            color: '#777777', // Dark gray label when focused
          },
        },
      },
    },
    MuiSelect: {
       styleOverrides: {
         root: {
           '&.MuiInputBase-root': { // Target the root element for background etc.
             backgroundColor: '#222222',
             borderRadius: 6,
              '& fieldset': {
                  borderColor: '#444444',
              },
              '&:hover fieldset': {
                  borderColor: '#666666',
              },
              '&.Mui-focused fieldset': {
                  borderColor: '#555555', // Changed from white to dark gray to match other inputs
              },
           },
         },
         icon: {
            color: '#ffffff', // White dropdown icon
         },
       },
    },
  },
  shape: {
    borderRadius: 6, // Slightly sharper global radius
  },
  breakpoints: { // Keep existing breakpoints or adjust as needed
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
});

// export default theme; // Remove the old theme export
export default darkTheme; // Export the new dark theme