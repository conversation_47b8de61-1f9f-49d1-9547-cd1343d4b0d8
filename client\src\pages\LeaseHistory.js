import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Tabs,
  Tab,
  Paper,
  Divider
} from '@mui/material';
import { motion } from 'framer-motion';
import LeaseCalendar from '../components/lease/LeaseCalendar';
import LeaseArchive from '../components/lease/LeaseArchive';
import { useLanguage } from '../context/LanguageContext';

const LeaseHistory = () => {
  const [activeTab, setActiveTab] = useState(0);
  const { translate } = useLanguage();

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Container maxWidth="xl" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom
          sx={{ 
            fontSize: { xs: '1.75rem', sm: '2rem', md: '2.125rem' }
          }}
        >
          {translate('My Book Leases')}
        </Typography>
        <Typography 
          variant="subtitle1" 
          color="text.secondary" 
          paragraph
          sx={{ 
            fontSize: { xs: '0.875rem', sm: '1rem' }
          }}
        >
          {translate('View your upcoming leases in the calendar and browse your lease history.')}
        </Typography>

        <Paper 
          elevation={3} 
          sx={{ 
            mt: { xs: 2, sm: 3 }, 
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={activeTab} 
              onChange={handleTabChange} 
              aria-label="lease history tabs"
              centered
              sx={{
                '& .MuiTab-root': {
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  minWidth: { xs: 'auto', sm: '160px' },
                  px: { xs: 1, sm: 2 }
                }
              }}
            >
              <Tab label={translate('Lease Calendar')} />
              <Tab label={translate('Lease Archive')} />
            </Tabs>
          </Box>

          <Box sx={{ 
            p: { xs: 1, sm: 2, md: 3 },
            '& .MuiPaper-root': {
              borderRadius: { xs: 1, sm: 2 }
            }
          }}>
            {activeTab === 0 && (
              <Box>
                <LeaseCalendar />
              </Box>
            )}
            {activeTab === 1 && (
              <Box>
                <LeaseArchive />
              </Box>
            )}
          </Box>
        </Paper>
      </Container>
    </motion.div>
  );
};

export default LeaseHistory;
