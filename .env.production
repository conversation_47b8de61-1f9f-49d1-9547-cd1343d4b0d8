# Environment Variables
PORT=80
JWT_SECRET=your_secure_jwt_secret_key_here
NODE_ENV=production

# Firebase Configuration
REACT_APP_FIREBASE_API_KEY="AIzaSyB9o3mWtEbZKqUzidg5zTFFIm-tsog3YZc"
REACT_APP_FIREBASE_AUTH_DOMAIN="educanet-login.firebaseapp.com"
REACT_APP_FIREBASE_PROJECT_ID="educanet-login"
REACT_APP_FIREBASE_STORAGE_BUCKET="educanet-login.firebasestorage.app"
REACT_APP_FIREBASE_MESSAGING_SENDER_ID="114355394944"
REACT_APP_FIREBASE_APP_ID="1:114355394944:web:66fd7bfe252727b490c965"
REACT_APP_FIREBASE_MEASUREMENT_ID="G-PL0<PERSON>K3E5Z"

# Google Books API Configuration
GOOGLE_BOOKS_API_KEY=AIzaSyD5aSwx9Lb7uuwBLBsBGxZTjeh2K37xgvQ

# Firebase Admin Configuration
# This should be set via Vercel Environment Variables for security
# FIREBASE_ADMIN_CREDENTIALS= 