import React, { createContext, useState, useContext, useEffect } from 'react';

// English-Czech translations
const translations = {
  en: {
    // Navbar
    'Book Leasing': 'Book Leasing',
    'Edu Bookshelf': 'Edu Bookshelf',
    'Home': 'Home',
    'Books': 'Books',
    'Leases': 'Leases',
    'Profile': 'Profile',
    'My Profile': 'My Profile',
    'Admin Dashboard': 'Admin Dashboard',
    'Logout': 'Logout',
    'Login': 'Login',
    'Register': 'Register',
    'Guest Options': 'Guest Options',
    'User Settings': 'User Settings',
    'Admin': 'Admin',
    'Lease History': 'Lease History',

    // Book Scanner Admin
    'Book Scanner Admin': 'Book Scanner Admin',
    'Scan book barcodes to fetch information automatically, then add them to your library.':
      'Scan book barcodes to fetch information automatically, then add them to your library.',
    'Start Scanner': 'Start Scanner',
    'Add ISBN Manually': 'Add ISBN Manually',
    'Enter Book ISBN': 'Enter Book ISBN',
    'Enter 10 or 13 digit ISBN': 'Enter 10 or 13 digit ISBN',
    'Search': 'Search',
    'Example:': 'Example:',
    'How to use the Book Scanner': 'How to use the Book Scanner',
    'Pro tip:': 'Pro tip:',
    'For best results, ensure good lighting and hold the camera 6-8 inches from the barcode.':
      'For best results, ensure good lighting and hold the camera 6-8 inches from the barcode.',
    'Note for mobile users:': 'Note for mobile users:',
    'If the camera doesn\'t start, check that your browser has camera permissions enabled and you\'re using HTTPS.':
      'If the camera doesn\'t start, check that your browser has camera permissions enabled and you\'re using HTTPS.',
    'Fetching book information...': 'Fetching book information...',
    'Book found with limited data': 'Book found with limited data',
    'Book information retrieved successfully': 'Book information retrieved successfully',
    'No book information found for this ISBN': 'No book information found for this ISBN',
    'No book information found': 'No book information found',
    'Failed to fetch book information. Please try again.': 'Failed to fetch book information. Please try again.',
    'Error retrieving book information': 'Error retrieving book information',
    'or': 'or',

    // BookForm
    'Edit Book Details': 'Edit Book Details',
    'Loading book details...': 'Loading book details...',
    'Title': 'Title',
    'Author': 'Author',
    'Title is required': 'Title is required',
    'Author is required': 'Author is required',
    'Description': 'Description',
    'Category': 'Category',
    'None': 'None',
    'Create New Category': 'Create New Category',
    'New Category Name': 'New Category Name',
    'Enter new category name': 'Enter new category name',
    'Auto-created': 'Auto-created',
    'Please enter a name for the new category': 'Please enter a name for the new category',
    'Published Year': 'Published Year',
    'Publisher': 'Publisher',
    'Total Copies': 'Total Copies',
    'Available Copies': 'Available Copies',
    'Total copies must be at least 1': 'Total copies must be at least 1',
    'Available copies must be between 0 and total copies': 'Available copies must be between 0 and total copies',
    'Cover Image': 'Cover Image',
    'Use URL': 'Use URL',
    'Upload File': 'Upload File',
    'Take Photo': 'Take Photo',
    'Cover Image URL': 'Cover Image URL',
    'Enter image URL': 'Enter image URL',
    'Select Image File': 'Select Image File',
    'Selected file': 'Selected file',
    'Capture Photo': 'Capture Photo',
    'Unable to load image. Please check the URL or try another image.': 'Unable to load image. Please check the URL or try another image.',
    'Book cover': 'Book cover',
    'Save Book': 'Save Book',
    'Saving...': 'Saving...',
    'Book title is required': 'Book title is required',
    'Failed to upload cover image, but will continue saving book': 'Failed to upload cover image, but will continue saving book',
    'Book saved successfully': 'Book saved successfully',
    'Failed to save book. Please try again.': 'Failed to save book. Please try again.',
    'Error saving book': 'Error saving book',
    'Unknown error': 'Unknown error',
    'Error accessing camera. Please check permissions.': 'Error accessing camera. Please check permissions.',

    // Common
    'Save': 'Save',
    'Cancel': 'Cancel',
    'Delete': 'Delete',
    'Edit': 'Edit',
    'Loading...': 'Loading...',
    'Error': 'Error',
    'Success': 'Success',
    'Warning': 'Warning',
    'Info': 'Info',

    // BarcodeScanner
    'Position barcode in the center': 'Position barcode in the center',
    'Camera permission denied. Please allow camera access.': 'Camera permission denied. Please allow camera access.',
    'Perfect! Barcode detected with high confidence.': 'Perfect! Barcode detected with high confidence.',
    'Good scan. Hold steady for confirmation.': 'Good scan. Hold steady for confirmation.',
    'Almost there! Hold the barcode closer.': 'Almost there! Hold the barcode closer.',
    'Barcode detected. Improve lighting or hold steady.': 'Barcode detected. Improve lighting or hold steady.',
    'Move closer to the barcode and center it.': 'Move closer to the barcode and center it.',
    'Camera initialization timed out. Please try again.': 'Camera initialization timed out. Please try again.',
    'Scanner container reference is not available. Please try again.': 'Scanner container reference is not available. Please try again.',
    'Camera permission denied. Please allow camera access in your browser settings.': 'Camera permission denied. Please allow camera access in your browser settings.',
    'No camera found. Please connect a camera and try again.': 'No camera found. Please connect a camera and try again.',
    'Camera is in use by another application. Please close other applications using the camera.': 'Camera is in use by another application. Please close other applications using the camera.',
    'Camera cannot satisfy the requested constraints. Try a different camera.': 'Camera cannot satisfy the requested constraints. Try a different camera.',
    'Error accessing camera: ': 'Error accessing camera: ',
    'Barcode found! Processing...': 'Barcode found! Processing...',
    'Barcode detected, hold steady...': 'Barcode detected, hold steady...',
    'Barcode identified, confirming...': 'Barcode identified, confirming...',
    'Starting camera...': 'Starting camera...',
    'Please allow camera access when prompted': 'Please allow camera access when prompted',
    'Retry Camera': 'Retry Camera',
    'Try Different Camera': 'Try Different Camera',
    'Turn off flash': 'Turn off flash',
    'Turn on flash': 'Turn on flash',
    'Switch camera': 'Switch camera',
    'Toggle focus mode': 'Toggle focus mode',

    // Footer
    'Book Leasing App': 'Book Leasing App',
    'A responsive application for leasing books with a beautiful UI and animations.': 'A responsive application for leasing books with a beautiful UI and animations.',
    'Quick Links': 'Quick Links',
    'Contact': 'Contact',
    'Email': 'Email',
    'Phone': 'Phone',
    'Book Leasing. All rights reserved.': 'Book Leasing. All rights reserved.',
    'School Library': 'School Library',
    'Praha EDUCAnet Library System': 'Praha EDUCAnet Library System',
    'Official Site:': 'Official Site:',
    'Opening Hours': 'Opening Hours',
    'Monday': 'Monday',
    'Tuesday': 'Tuesday',
    'Wednesday': 'Wednesday',
    'Thursday': 'Thursday',
    'Friday': 'Friday',
    'Closed': 'Closed',
    'EDUCAnet Praha. All rights reserved.': 'EDUCAnet Praha. All rights reserved.',

    // Email Verification Page
    'emailVerification.title': 'Verify Your Email Address',
    'emailVerification.checkingStatus': 'Checking verification status...',
    'emailVerification.statusVerified': 'Your email has been successfully verified!',
    'emailVerification.statusVerifiedMessage': 'You will be redirected to your profile shortly.',
    'emailVerification.statusUnverifiedMessage': 'A verification email has been sent to',
    'emailVerification.pleaseCheckInbox': 'Please check your inbox (and spam folder) and click the link to verify your email address.',
    'emailVerification.didNotReceive': "Didn't receive the email?",
    'emailVerification.resendEmail': 'Resend Verification Email',
    'emailVerification.resendSuccess': 'A new verification email has been sent. Please check your inbox.',
    'emailVerification.resendError': 'Failed to resend verification email. Please try again later.',
    'emailVerification.rateLimited': 'You have requested too many verification emails. Please wait a moment before trying again.',
    'emailVerification.checkStatusError': 'Failed to check verification status. Please try refreshing the page.',
    'emailVerification.firebaseInitError': 'Failed to initialize Firebase for verification. Please refresh.',
    'emailVerification.backToLogin': 'Back to Login',
    'emailVerification.goToProfile': 'Go to Profile',
    'emailVerification.emailSentTo': 'Verification email sent to',
    'emailVerification.ifNotReceived': 'If you don\u0027t receive it within a few minutes, please check your spam folder or try resending.',
    'emailVerification.resendWait': 'You can resend the email in',
    'emailVerification.seconds': 'seconds',
    'emailVerification.firstResendFree': 'First resend is immediate',
    'emailVerification.cooldownInfo': 'Additional resends have a cooldown period',

    // Email Verification Confirmation Page
    'verification.title': 'Email Verification',
    'verification.verifying': 'Verifying your email...',
    'verification.success': 'Your email has been successfully verified!',
    'verification.failed': 'Failed to verify email. The link may be expired or invalid.',
    'verification.invalidLink': 'Invalid verification link. No token or code provided.',
    'verification.unexpectedError': 'An unexpected error occurred during verification.',
    'verification.continueToHome': 'Continue to Home',
    'verification.tryAgain': 'Try Again',

    // Book suggestion
    'Suggest Book': 'Suggest Book',
    'Suggest a Book': 'Suggest a Book',
    'Suggest a book you would like to see in our library. You have': 'Suggest a book you would like to see in our library. You have',
    'suggestions remaining this week.': 'suggestions remaining this week.',
    'Book Title': 'Book Title',
    'ISBN (Optional)': 'ISBN (Optional)',
    'If you know the ISBN, please provide it': 'If you know the ISBN, please provide it',
    'Cancel': 'Cancel',
    'Submitting...': 'Submitting...',
    'Submit Suggestion': 'Submit Suggestion',
    'Title and author are required': 'Title and author are required',
    'Book suggestion submitted successfully': 'Book suggestion submitted successfully',
    'Failed to submit suggestion': 'Failed to submit suggestion',
    'Loading books...': 'Loading books...',
    'Book Catalog': 'Book Catalog',
    'Search Books': 'Search Books',
    'Category': 'Category',
    'All Categories': 'All Categories',
    'Available Only': 'Available Only',
    'No books found matching your criteria': 'No books found matching your criteria',
    'Available': 'Available',
    'Unavailable': 'Unavailable',
    // Suggestions Manager
    'Book Suggestions': 'Book Suggestions',
    'Manage user-submitted book suggestions': 'Manage user-submitted book suggestions',
    'No pending suggestions': 'No pending suggestions',
    'Title': 'Title',
    'Author': 'Author',
    'ISBN': 'ISBN',
    'User': 'User',
    'Submitted': 'Submitted',
    'Actions': 'Actions',
    'Uncategorized': 'Uncategorized',
    'Approve': 'Approve',
    'Reject': 'Reject',
    'Assign Category': 'Assign Category',
    'Approve Suggestion': 'Approve Suggestion',
    'Are you sure you want to approve this suggestion? This will add the book to the library catalog.': 'Are you sure you want to approve this suggestion? This will add the book to the library catalog.',
    'Reject Suggestion': 'Reject Suggestion',
    'Are you sure you want to reject this suggestion? This action cannot be undone.': 'Are you sure you want to reject this suggestion? This action cannot be undone.',
    'Assign a category to this book suggestion.': 'Assign a category to this book suggestion.',
    'Select Category': 'Select Category',
    'Or create a new category:': 'Or create a new category:',
    'New Category': 'New Category',
    'Processing...': 'Processing...',
    'Assign': 'Assign',
    'Suggestion approved successfully': 'Suggestion approved successfully',
    'Failed to approve suggestion': 'Failed to approve suggestion',
    'Suggestion rejected': 'Suggestion rejected',
    'Failed to reject suggestion': 'Failed to reject suggestion',
    'Please select or enter a category': 'Please select or enter a category',
    'Category assigned successfully': 'Category assigned successfully',
    'Failed to assign category': 'Failed to assign category',
    'Failed to load suggestions. Please try again.': 'Failed to load suggestions. Please try again.',
    // Book cover
    'No cover available': 'No cover available',

    // Admin Dashboard
    'Dashboard': 'Dashboard',
    'Books': 'Books',
    'Leases': 'Leases',
    'Users': 'Users',
    'Book Scanner': 'Book Scanner',
    'Suggestions': 'Suggestions',
    'Security': 'Security',
    'Welcome to Admin Dashboard': 'Welcome to Admin Dashboard',
    'Use the tabs above to manage books, leases, users, and security settings.': 'Use the tabs above to manage books, leases, users, and security settings.',
    'Book Management': 'Book Management',
    'This component will allow administrators to manage the book catalog, including adding, editing, and removing books from the library.': 'This component will allow administrators to manage the book catalog, including adding, editing, and removing books from the library.',
    'Add New Book': 'Add New Book',
    'Import Books': 'Import Books',
    'Lease Management': 'Lease Management',
    'Bulk Actions': 'Bulk Actions',
    'Calendar View': 'Calendar View',

    // UserManagement
    'Username': 'Username',
    'Email': 'Email',
    'Role': 'Role',
    'Status': 'Status',
    'Verified': 'Verified',
    'Created': 'Created',
    'Actions': 'Actions',
    'No users found': 'No users found',
    'Ban User': 'Ban User',
    'Unban User': 'Unban User',
    'Ban Reason': 'Ban Reason',
    'Reason for banning this user': 'Reason for banning this user',
    'Are you sure you want to ban': 'Are you sure you want to ban',
    'Are you sure you want to unban': 'Are you sure you want to unban',
    'Processing...': 'Processing...',
    'Change User Role': 'Change User Role',
    'Update role for user:': 'Update role for user:',
    'Updating...': 'Updating...',
    'Update Role': 'Update Role',
    'Delete User Account': 'Delete User Account',
    'Warning: This action cannot be undone.': 'Warning: This action cannot be undone.',
    'Are you sure you want to permanently delete the account for': 'Are you sure you want to permanently delete the account for',
    'Deleting...': 'Deleting...',
    'Delete User': 'Delete User',
    'Verify User Email': 'Verify User Email',
    'Email has been verified successfully for': 'Email has been verified successfully for',
    'Manually verify email for user:': 'Manually verify email for user:',
    'Email address:': 'Email address:',
    'This will mark the user email as verified without requiring them to go through the email verification process.': 'This will mark the user email as verified without requiring them to go through the email verification process.',
    'Verifying...': 'Verifying...',
    'Verify Email': 'Verify Email',
    'Email verified': 'Email verified',
    'Email not verified': 'Email not verified',
    'Change Role': 'Change Role',
    'Admin accounts cannot be deleted.': 'Admin accounts cannot be deleted.',
    'Active': 'Active',
    'Banned': 'Banned',

    // Book Management
    'Books List': 'Books List',
    'Categories': 'Categories',
    'Search by title, author, ISBN...': 'Search by title, author, ISBN...',
    'Scan ISBN': 'Scan ISBN',
    'Import': 'Import',
    'Add Book': 'Add Book',
    'Loading books...': 'Loading books...',
    'Copies': 'Copies',
    'Edit Book': 'Edit Book',
    'No books found': 'No books found',
    'Rows per page:': 'Rows per page:',
    'Book Categories': 'Book Categories',
    'Add Category': 'Add Category',
    'Loading categories...': 'Loading categories...',
    'No categories found': 'No categories found',
    'Category Name': 'Category Name',
    'Edit Category': 'Edit Category',
    'Add New Category': 'Add New Category',
    'Update': 'Update',
    'Add': 'Add',
    'Import Books from CSV': 'Import Books from CSV',
    'Upload a CSV file with the following columns:': 'Upload a CSV file with the following columns:',
    'Select CSV File': 'Select CSV File',
    'Selected file:': 'Selected file:',
    'Importing...': 'Importing...',
    'Book Information': 'Book Information',
    'Scan or Enter ISBN': 'Scan or Enter ISBN',
    'This book already exists in the library': 'This book already exists in the library',
    'Or enter ISBN manually:': 'Or enter ISBN manually:',
    'Enter ISBN': 'Enter ISBN',
    'Back': 'Back',
    'Save Book': 'Save Book',
    'Close': 'Close',
    '-- None --': '-- None --',
    '+ Add New Category': '+ Add New Category',
    'Brief description of the book...': 'Brief description of the book...',
    'https://example.com/book-cover.jpg': 'https://example.com/book-cover.jpg',
    'Unable to load image from URL': 'Unable to load image from URL',
    'Book cover preview': 'Book cover preview',
    'Library Management': 'Library Management',
    'Book Details': 'Book Details',
    'ISBN-13': 'ISBN-13',
    'ISBN-10': 'ISBN-10',

    // Home Page
    'Discover Your Next Great Read': 'Discover Your Next Great Read',
    'Explore our curated collection of books and lease them with just a few clicks.': 'Explore our curated collection of books and lease them with just a few clicks.',
    'Browse Books': 'Browse Books',
    'No popular books available yet': 'No popular books available yet',
    'Browse Catalog': 'Browse Catalog',
    'What Our Users Say': 'What Our Users Say',

    // Security Analytics
    'Security Analytics': 'Security Analytics',
    'Refresh': 'Refresh',
    'Current Security Mode': 'Current Security Mode',
    'Configure the system\'s security level based on threat detection.': 'Configure the system\'s security level based on threat detection.',
    'reCAPTCHA is configured with a test key (6LeIxAcTAAAAA...). For production use, replace with your own keys in server/config.js or set environment variables.': 'reCAPTCHA is configured with a test key (6LeIxAcTAAAAA...). For production use, replace with your own keys in server/config.js or set environment variables.',
    'Normal Mode': 'Normal Mode',
    'Mild Security (reCAPTCHA)': 'Mild Security (reCAPTCHA)',
    'High Security (Lockdown)': 'High Security (Lockdown)',
    'Threat Analytics': 'Threat Analytics',
    'Security Incidents': 'Security Incidents',
    'Activity Monitoring': 'Activity Monitoring',
    'System Activity': 'System Activity',
    'Security Status Summary': 'Security Status Summary',
    'Suspicious IPs Blocked': 'Suspicious IPs Blocked',
    'Rate Limit Exceeded': 'Rate Limit Exceeded',
    'Spam Attempts': 'Spam Attempts',
    'Security Score': 'Security Score',
    'Confirm Security Mode Change': 'Confirm Security Mode Change',
    'You are about to switch to Normal security mode. This mode provides standard security protections without additional verification steps for users.': 'You are about to switch to Normal security mode. This mode provides standard security protections without additional verification steps for users.',
    'You are about to switch to Mild security mode. This will require reCAPTCHA verification for suspicious activities and may slow down some operations.': 'You are about to switch to Mild security mode. This will require reCAPTCHA verification for suspicious activities and may slow down some operations.',
    'WARNING: You are about to enable High security (Lockdown) mode. Only administrators will be able to access the system. All regular user access will be blocked until this mode is disabled.': 'WARNING: You are about to enable High security (Lockdown) mode. Only administrators will be able to access the system. All regular user access will be blocked until this mode is disabled.',
    'Normal': 'Normal',
    'Unknown': 'Unknown',
    'Suspicious IPs': 'Suspicious IPs',
    'Rate Limit Exceeds': 'Rate Limit Exceeds',
    'Invalid Tokens': 'Invalid Tokens',
    'Threat Incidents': 'Threat Incidents',
    'Login Attempts': 'Login Attempts',
    'Failed Logins': 'Failed Logins',
    'API Requests': 'API Requests',
    'Failed to change security mode': 'Failed to change security mode',

    // Profile
    'My Profile': 'My Profile',
    'Edit Profile': 'Edit Profile',
    'Save Changes': 'Save Changes',
    'Username': 'Username',
    'Email': 'Email',
    'Profile updated successfully': 'Profile updated successfully',
    'Failed to update profile. Please try again.': 'Failed to update profile. Please try again.',
    'Lease date': 'Lease date',
    'Due date': 'Due date',
    'Return date': 'Return date',
    'Book Title': 'Book Title',
    'Current Leases': 'Current Leases',
    'Past Leases': 'Past Leases',
    'No leases found': 'No leases found',
    'Status': 'Status',
    'Actions': 'Actions',
    'Overdue': 'Overdue',
    'Active': 'Active',
    'Returned': 'Returned',
    'Return': 'Return',
    'Renew': 'Renew',
    'View Details': 'View Details',

    // Lease History
    'My Book Leases': 'My Book Leases',
    'View your upcoming leases in the calendar and browse your lease history.': 'View your upcoming leases in the calendar and browse your lease history.',
    'Lease Calendar': 'Lease Calendar',
    'Lease Archive': 'Lease Archive',
    'Lease History Archive': 'Lease History Archive',
    'You have no past lease records. Completed, cancelled, or overdue leases will appear here.': 'You have no past lease records. Completed, cancelled, or overdue leases will appear here.',
    'Failed to load lease history. Please try again later.': 'Failed to load lease history. Please try again later.',
    'View Lease History': 'View Lease History',
    'View your current and past leases in the lease history page.': 'View your current and past leases in the lease history page.',
    'Leased': 'Leased',
    'Due': 'Due',
    'Returned': 'Returned',
    'Cancelled': 'Cancelled',
    'View Book': 'View Book',
    'ACTIVE': 'ACTIVE',
    'RETURNED': 'RETURNED',
    'OVERDUE': 'OVERDUE',
    'PENDING': 'PENDING',
    'CANCELLED': 'CANCELLED',
    'COMPLETED': 'COMPLETED',

    // Calendar
    'Expiring Leases': 'Expiring Leases',
    'Filter by Book': 'Filter by Book',
    'Filter by User': 'Filter by User',
    'All Books': 'All Books',
    'All Users': 'All Users',
    'Refresh data': 'Refresh data',
    'Failed to load calendar data. Please try again later.': 'Failed to load calendar data. Please try again later.',
    'Status': 'Status',
    'Approval': 'Approval',
    'Leases Expiring Soon': 'Leases Expiring Soon',
    'No leases expiring in the next 7 days.': 'No leases expiring in the next 7 days.',
    'User': 'User',
    'Days Left': 'Days Left',
    'Details': 'Details',
    'Unknown Book': 'Unknown Book',

    // Barcode Scanner
    'Barcode Scanner': 'Barcode Scanner',
    'Focused': 'Focused',
    'Focusing': 'Focusing',
    'Failed to access camera': 'Failed to access camera',
    'Try Again': 'Try Again',
    'Starting camera...': 'Starting camera...',
    'Auto-focusing...': 'Auto-focusing...',
    'Ready to scan!': 'Ready to scan!',
    'Position barcode in frame': 'Position barcode in frame',
    'Tap anywhere to refocus • Supports all barcode formats': 'Tap anywhere to refocus • Supports all barcode formats',
    'Tap screen to focus • Works with ISBN, UPC, EAN codes': 'Tap screen to focus • Works with ISBN, UPC, EAN codes',
    'Barcode detected!': 'Barcode detected!',
    'Processing book information...': 'Processing book information...',
    'Universal Barcode Scanner': 'Universal Barcode Scanner',
    'Ready to scan any barcode containing ISBN': 'Ready to scan any barcode containing ISBN',
    'Processing...': 'Processing...',
    'Please wait while we process your barcode': 'Please wait while we process your barcode',
    'Manual Input': 'Manual Input',
    'Enter ISBN manually': 'Enter ISBN manually',
    'Enter ISBN': 'Enter ISBN',
    'Submit': 'Submit',

    // Profile - Account Details
    'Account Information': 'Account Information',
    'Account Type:': 'Account Type:',
    'Administrator': 'Administrator',
    'User': 'User',
    'Active Leases:': 'Active Leases:',
    'Total Leases:': 'Total Leases:',
    'You don\'t have any active leases.': 'You don\'t have any active leases.',

    // AuthPopup new strings
    'Connecting to Google...': 'Connecting to Google...',
    'Please complete the sign-in process in the Google window.': 'Please complete the sign-in process in the Google window.',
    'Authentication Successful!': 'Authentication Successful!',
    'Please wait while we log you in...': 'Please wait while we log you in...',
    'This window should close automatically. If not, you may close it.': 'This window should close automatically. If not, you may close it.',
    'Authentication Failed': 'Authentication Failed',
    'Try Again': 'Try Again',
    'Preparing Google Sign-In...': 'Preparing Google Sign-In...',
    'Authentication failed. Please try again or close this window.': 'Authentication failed. Please try again or close this window.',
    'Authentication cancelled. You can close this window.': 'Authentication cancelled. You can close this window.',
    'Popup issue detected. Please ensure popups are allowed and try again.': 'Popup issue detected. Please ensure popups are allowed and try again.',
    'Could not communicate back to the main application. Please close this window and try again.': 'Could not communicate back to the main application. Please close this window and try again.',

    // Profile Completion Modal
    'Complete Your Profile': 'Complete Your Profile',
    'To rent books and make suggestions, please provide your class teacher and grade information.': 'To rent books and make suggestions, please provide your class teacher and grade information.',
    'Class Teacher': 'Class Teacher',
    'Enter your class teacher\'s name': 'Enter your class teacher\'s name',
    'Example: Mrs. Smith, Mr. Johnson': 'Example: Mrs. Smith, Mr. Johnson',
    'Grade/Class': 'Grade/Class',
    'Enter your grade or class': 'Enter your grade or class',
    'Example: 9A, Grade 10, Class 11B': 'Example: 9A, Grade 10, Class 11B',
    'Both class teacher and grade are required.': 'Both class teacher and grade are required.',
    'Failed to update profile. Please try again.': 'Failed to update profile. Please try again.',
    'Saving...': 'Saving...',
    'Save Profile': 'Save Profile',

    // User Profile Page
    'Profile Information': 'Profile Information',
    'Class Teacher:': 'Class Teacher:',
    'Grade/Class:': 'Grade/Class:',
    'Not specified': 'Not specified',
    'Profile incomplete. Please complete your profile to rent books and make suggestions.': 'Profile incomplete. Please complete your profile to rent books and make suggestions.',
    'Complete Profile': 'Complete Profile',
    'Profile updated successfully!': 'Profile updated successfully!',

    // Admin User Management
    'Edit Profile': 'Edit Profile',
    'Edit User Profile': 'Edit User Profile',
    'Update profile information for user:': 'Update profile information for user:',
    'Enter class teacher name': 'Enter class teacher name',
    'Enter grade or class': 'Enter grade or class',
    'Updating...': 'Updating...',
    'Update Profile': 'Update Profile',

    // Error Messages
    'Please complete your profile by providing your class teacher and grade information.': 'Please complete your profile by providing your class teacher and grade information.',
    'Please log in to continue.': 'Please log in to continue.',
    'User information not available.': 'User information not available.',
    'Please verify your email address first.': 'Please verify your email address first.',
    'Failed to fetch profile status': 'Failed to fetch profile status',

    // User Profile Additional
    'Account Type:': 'Account Type:',
    'Active Leases:': 'Active Leases:',
    'Total Leases:': 'Total Leases:',
    'Active Leases': 'Active Leases',
    'Past Leases': 'Past Leases',
    'Untitled Book': 'Untitled Book',
    'Unknown Author': 'Unknown Author',
    'by': 'by',
    'Leased on:': 'Leased on:',
    'Due date:': 'Due date:',
    'Returned on:': 'Returned on:',
    'Cancelled on:': 'Cancelled on:',
    'Pending': 'Pending',
    'Overdue': 'Overdue',
    'Active': 'Active',
    'Cancellable': 'Cancellable',
    'Return Book': 'Return Book',
    'Cancel Lease': 'Cancel Lease',
    'You don\'t have any active leases.': 'You don\'t have any active leases.',
    'View Lease Calendar': 'View Lease Calendar',
    'You don\'t have any past leases yet.': 'You don\'t have any past leases yet.',
    'Cancel Lease': 'Cancel Lease',
    'Are you sure you want to cancel your lease for': 'Are you sure you want to cancel your lease for',
    'This action cannot be undone. You can only cancel leases within 3 hours of creating them.': 'This action cannot be undone. You can only cancel leases within 3 hours of creating them.',
    'No, Keep Lease': 'No, Keep Lease',
    'Yes, Cancel Lease': 'Yes, Cancel Lease',

    // Additional missing translations
    'Submit': 'Submit',
    'Description (optional)': 'Description (optional)',
    'Suggest a book you would like to see in our library.': 'Suggest a book you would like to see in our library.',
    'You have reached your weekly suggestion limit. You can make more suggestions next week.': 'You have reached your weekly suggestion limit. You can make more suggestions next week.',

    // Lease Details Dialog
    'Requested': 'Requested',
    'Approved': 'Approved',
    'Returned': 'Returned',
    'Lease Information': 'Lease Information',
    'Book Information': 'Book Information',
    'Lease Timeline': 'Lease Timeline',
    'Close': 'Close',

    // Admin components
    'No book suggestions found.': 'No book suggestions found.',
    'Date': 'Date',
    'Suggested By': 'Suggested By',
    'Request Date': 'Request Date',
    'Due Date': 'Due Date',
    'Approval/Rejection Date': 'Approval/Rejection Date',
    'Return Date': 'Return Date',
    'Rejection Reason': 'Rejection Reason',
    'Lease Details': 'Lease Details',
    'Title': 'Title',
    'Author': 'Author',

    // SuggestionManagement
    'Book Suggestions': 'Book Suggestions',
    'Refresh suggestions': 'Refresh suggestions',
    'Filter': 'Filter',
    'All Suggestions': 'All Suggestions',
    'Pending': 'Pending',
    'Approved': 'Approved',
    'Rejected': 'Rejected',
    'ISBN': 'ISBN',
    'Actions': 'Actions',
    'Book Suggestion Details': 'Book Suggestion Details',
    'by': 'by',
    'Suggested by:': 'Suggested by:',
    'Date suggested:': 'Date suggested:',
    'No description available.': 'No description available.',
    'Rejection Reason:': 'Rejection Reason:',
    'Reject': 'Reject',
    'Approve': 'Approve',
    'Are you sure you want to permanently delete this suggestion?': 'Are you sure you want to permanently delete this suggestion?',
    'Book suggestion approved successfully!': 'Book suggestion approved successfully!',
    'Failed to approve suggestion. Please try again.': 'Failed to approve suggestion. Please try again.',
    'Book suggestion rejected.': 'Book suggestion rejected.',
    'Failed to reject suggestion. Please try again.': 'Failed to reject suggestion. Please try again.',
    'Book suggestion deleted successfully.': 'Book suggestion deleted successfully.',
    'Failed to delete suggestion. Please try again.': 'Failed to delete suggestion. Please try again.',
    'Failed to load suggestions': 'Failed to load suggestions',

    // Login/Auth pages
    'Login': 'Login',
    'Email Address': 'Email Address',
    'Password': 'Password',
    'Sign In': 'Sign In',
    'Sign in with Google': 'Sign in with Google',
    'Don\'t have an account? Register': 'Don\'t have an account? Register',
    'Invalid credentials or server error. Please try again.': 'Invalid credentials or server error. Please try again.',
    'Email and password are required': 'Email and password are required',
    'Don\'t have an account?': 'Don\'t have an account?',
    'Sign Up': 'Sign Up',
    'OR': 'OR',
    'Register': 'Register',
    'Create an Account': 'Create an Account',
    'Username': 'Username',
    'Confirm Password': 'Confirm Password',
    'Passwords do not match': 'Passwords do not match',
    'Registration failed. Please check your details or try again later.': 'Registration failed. Please check your details or try again later.',

    // Error messages
    'Email is required': 'Email is required',
    'Email is invalid': 'Email is invalid',
    'Password is required': 'Password is required',
    'Invalid email or password': 'Invalid email or password',
    'Your account has been banned. Please contact support.': 'Your account has been banned. Please contact support.',
    'System is in lockdown mode. Only administrators can access.': 'System is in lockdown mode. Only administrators can access.',
    'Server error. Please try again later.': 'Server error. Please try again later.',
    'An account with this email or username already exists.': 'An account with this email or username already exists.',
    'Please fill in all required fields.': 'Please fill in all required fields.',
    'Please enter a valid email address.': 'Please enter a valid email address.',
    'Invalid registration data. Please check your information.': 'Invalid registration data. Please check your information.',

    // Form helpers
    'Choose a unique username': 'Choose a unique username',
    'Enter a valid email address': 'Enter a valid email address',
    'Minimum 6 characters': 'Minimum 6 characters',
    'Must match password': 'Must match password',
    'Already have an account?': 'Already have an account?',
    'Already have an account? Sign In': 'Already have an account? Sign In',
    'Registration failed. Please try again.': 'Registration failed. Please try again.',

    // Email Verification
    'Email Verification Required': 'Email Verification Required',
    'Your email has been successfully verified! Redirecting to your profile...': 'Your email has been successfully verified! Redirecting to your profile...',
    'Your email address': 'Your email address',
    'needs to be verified before you can access this feature.': 'needs to be verified before you can access this feature.',
    'We\'ve sent a verification link to your email address. Please check your inbox (and spam folder) and click the verification link to complete the verification process.': 'We\'ve sent a verification link to your email address. Please check your inbox (and spam folder) and click the verification link to complete the verification process.',

    // Register page additional
    'Create an Account': 'Create an Account',
    'Registration Complete': 'Registration Complete',
    'Your account has been created successfully!': 'Your account has been created successfully!',
    'A verification email has been sent to': 'A verification email has been sent to',
    'Please check your inbox (and spam folder) and click the verification link to complete your registration.': 'Please check your inbox (and spam folder) and click the verification link to complete your registration.',
    'You can still use the application, but some features may be limited until you verify your email address.': 'You can still use the application, but some features may be limited until you verify your email address.',
    'You can now log in to your account.': 'You can now log in to your account.',
    'Go to Login': 'Go to Login',
    'Already have an account?': 'Already have an account?',
    'Username is required': 'Username is required',
    'Username must be at least 3 characters': 'Username must be at least 3 characters',
    'Email is required': 'Email is required',
    'Email is invalid': 'Email is invalid',
    'Password is required': 'Password is required',
    'Password must be at least 6 characters': 'Password must be at least 6 characters',

    // Database Management
    'Database': 'Database',
    'Database Management': 'Database Management',
    'Tables Overview': 'Tables Overview',
    'Table Data': 'Table Data',
    'Schema Management': 'Schema Management',
    'Manage': 'Manage',
    'Protected': 'Protected',
    'Add Column': 'Add Column',
    'Add Row': 'Add Row',
    'Column Name': 'Column Name',
    'Data Type': 'Data Type',
    'Nullable': 'Nullable',
    'Default Value (optional)': 'Default Value (optional)',
    'Add New Column': 'Add New Column',
    'Add New Row': 'Add New Row',
    'Edit Row': 'Edit Row',
    'Confirm Delete': 'Confirm Delete',
    'Are you sure you want to delete this row? This action cannot be undone.': 'Are you sure you want to delete this row? This action cannot be undone.',
    'Update Row': 'Update Row',
    'Primary Key': 'Primary Key',
    'Cannot delete protected column': 'Cannot delete protected column',
    'Cannot modify protected table': 'Cannot modify protected table',
    'Column added successfully': 'Column added successfully',
    'Column deleted successfully': 'Column deleted successfully',
    'Row added successfully': 'Row added successfully',
    'Row updated successfully': 'Row updated successfully',
    'Row deleted successfully': 'Row deleted successfully',
    'SQL Query': 'SQL Query',
    'SQL Query Console': 'SQL Query Console',
    'Execute Query': 'Execute Query',
    'Clear': 'Clear',
    'Query Result': 'Query Result',
    'No results returned.': 'No results returned.',
    'Changes:': 'Changes:',
    'Last Insert ID:': 'Last Insert ID:',
    'Please enter a SQL query': 'Please enter a SQL query',
    'Query executed successfully': 'Query executed successfully',
    'Query failed:': 'Query failed:',
    'Advanced Feature:': 'Advanced Feature:',
    'Execute custom SQL queries. Only SELECT, INSERT, UPDATE, DELETE operations are allowed.': 'Execute custom SQL queries. Only SELECT, INSERT, UPDATE, DELETE operations are allowed.',

    // Security Analytics
    '24-Hour Activity Monitor': '24-Hour Activity Monitor',
    'Time': 'Time',
    'Time (24h)': 'Time (24h)',
    'Login Activity': 'Login Activity',
    'API Requests Today': 'API Requests Today',
    'Logins Today': 'Logins Today',
    'Failed Logins Today': 'Failed Logins Today',
    'Suspicious IPs': 'Suspicious IPs',

    // Books Page and Book Details - Missing translations
    'Search by title or author': 'Search by title or author',
    'All': 'All',
    'No books found matching your criteria.': 'No books found matching your criteria.',
    'Book not found': 'Book not found',
    'Back to Book Catalog': 'Back to Book Catalog',
    'By': 'By',
    'copies': 'copies',
    'Borrow This Book': 'Borrow This Book',
    'Back to Catalog': 'Back to Catalog',
    'You need to sign in to borrow books.': 'You need to sign in to borrow books.',
    'Sign in to borrow books or make suggestions.': 'Sign in to borrow books or make suggestions.',
    'Sign in to borrow or suggest books': 'Sign in to borrow or suggest books',
    'Sign In': 'Sign In',
    'Book not found.': 'Book not found.',
    'by': 'by',
    'N/A': 'N/A',
    'Lease This Book': 'Lease This Book',
    'Currently Unavailable': 'Currently Unavailable',
    'This book is currently leased by another user.': 'This book is currently leased by another user.',
    'Login to lease this book.': 'Login to lease this book.',
    'Borrow': 'Borrow',
    'Your borrowing request has been submitted successfully! We will notify you when it is approved.': 'Your borrowing request has been submitted successfully! We will notify you when it is approved.',
    'Please confirm that you would like to borrow this book and specify for how long:': 'Please confirm that you would like to borrow this book and specify for how long:',

    // Barcode Scanner - Missing translations
    'Scanner stopped - Book details loaded': 'Scanner stopped - Book details loaded',
    'Barcode detected! Looking up ISBN...': 'Barcode detected! Looking up ISBN...',
    'Searching for book...': 'Searching for book...',
    '✓ Barcode Detected!': '✓ Barcode Detected!',
    'Manual Entry': 'Manual Entry',
    'Enter ISBN (optional)': 'Enter ISBN (optional)',
    'Enter an ISBN to auto-fill book details, or leave blank to enter manually.': 'Enter an ISBN to auto-fill book details, or leave blank to enter manually.',
    'Search ISBN': 'Search ISBN',
    'Create Empty': 'Create Empty',
    'Book not found. Please verify the barcode was scanned correctly or enter details manually.': 'Book not found. Please verify the barcode was scanned correctly or enter details manually.',
    'Error during book lookup. Please try again or verify the barcode was scanned correctly.': 'Error during book lookup. Please try again or verify the barcode was scanned correctly.',
    'Error checking local library. Please try again or verify the barcode was scanned correctly.': 'Error checking local library. Please try again or verify the barcode was scanned correctly.',

    // Text Recognition
    'Recognizing': 'Recognizing',
    'Point camera at text': 'Point camera at text',
    'Scan Now': 'Scan Now',
    'Initializing text recognition...': 'Initializing text recognition...',
    'Error': 'Error',
    'Failed to start text recognition': 'Failed to start text recognition',
    'Camera access denied. Please grant permission.': 'Camera access denied. Please grant permission.',
    'No camera found on this device.': 'No camera found on this device.',
    'Align numbers within this box': 'Align numbers within this box',

    // Book Cover
    'No Cover Available': 'No Cover Available',

    // Additional missing translations
    'Remaining Suggestions': 'Remaining Suggestions',
    'Book Information': 'Book Information',
    'Add New Book': 'Add New Book',
    'Book found! Please review and save.': 'Book found! Please review and save.',
    'Book information found! Please review and save.': 'Book information found! Please review and save.',

    // Navbar and Navigation
    'Account settings': 'Account settings',
    'Email not verified': 'Email not verified',
    'Email verified': 'Email verified',
    'Leases': 'Leases',

    // Admin Dashboard
    'Book Management': 'Book Management',
    'Category Management': 'Category Management',
    'User Management': 'User Management',
    'Lease Management': 'Lease Management',

    // Camera Error Messages
    'Camera access denied. Please allow camera permissions and try again.': 'Camera access denied. Please allow camera permissions and try again.',
    'Camera initialization failed. Please try refreshing the page.': 'Camera initialization failed. Please try refreshing the page.',
    'No camera found. Please ensure your device has a camera.': 'No camera found. Please ensure your device has a camera.',
    'Camera permission denied. Please allow camera access in your browser settings.': 'Camera permission denied. Please allow camera access in your browser settings.',
    'Camera is being used by another application. Please close other camera apps and try again.': 'Camera is being used by another application. Please close other camera apps and try again.',
    'Camera settings not supported. Trying with basic settings...': 'Camera settings not supported. Trying with basic settings...',

    // reCAPTCHA Messages
    'Enhanced security is enabled. Additional verification required.': 'Enhanced security is enabled. Additional verification required.',
    'Security verification unavailable. Please try again later.': 'Security verification unavailable. Please try again later.',
    'reCAPTCHA is loading. Please wait and try again.': 'reCAPTCHA is loading. Please wait and try again.',
    'reCAPTCHA verification failed. Please try again.': 'reCAPTCHA verification failed. Please try again.',
    'Security verification failed. Please try again.': 'Security verification failed. Please try again.',
  },
  cs: {
    // Email Verification Page (Czech)
    'emailVerification.title': 'Ověřte svou e-mailovou adresu',
    'emailVerification.checkingStatus': 'Kontrola stavu ověření...',
    'emailVerification.statusVerified': 'Váš e-mail byl úspěšně ověřen!',
    'emailVerification.statusVerifiedMessage': 'Brzy budete přesměrováni na svůj profil.',
    'emailVerification.statusUnverifiedMessage': 'Ověřovací e-mail byl odeslán na adresu',
    'emailVerification.pleaseCheckInbox': 'Zkontrolujte prosím svou doručenou poštu (a složku se spamem) a klikněte na odkaz pro ověření vaší e-mailové adresy.',
    'emailVerification.didNotReceive': 'Neobdrželi jste e-mail?',
    'emailVerification.resendEmail': 'Znovu odeslat ověřovací e-mail',
    'emailVerification.resendSuccess': 'Nový ověřovací e-mail byl odeslán. Zkontrolujte prosím svou doručenou poštu.',
    'emailVerification.resendError': 'Nepodařilo se znovu odeslat ověřovací e-mail. Zkuste to prosím později.',
    'emailVerification.rateLimited': 'Požádali jste o příliš mnoho ověřovacích e-mailů. Počkejte prosím chvíli, než to zkusíte znovu.',
    'emailVerification.checkStatusError': 'Nepodařilo se zkontrolovat stav ověření. Zkuste prosím obnovit stránku.',
    'emailVerification.firebaseInitError': 'Nepodařilo se inicializovat Firebase pro ověření. Obnovte prosím stránku.',
    'emailVerification.backToLogin': 'Zpět na přihlášení',
    'emailVerification.goToProfile': 'Přejít na profil',
    'emailVerification.emailSentTo': 'Ověřovací e-mail odeslán na adresu',
    'emailVerification.ifNotReceived': 'Pokud jej neobdržíte během několika minut, zkontrolujte prosím složku se spamem nebo zkuste odeslat znovu.',
    'emailVerification.resendWait': 'E-mail můžete znovu odeslat za',
    'emailVerification.seconds': 'sekund',
    'emailVerification.firstResendFree': 'První opětovné odeslání je okamžité',
    'emailVerification.cooldownInfo': 'Další opětovná odeslání mají ochrannou lhůtu',

    // Email Verification Confirmation Page (Czech)
    'verification.title': 'Ověření e-mailu',
    'verification.verifying': 'Ověřování vašeho e-mailu...',
    'verification.success': 'Váš e-mail byl úspěšně ověřen!',
    'verification.failed': 'Nepodařilo se ověřit e-mail. Odkaz může být vypršelý nebo neplatný.',
    'verification.invalidLink': 'Neplatný ověřovací odkaz. Nebyl poskytnut žádný token nebo kód.',
    'verification.unexpectedError': 'Během ověřování došlo k neočekávané chybě.',
    'verification.continueToHome': 'Pokračovat na domovskou stránku',
    'verification.tryAgain': 'Zkusit znovu',

    // Navbar
    'School Libary': 'Školní knihovna',
    'Praha EDUCAnet Library System': 'Praha Educanet knižní systém',
    'Edu Bookshelf': 'Edu Knihovna',
    'Books': 'Knihy',
    'Home': 'Domů',
    'Profile': 'Profil',
    'My Profile': 'Můj profil',
    'Admin Dashboard': 'Administrátorský panel',
    'Logout': 'Odhlásit se',
    'Login': 'Přihlásit se',
    'Register': 'Registrovat se',
    'Guest Options': 'Možnosti pro hosty',
    'User Settings': 'Uživatelská nastavení',
    'Admin': 'Admin',
    'Lease History': 'Historie výpůjček',
    'Official Site: www.praha.educanet.cz': 'Oficiální stránka: www.praha.educanet.cz',
    '© 2025 EDUCAnet Praha. All rights reserved.': '© 2025 EDUCAnet Praha. Všechna práva vyhrazena.',

    // Book Scanner Admin
    'Book Scanner Admin': 'Skener knih - administrace',
    'Scan book barcodes to fetch information automatically, then add them to your library.':
      'Naskenujte čárové kódy knih pro automatické získání informací a přidání do knihovny.',
    'Start Scanner': 'Spustit skener',
    'Add ISBN Manually': 'Přidat ISBN ručně',
    'Enter Book ISBN': 'Zadejte ISBN knihy',
    'Enter 10 or 13 digit ISBN': 'Zadejte 10 nebo 13místné ISBN',
    'Search': 'Hledat',
    'Example:': 'Příklad:',
    'How to use the Book Scanner': 'Jak používat skener knih',
    'Pro tip:': 'Tip:',
    'For best results, ensure good lighting and hold the camera 6-8 inches from the barcode.':
      'Pro nejlepší výsledky zajistěte dobré osvětlení a držte kameru 15-20 cm od čárového kódu.',
    'Note for mobile users:': 'Poznámka pro uživatele mobilních zařízení:',
    'If the camera doesn\'t start, check that your browser has camera permissions enabled and you\'re using HTTPS.':
      'Pokud se kamera nespustí, zkontrolujte, zda má váš prohlížeč povolená oprávnění ke kameře a používáte HTTPS.',
    'Fetching book information...': 'Načítání informací o knize...',
    'Book found with limited data': 'Kniha nalezena s omezenými údaji',
    'Book information retrieved successfully': 'Informace o knize úspěšně načteny',
    'No book information found for this ISBN': 'Pro toto ISBN nebyly nalezeny žádné informace o knize',
    'No book information found': 'Nenalezeny žádné informace o knize',
    'Failed to fetch book information. Please try again.': 'Nepodařilo se načíst informace o knize. Zkuste to prosím znovu.',
    'Error retrieving book information': 'Chyba při načítání informací o knize',
    'or': 'nebo',

    // BookForm
    'Edit Book Details': 'Upravit detaily knihy',
    'Loading book details...': 'Načítání detailů knihy...',
    'Title': 'Název',
    'Author': 'Autor',
    'Title is required': 'Název je povinný',
    'Author is required': 'Autor je povinný',
    'Description': 'Popis',
    'Category': 'Kategorie',
    'None': 'Žádná',
    'Create New Category': 'Vytvořit novou kategorii',
    'New Category Name': 'Název nové kategorie',
    'Enter new category name': 'Zadejte název nové kategorie',
    'Auto-created': 'Automaticky vytvořeno',
    'Please enter a name for the new category': 'Zadejte prosím název nové kategorie',
    'Published Year': 'Rok vydání',
    'Publisher': 'Vydavatel',
    'Total Copies': 'Celkový počet kopií',
    'Available Copies': 'Dostupné kopie',
    'Total copies must be at least 1': 'Celkový počet kopií musí být alespoň 1',
    'Available copies must be between 0 and total copies': 'Dostupné kopie musí být mezi 0 a celkovým počtem kopií',
    'Cover Image': 'Obal knihy',
    'Use URL': 'Použít URL',
    'Upload File': 'Nahrát soubor',
    'Take Photo': 'Vyfotit',
    'Cover Image URL': 'URL obálky knihy',
    'Enter image URL': 'Zadejte URL obrázku',
    'Select Image File': 'Vybrat obrázek',
    'Selected file': 'Vybraný soubor',
    'Capture Photo': 'Vyfotit',
    'Unable to load image. Please check the URL or try another image.': 'Nelze načíst obrázek. Zkontrolujte URL nebo zkuste jiný obrázek.',
    'Book cover': 'Obálka knihy',
    'Save Book': 'Uložit knihu',
    'Saving...': 'Ukládání...',
    'Book title is required': 'Název knihy je povinný',
    'Failed to upload cover image, but will continue saving book': 'Nepodařilo se nahrát obálku knihy, ale kniha bude uložena',
    'Book saved successfully': 'Kniha byla úspěšně uložena',
    'Failed to save book. Please try again.': 'Nepodařilo se uložit knihu. Zkuste to znovu.',
    'Error saving book': 'Chyba při ukládání knihy',
    'Unknown error': 'Neznámá chyba',
    'Error accessing camera. Please check permissions.': 'Chyba při přístupu ke kameře. Zkontrolujte prosím oprávnění.',

    // Common
    'Save': 'Uložit',
    'Cancel': 'Zrušit',
    'Delete': 'Smazat',
    'Edit': 'Upravit',
    'Loading...': 'Načítání...',
    'Error': 'Chyba',
    'Success': 'Úspěch',
    'Warning': 'Varování',
    'Info': 'Informace',

    // BarcodeScanner
    'Position barcode in the center': 'Umístěte čárový kód do středu',
    'Camera permission denied. Please allow camera access.': 'Přístup ke kameře byl zamítnut. Povolte prosím přístup ke kameře.',
    'Perfect! Barcode detected with high confidence.': 'Perfektní! Čárový kód byl detekován s vysokou jistotou.',
    'Good scan. Hold steady for confirmation.': 'Dobrý sken. Držte stabilně pro potvrzení.',
    'Almost there! Hold the barcode closer.': 'Téměř tam! Přibližte čárový kód blíže.',
    'Barcode detected. Improve lighting or hold steady.': 'Čárový kód detekován. Zlepšete osvětlení nebo držte stabilně.',
    'Move closer to the barcode and center it.': 'Přibližte se k čárovému kódu a vycentrujte jej.',
    'Camera initialization timed out. Please try again.': 'Časový limit pro inicializaci kamery vypršel. Zkuste to prosím znovu.',
    'Scanner container reference is not available. Please try again.': 'Reference kontejneru skeneru není k dispozici. Zkuste to prosím znovu.',
    'Camera permission denied. Please allow camera access in your browser settings.': 'Přístup ke kameře byl zamítnut. Povolte prosím přístup ke kameře v nastavení prohlížeče.',
    'No camera found. Please connect a camera and try again.': 'Nebyla nalezena žádná kamera. Připojte prosím kameru a zkuste to znovu.',
    'Camera is in use by another application. Please close other applications using the camera.': 'Kamera je používána jinou aplikací. Zavřete prosím jiné aplikace používající kameru.',
    'Camera cannot satisfy the requested constraints. Try a different camera.': 'Kamera nemůže splnit požadované parametry. Vyzkoušejte jinou kameru.',
    'Error accessing camera: ': 'Chyba při přístupu ke kameře: ',
    'Barcode found! Processing...': 'Čárový kód nalezen! Zpracovávám...',
    'Barcode detected, hold steady...': 'Čárový kód detekován, držte stabilně...',
    'Barcode identified, confirming...': 'Čárový kód identifikován, potvrzuji...',
    'Starting camera...': 'Spouštím kameru...',
    'Please allow camera access when prompted': 'Povolte prosím přístup ke kameře, až budete vyzváni',
    'Retry Camera': 'Zkusit kameru znovu',
    'Try Different Camera': 'Zkusit jinou kameru',
    'Turn off flash': 'Vypnout blesk',
    'Turn on flash': 'Zapnout blesk',
    'Switch camera': 'Přepnout kameru',
    'Toggle focus mode': 'Přepnout režim zaostření',

    // Footer
    'Book Leasing App': 'Aplikace pro půjčování knih',
    'A responsive application for leasing books with a beautiful UI and animations.': 'Responzivní aplikace pro půjčování knih s krásným UI a animacemi.',
    'Quick Links': 'Rychlé odkazy',
    'Contact': 'Kontakt',
    'Email': 'E-mail',
    'Phone': 'Telefon',
    'Book Leasing. All rights reserved.': 'Book Leasing. Všechna práva vyhrazena.',
    'School Library': 'Školní knihovna',
    'Praha EDUCAnet Library System': 'Praha Educanet knižní systém',
    'Official Site:': 'Oficiální stránka:',
    'Opening Hours': 'Otevírací doba',
    'Monday': 'Pondělí',
    'Tuesday': 'Úterý',
    'Wednesday': 'Středa',
    'Thursday': 'Čtvrtek',
    'Friday': 'Pátek',
    'Closed': 'Zavřeno',
    'EDUCAnet Praha. All rights reserved.': 'EDUCAnet Praha. Všechna práva vyhrazena.',
    // Book suggestion
    'Suggest Book': 'Navrhnout knihu',
    'Suggest a Book': 'Navrhněte knihu',
    'Suggest a book you would like to see in our library. You have': 'Navrhněte knihu, kterou byste chtěli vidět v naší knihovně. Máte',
    'suggestions remaining this week.': 'zbývajících návrhů tento týden.',
    'Book Title': 'Název knihy',
    'ISBN (Optional)': 'ISBN (Nepovinné)',
    'If you know the ISBN, please provide it': 'Pokud znáte ISBN, prosím poskytněte ho',
    'Cancel': 'Zrušit',
    'Submitting...': 'Odesílání...',
    'Submit Suggestion': 'Odeslat návrh',
    'Title and author are required': 'Název a autor jsou povinné',
    'Book suggestion submitted successfully': 'Návrh knihy byl úspěšně odeslán',
    'Failed to submit suggestion': 'Nepodařilo se odeslat návrh',
    'Loading books...': 'Načítání knih...',
    'Book Catalog': 'Katalog knih',
    'Search Books': 'Hledat knihy',
    'Category': 'Kategorie',
    'All Categories': 'Všechny kategorie',
    'Available Only': 'Pouze dostupné',
    'No books found matching your criteria': 'Nebyly nalezeny žádné knihy odpovídající vašim kritériím',
    'Available': 'Dostupné',
    'Unavailable': 'Nedostupné',
    // Suggestions Manager
    'Book Suggestions': 'Návrhy knih',
    'Manage user-submitted book suggestions': 'Správa návrhů knih od uživatelů',
    'No pending suggestions': 'Žádné nevyřízené návrhy',
    'Title': 'Název',
    'Author': 'Autor',
    'ISBN': 'ISBN',
    'User': 'Uživatel',
    'Submitted': 'Datum podání',
    'Actions': 'Akce',
    'Uncategorized': 'Nezařazeno',
    'Approve': 'Schválit',
    'Reject': 'Odmítnout',
    'Assign Category': 'Přiřadit kategorii',
    'Approve Suggestion': 'Schválit návrh',
    'Are you sure you want to approve this suggestion? This will add the book to the library catalog.': 'Opravdu chcete schválit tento návrh? Kniha bude přidána do katalogu knihovny.',
    'Reject Suggestion': 'Odmítnout návrh',
    'Are you sure you want to reject this suggestion? This action cannot be undone.': 'Opravdu chcete odmítnout tento návrh? Tuto akci nelze vrátit zpět.',
    'Assign a category to this book suggestion.': 'Přiřaďte kategorii k tomuto návrhu knihy.',
    'Select Category': 'Vyberte kategorii',
    'Or create a new category:': 'Nebo vytvořte novou kategorii:',
    'New Category': 'Nová kategorie',
    'Processing...': 'Zpracování...',
    'Assign': 'Přiřadit',
    'Suggestion approved successfully': 'Návrh byl úspěšně schválen',
    'Failed to approve suggestion': 'Nepodařilo se schválit návrh',
    'Suggestion rejected': 'Návrh byl odmítnut',
    'Failed to reject suggestion': 'Nepodařilo se odmítnout návrh',
    'Please select or enter a category': 'Vyberte nebo zadejte kategorii',
    'Category assigned successfully': 'Kategorie byla úspěšně přiřazena',
    'Failed to assign category': 'Nepodařilo se přiřadit kategorii',
    'Failed to load suggestions. Please try again.': 'Nepodařilo se načíst návrhy. Zkuste to prosím znovu.',
    // Book cover
    'No cover available': 'Obal není k dispozici',

    // Admin Dashboard
    'Dashboard': 'Přehled',
    'Books': 'Knihy',
    'Leases': 'Výpůjčky',
    'Users': 'Uživatelé',
    'Book Scanner': 'Skener knih',
    'Suggestions': 'Návrhy',
    'Security': 'Zabezpečení',
    'Welcome to Admin Dashboard': 'Vítejte v administraci',
    'Use the tabs above to manage books, leases, users, and security settings.': 'Pomocí karet výše můžete spravovat knihy, výpůjčky, uživatele a nastavení zabezpečení.',
    'Book Management': 'Správa knih',
    'This component will allow administrators to manage the book catalog, including adding, editing, and removing books from the library.': 'Tato komponenta umožňuje správcům spravovat katalog knih, včetně přidávání, úpravy a odstraňování knih z knihovny.',
    'Add New Book': 'Přidat novou knihu',
    'Import Books': 'Importovat knihy',
    'Lease Management': 'Správa výpůjček',
    'Bulk Actions': 'Hromadné akce',
    'Calendar View': 'Kalendářní pohled',

    // UserManagement
    'Username': 'Uživatelské jméno',
    'Email': 'E-mail',
    'Role': 'Role',
    'Status': 'Stav',
    'Verified': 'Ověřeno',
    'Created': 'Vytvořeno',
    'Actions': 'Akce',
    'No users found': 'Žádní uživatelé nenalezeni',
    'Ban User': 'Zablokovat uživatele',
    'Unban User': 'Odblokovat uživatele',
    'Ban Reason': 'Důvod zablokování',
    'Reason for banning this user': 'Důvod pro zablokování tohoto uživatele',
    'Are you sure you want to ban': 'Opravdu chcete zablokovat',
    'Are you sure you want to unban': 'Opravdu chcete odblokovat',
    'Processing...': 'Zpracování...',
    'Change User Role': 'Změnit roli uživatele',
    'Update role for user:': 'Aktualizovat roli pro uživatele:',
    'Updating...': 'Aktualizuji...',
    'Update Role': 'Aktualizovat roli',
    'Delete User Account': 'Smazat uživatelský účet',
    'Warning: This action cannot be undone.': 'Varování: Tuto akci nelze vrátit zpět.',
    'Are you sure you want to permanently delete the account for': 'Opravdu chcete trvale smazat účet pro',
    'Deleting...': 'Mazání...',
    'Delete User': 'Smazat uživatele',
    'Verify User Email': 'Ověřit e-mail uživatele',
    'Email has been verified successfully for': 'E-mail byl úspěšně ověřen pro',
    'Manually verify email for user:': 'Ručně ověřit e-mail pro uživatele:',
    'Email address:': 'E-mailová adresa:',
    'This will mark the user email as verified without requiring them to go through the email verification process.': 'Tímto bude e-mail uživatele označen jako ověřený bez nutnosti procházet procesem ověření e-mailu.',
    'Verifying...': 'Ověřování...',
    'Verify Email': 'Ověřit e-mail',
    'Email verified': 'E-mail ověřen',
    'Email not verified': 'E-mail není ověřený',
    'Change Role': 'Změnit roli',
    'Admin accounts cannot be deleted.': 'Administrátorské účty nelze smazat.',
    'Active': 'Aktivní',
    'Banned': 'Zablokovaný',

    // Book Management
    'Books List': 'Seznam knih',
    'Categories': 'Kategorie',
    'Search by title, author, ISBN...': 'Hledat podle názvu, autora, ISBN...',
    'Scan ISBN': 'Skenovat ISBN',
    'Import': 'Import',
    'Add Book': 'Přidat knihu',
    'Loading books...': 'Načítání knih...',
    'Copies': 'Kopie',
    'Edit Book': 'Upravit knihu',
    'No books found': 'Nebyly nalezeny žádné knihy',
    'Rows per page:': 'Řádků na stránku:',
    'Book Categories': 'Kategorie knih',
    'Add Category': 'Přidat kategorii',
    'Loading categories...': 'Načítání kategorií...',
    'No categories found': 'Nebyly nalezeny žádné kategorie',
    'Category Name': 'Název kategorie',
    'Edit Category': 'Upravit kategorii',
    'Add New Category': 'Přidat novou kategorii',
    'Update': 'Aktualizovat',
    'Add': 'Přidat',
    'Import Books from CSV': 'Importovat knihy z CSV',
    'Upload a CSV file with the following columns:': 'Nahrajte soubor CSV s následujícími sloupci:',
    'Select CSV File': 'Vybrat CSV soubor',
    'Selected file:': 'Vybraný soubor:',
    'Importing...': 'Importování...',
    'Book Information': 'Informace o knize',
    'Scan or Enter ISBN': 'Naskenujte nebo zadejte ISBN',
    'This book already exists in the library': 'Tato kniha již v knihovně existuje',
    'Or enter ISBN manually:': 'Nebo zadejte ISBN ručně:',
    'Enter ISBN': 'Zadejte ISBN',
    'Back': 'Zpět',
    'Save Book': 'Uložit knihu',
    'Close': 'Zavřít',
    '-- None --': '-- Žádná --',
    '+ Add New Category': '+ Přidat novou kategorii',
    'Brief description of the book...': 'Stručný popis knihy...',
    'https://example.com/book-cover.jpg': 'https://example.com/obal-knihy.jpg',
    'Unable to load image from URL': 'Nelze načíst obrázek z URL',
    'Book cover preview': 'Náhled obálky knihy',
    'Library Management': 'Správa knihovny',
    'Book Details': 'Detaily knihy',
    'ISBN-13': 'ISBN-13',
    'ISBN-10': 'ISBN-10',

    // Home Page
    'Discover Your Next Great Read': 'Objevte svou další skvělou knihu',
    'Explore our curated collection of books and lease them with just a few clicks.': 'Prozkoumejte naši vybranou sbírku knih a půjčte si je pouhými několika kliknutími.',
    'Browse Books': 'Procházet knihy',
    'No popular books available yet': 'Zatím nejsou k dispozici žádné populární knihy',
    'Browse Catalog': 'Procházet katalog',
    'What Our Users Say': 'Co říkají naši uživatelé',

    // Security Analytics
    'Security Analytics': 'Bezpečnostní analýza',
    'Refresh': 'Obnovit',
    'Current Security Mode': 'Aktuální bezpečnostní režim',
    'Configure the system\'s security level based on threat detection.': 'Nakonfigurujte úroveň zabezpečení systému na základě detekce hrozeb.',
    'reCAPTCHA is configured with a test key (6LeIxAcTAAAAA...). For production use, replace with your own keys in server/config.js or set environment variables.': 'reCAPTCHA je nakonfigurována s testovacím klíčem (6LeIxAcTAAAAA...). Pro produkční použití nahraďte vlastními klíči v server/config.js nebo nastavte proměnné prostředí.',
    'Normal Mode': 'Normální režim',
    'Mild Security (reCAPTCHA)': 'Mírné zabezpečení (reCAPTCHA)',
    'High Security (Lockdown)': 'Vysoké zabezpečení (Uzamčení)',
    'Threat Analytics': 'Analýza hrozeb',
    'Security Incidents': 'Bezpečnostní incidenty',
    'Activity Monitoring': 'Sledování aktivity',
    'System Activity': 'Aktivita systému',
    'Security Status Summary': 'Souhrn stavu zabezpečení',
    'Suspicious IPs Blocked': 'Blokované podezřelé IP adresy',
    'Rate Limit Exceeded': 'Překročený limit přístupu',
    'Spam Attempts': 'Pokusy o spam',
    'Security Score': 'Skóre zabezpečení',
    'Confirm Security Mode Change': 'Potvrdit změnu bezpečnostního režimu',
    'You are about to switch to Normal security mode. This mode provides standard security protections without additional verification steps for users.': 'Chystáte se přepnout do normálního bezpečnostního režimu. Tento režim poskytuje standardní bezpečnostní ochranu bez dodatečných kroků ověřování pro uživatele.',
    'You are about to switch to Mild security mode. This will require reCAPTCHA verification for suspicious activities and may slow down some operations.': 'Chystáte se přepnout do mírného bezpečnostního režimu. To bude vyžadovat ověření reCAPTCHA pro podezřelé aktivity a může zpomalit některé operace.',
    'WARNING: You are about to enable High security (Lockdown) mode. Only administrators will be able to access the system. All regular user access will be blocked until this mode is disabled.': 'VAROVÁNÍ: Chystáte se povolit režim vysokého zabezpečení (Uzamčení). Pouze administrátoři budou mít přístup do systému. Veškerý běžný uživatelský přístup bude blokován, dokud nebude tento režim deaktivován.',
    'Normal': 'Normální',
    'Unknown': 'Neznámý',
    'Suspicious IPs': 'Podezřelé IP adresy',
    'Rate Limit Exceeds': 'Překročení limitu přístupu',
    'Invalid Tokens': 'Neplatné tokeny',
    'Threat Incidents': 'Bezpečnostní incidenty',
    'Login Attempts': 'Pokusy o přihlášení',
    'Failed Logins': 'Neúspěšná přihlášení',
    'API Requests': 'API požadavky',
    'Failed to change security mode': 'Nepodařilo se změnit bezpečnostní režim',

    // Profile
    'My Profile': 'Můj profil',
    'Edit Profile': 'Upravit profil',
    'Save Changes': 'Uložit změny',
    'Username': 'Uživatelské jméno',
    'Email': 'Email',
    'Profile updated successfully': 'Profil byl úspěšně aktualizován',
    'Failed to update profile. Please try again.': 'Nepodařilo se aktualizovat profil. Prosím zkuste to znovu.',
    'Lease date': 'Datum výpůjčky',
    'Due date': 'Datum splatnosti',
    'Return date': 'Datum vrácení',
    'Book Title': 'Název knihy',
    'Current Leases': 'Aktuální výpůjčky',
    'Past Leases': 'Minulé výpůjčky',
    'No leases found': 'Nenalezeny žádné výpůjčky',
    'Status': 'Stav',
    'Actions': 'Akce',
    'Overdue': 'Po splatnosti',
    'Active': 'Aktivní',
    'Returned': 'Vráceno',
    'Return': 'Vrátit',
    'Renew': 'Prodloužit',
    'View Details': 'Zobrazit detaily',

    // Lease History
    'My Book Leases': 'Moje výpůjčky knih',
    'View your upcoming leases in the calendar and browse your lease history.': 'Prohlédněte si nadcházející výpůjčky v kalendáři a procházejte historii výpůjček.',
    'Lease Calendar': 'Kalendář výpůjček',
    'Lease Archive': 'Archiv výpůjček',
    'Lease History Archive': 'Archiv historie výpůjček',
    'You have no past lease records. Completed, cancelled, or overdue leases will appear here.': 'Nemáte žádné záznamy o předchozích výpůjčkách. Dokončené, zrušené nebo opožděné výpůjčky se zobrazí zde.',
    'Failed to load lease history. Please try again later.': 'Nepodařilo se načíst historii výpůjček. Zkuste to prosím později.',
    'View Lease History': 'Zobrazit historii výpůjček',
    'View your current and past leases in the lease history page.': 'Zobrazte své současné a minulé výpůjčky na stránce historie výpůjček.',
    'Leased': 'Vypůjčeno',
    'Due': 'Splatnost',
    'Returned': 'Vráceno',
    'Cancelled': 'Zrušeno',
    'View Book': 'Zobrazit knihu',
    'ACTIVE': 'AKTIVNÍ',
    'RETURNED': 'VRÁCENO',
    'OVERDUE': 'PO SPLATNOSTI',
    'PENDING': 'ČEKAJÍCÍ',
    'CANCELLED': 'ZRUŠENO',
    'COMPLETED': 'DOKONČENO',

    // Calendar
    'Expiring Leases': 'Končící výpůjčky',
    'Filter by Book': 'Filtrovat podle knihy',
    'Filter by User': 'Filtrovat podle uživatele',
    'All Books': 'Všechny knihy',
    'All Users': 'Všichni uživatelé',
    'Refresh data': 'Obnovit data',
    'Failed to load calendar data. Please try again later.': 'Nepodařilo se načíst data kalendáře. Zkuste to prosím později.',
    'Status': 'Stav',
    'Approval': 'Schválení',
    'Leases Expiring Soon': 'Výpůjčky končící brzy',
    'No leases expiring in the next 7 days.': 'Žádné výpůjčky nekončí v následujících 7 dnech.',
    'User': 'Uživatel',
    'Days Left': 'Zbývá dnů',
    'Details': 'Detaily',
    'Unknown Book': 'Neznámá kniha',

    // Barcode Scanner
    'Barcode Scanner': 'Skaner čárových kódů',
    'Focused': 'Zaostřeno',
    'Focusing': 'Zaostřování',
    'Failed to access camera': 'Nepodařilo se získat přístup ke kameře',
    'Try Again': 'Zkusit znovu',
    'Starting camera...': 'Spouštění kamery...',
    'Auto-focusing...': 'Automatické zaostřování...',
    'Ready to scan!': 'Připraven ke skenování!',
    'Position barcode in frame': 'Umístěte čárový kód do rámce',
    'Tap anywhere to refocus • Supports all barcode formats': 'Klepněte kamkoli pro přeostření • Podporuje všechny formáty kódů',
    'Tap screen to focus • Works with ISBN, UPC, EAN codes': 'Klepněte na obrazovku pro zaostření • Funguje s kódy ISBN, UPC, EAN',
    'Barcode detected!': 'Čárový kód detekován!',
    'Processing book information...': 'Zpracování informací o knize...',
    'Universal Barcode Scanner': 'Univerzální skaner čárových kódů',
    'Ready to scan any barcode containing ISBN': 'Připraven ke skenování kódů obsahujících ISBN',
    'Processing...': 'Zpracování...',
    'Please wait while we process your barcode': 'Počkejte prosím, zpracováváme váš čárový kód',
    'Manual Input': 'Ruční zadání',
    'Enter ISBN manually': 'Zadejte ISBN ručně',
    'Enter ISBN': 'Zadejte ISBN',
    'Submit': 'Odeslat',

    // Profile - Account Details
    'Account Information': 'Informace o účtu',
    'Account Type:': 'Typ účtu:',
    'Administrator': 'Administrátor',
    'User': 'Uživatel',
    'Active Leases:': 'Aktivní výpůjčky:',
    'Total Leases:': 'Celkem výpůjček:',
    'You don\'t have any active leases.': 'Nemáte žádné aktivní výpůjčky.',

    // AuthPopup new strings
    'Connecting to Google...': 'Connecting to Google...',
    'Please complete the sign-in process in the Google window.': 'Please complete the sign-in process in the Google window.',
    'Authentication Successful!': 'Authentication Successful!',
    'Please wait while we log you in...': 'Please wait while we log you in...',
    'This window should close automatically. If not, you may close it.': 'This window should close automatically. If not, you may close it.',
    'Authentication Failed': 'Authentication Failed',
    'Try Again': 'Try Again',
    'Preparing Google Sign-In...': 'Preparing Google Sign-In...',
    'Authentication failed. Please try again or close this window.': 'Authentication failed. Please try again or close this window.',
    'Authentication cancelled. You can close this window.': 'Authentication cancelled. You can close this window.',
    'Popup issue detected. Please ensure popups are allowed and try again.': 'Popup issue detected. Please ensure popups are allowed and try again.',
    'Could not communicate back to the main application. Please close this window and try again.': 'Could not communicate back to the main application. Please close this window and try again.',

    // Profile Completion Modal
    'Complete Your Profile': 'Dokončete svůj profil',
    'To rent books and make suggestions, please provide your class teacher and grade information.': 'Pro půjčování knih a podávání návrhů prosím poskytněte informace o třídním učiteli a třídě.',
    'Class Teacher': 'Třídní učitel',
    'Enter your class teacher\'s name': 'Zadejte jméno svého třídního učitele',
    'Example: Mrs. Smith, Mr. Johnson': 'Příklad: paní Nováková, pan Svoboda',
    'Grade/Class': 'Třída/Ročník',
    'Enter your grade or class': 'Zadejte svou třídu nebo ročník',
    'Example: 9A, Grade 10, Class 11B': 'Příklad: 9A, 10. třída, třída 11B',
    'Both class teacher and grade are required.': 'Třídní učitel i třída jsou povinné.',
    'Failed to update profile. Please try again.': 'Nepodařilo se aktualizovat profil. Zkuste to prosím znovu.',
    'Saving...': 'Ukládání...',
    'Save Profile': 'Uložit profil',

    // User Profile Page
    'Profile Information': 'Informace o profilu',
    'Class Teacher:': 'Třídní učitel:',
    'Grade/Class:': 'Třída/Ročník:',
    'Not specified': 'Neuvedeno',
    'Profile incomplete. Please complete your profile to rent books and make suggestions.': 'Profil není dokončen. Dokončete prosím svůj profil pro půjčování knih a podávání návrhů.',
    'Complete Profile': 'Dokončit profil',
    'Profile updated successfully!': 'Profil byl úspěšně aktualizován!',

    // Admin User Management
    'Edit Profile': 'Upravit profil',
    'Edit User Profile': 'Upravit profil uživatele',
    'Update profile information for user:': 'Aktualizovat informace profilu pro uživatele:',
    'Enter class teacher name': 'Zadejte jméno třídního učitele',
    'Enter grade or class': 'Zadejte třídu nebo ročník',
    'Updating...': 'Aktualizuji...',
    'Update Profile': 'Aktualizovat profil',

    // Error Messages
    'Please complete your profile by providing your class teacher and grade information.': 'Dokončete prosím svůj profil poskytnutím informací o třídním učiteli a třídě.',
    'Please log in to continue.': 'Pro pokračování se prosím přihlaste.',
    'User information not available.': 'Informace o uživateli nejsou k dispozici.',
    'Please verify your email address first.': 'Nejprve prosím ověřte svou e-mailovou adresu.',
    'Failed to fetch profile status': 'Nepodařilo se načíst stav profilu',

    // User Profile Additional
    'Account Type:': 'Typ účtu:',
    'Active Leases:': 'Aktivní výpůjčky:',
    'Total Leases:': 'Celkem výpůjček:',
    'Active Leases': 'Aktivní výpůjčky',
    'Past Leases': 'Minulé výpůjčky',
    'Untitled Book': 'Kniha bez názvu',
    'Unknown Author': 'Neznámý autor',
    'by': 'od',
    'Leased on:': 'Vypůjčeno dne:',
    'Due date:': 'Datum splatnosti:',
    'Returned on:': 'Vráceno dne:',
    'Cancelled on:': 'Zrušeno dne:',
    'Pending': 'Čekající',
    'Overdue': 'Po splatnosti',
    'Active': 'Aktivní',
    'Cancellable': 'Lze zrušit',
    'Return Book': 'Vrátit knihu',
    'Cancel Lease': 'Zrušit výpůjčku',
    'You don\'t have any active leases.': 'Nemáte žádné aktivní výpůjčky.',
    'View Lease Calendar': 'Zobrazit kalendář výpůjček',
    'You don\'t have any past leases yet.': 'Zatím nemáte žádné minulé výpůjčky.',
    'Are you sure you want to cancel your lease for': 'Opravdu chcete zrušit výpůjčku pro',
    'This action cannot be undone. You can only cancel leases within 3 hours of creating them.': 'Tuto akci nelze vrátit zpět. Výpůjčky můžete zrušit pouze do 3 hodin od jejich vytvoření.',
    'No, Keep Lease': 'Ne, ponechat výpůjčku',
    'Yes, Cancel Lease': 'Ano, zrušit výpůjčku',

    // Additional missing translations
    'Submit': 'Odeslat',
    'Description (optional)': 'Popis (nepovinný)',
    'Suggest a book you would like to see in our library.': 'Navrhněte knihu, kterou byste chtěli vidět v naší knihovně.',
    'You have reached your weekly suggestion limit. You can make more suggestions next week.': 'Dosáhli jste týdenního limitu návrhů. Další návrhy můžete podat příští týden.',

    // Lease Details Dialog
    'Requested': 'Požádáno',
    'Approved': 'Schváleno',
    'Returned': 'Vráceno',
    'Lease Information': 'Informace o výpůjčce',
    'Book Information': 'Informace o knize',
    'Lease Timeline': 'Časová osa výpůjčky',
    'Close': 'Zavřít',

    // Admin components
    'No book suggestions found.': 'Nebyly nalezeny žádné návrhy knih.',
    'Date': 'Datum',
    'Suggested By': 'Navrhl',
    'Request Date': 'Datum žádosti',
    'Due Date': 'Datum splatnosti',
    'Approval/Rejection Date': 'Datum schválení/odmítnutí',
    'Return Date': 'Datum vrácení',
    'Rejection Reason': 'Důvod odmítnutí',
    'Lease Details': 'Detaily výpůjčky',
    'Title': 'Název',
    'Author': 'Autor',

    // SuggestionManagement
    'Book Suggestions': 'Návrhy knih',
    'Refresh suggestions': 'Obnovit návrhy',
    'Filter': 'Filtr',
    'All Suggestions': 'Všechny návrhy',
    'Pending': 'Čekající',
    'Approved': 'Schválené',
    'Rejected': 'Odmítnuté',
    'ISBN': 'ISBN',
    'Actions': 'Akce',
    'Book Suggestion Details': 'Detaily návrhu knihy',
    'by': 'od',
    'Suggested by:': 'Navrhl:',
    'Date suggested:': 'Datum návrhu:',
    'No description available.': 'Popis není k dispozici.',
    'Rejection Reason:': 'Důvod odmítnutí:',
    'Reject': 'Odmítnout',
    'Approve': 'Schválit',
    'Are you sure you want to permanently delete this suggestion?': 'Opravdu chcete trvale smazat tento návrh?',
    'Book suggestion approved successfully!': 'Návrh knihy byl úspěšně schválen!',
    'Failed to approve suggestion. Please try again.': 'Nepodařilo se schválit návrh. Zkuste to prosím znovu.',
    'Book suggestion rejected.': 'Návrh knihy byl odmítnut.',
    'Failed to reject suggestion. Please try again.': 'Nepodařilo se odmítnout návrh. Zkuste to prosím znovu.',
    'Book suggestion deleted successfully.': 'Návrh knihy byl úspěšně smazán.',
    'Failed to delete suggestion. Please try again.': 'Nepodařilo se smazat návrh. Zkuste to prosím znovu.',
    'Failed to load suggestions': 'Nepodařilo se načíst návrhy',

    // Login/Auth pages
    'Login': 'Přihlášení',
    'Email Address': 'E-mailová adresa',
    'Password': 'Heslo',
    'Sign In': 'Přihlásit se',
    'Sign in with Google': 'Přihlásit se pomocí Google',
    'Don\'t have an account? Register': 'Nemáte účet? Registrujte se',
    'Invalid credentials or server error. Please try again.': 'Neplatné přihlašovací údaje nebo chyba serveru. Zkuste to prosím znovu.',
    'Email and password are required': 'E-mail a heslo jsou povinné',
    'Don\'t have an account?': 'Nemáte účet?',
    'Sign Up': 'Registrovat se',
    'OR': 'NEBO',
    'Register': 'Registrace',
    'Create an Account': 'Vytvořit účet',
    'Username': 'Uživatelské jméno',
    'Confirm Password': 'Potvrdit heslo',
    'Passwords do not match': 'Hesla se neshodují',

    // Error messages
    'Email is required': 'E-mail je povinný',
    'Email is invalid': 'E-mail je neplatný',
    'Password is required': 'Heslo je povinné',
    'Invalid email or password': 'Neplatný e-mail nebo heslo',
    'Your account has been banned. Please contact support.': 'Váš účet byl zablokován. Kontaktujte prosím podporu.',
    'System is in lockdown mode. Only administrators can access.': 'Systém je v režimu uzamčení. Přístup mají pouze administrátoři.',
    'Server error. Please try again later.': 'Chyba serveru. Zkuste to prosím později.',
    'An account with this email or username already exists.': 'Účet s tímto e-mailem nebo uživatelským jménem již existuje.',
    'Please fill in all required fields.': 'Vyplňte prosím všechna povinná pole.',
    'Please enter a valid email address.': 'Zadejte prosím platnou e-mailovou adresu.',
    'Invalid registration data. Please check your information.': 'Neplatné registrační údaje. Zkontrolujte prosím své informace.',

    // Form helpers
    'Choose a unique username': 'Vyberte si jedinečné uživatelské jméno',
    'Enter a valid email address': 'Zadejte platnou e-mailovou adresu',
    'Minimum 6 characters': 'Minimálně 6 znaků',
    'Must match password': 'Musí se shodovat s heslem',
    'Already have an account?': 'Již máte účet?',
    'Registration failed. Please check your details or try again later.': 'Registrace se nezdařila. Zkontrolujte své údaje nebo to zkuste později.',
    'Already have an account? Sign In': 'Již máte účet? Přihlaste se',
    'Registration failed. Please try again.': 'Registrace se nezdařila. Zkuste to prosím znovu.',

    // Email Verification
    'Email Verification Required': 'Vyžadováno ověření e-mailu',
    'Your email has been successfully verified! Redirecting to your profile...': 'Váš e-mail byl úspěšně ověřen! Přesměrování na váš profil...',
    'Your email address': 'Vaše e-mailová adresa',
    'needs to be verified before you can access this feature.': 'musí být ověřena před použitím této funkce.',
    'We\'ve sent a verification link to your email address. Please check your inbox (and spam folder) and click the verification link to complete the verification process.': 'Poslali jsme ověřovací odkaz na vaši e-mailovou adresu. Zkontrolujte prosím svou schránku (a složku spam) a klikněte na ověřovací odkaz pro dokončení procesu ověření.',

    // Register page additional
    'Create an Account': 'Vytvořit účet',
    'Registration Complete': 'Registrace dokončena',
    'Your account has been created successfully!': 'Váš účet byl úspěšně vytvořen!',
    'A verification email has been sent to': 'Ověřovací e-mail byl odeslán na',
    'Please check your inbox (and spam folder) and click the verification link to complete your registration.': 'Zkontrolujte prosím svou schránku (a složku spam) a klikněte na ověřovací odkaz pro dokončení registrace.',
    'You can still use the application, but some features may be limited until you verify your email address.': 'Aplikaci můžete stále používat, ale některé funkce mohou být omezené, dokud neověříte svou e-mailovou adresu.',
    'You can now log in to your account.': 'Nyní se můžete přihlásit ke svému účtu.',
    'Go to Login': 'Přejít na přihlášení',
    'Already have an account?': 'Již máte účet?',
    'Username is required': 'Uživatelské jméno je povinné',
    'Username must be at least 3 characters': 'Uživatelské jméno musí mít alespoň 3 znaky',
    'Email is required': 'E-mail je povinný',
    'Email is invalid': 'E-mail je neplatný',
    'Password is required': 'Heslo je povinné',
    'Password must be at least 6 characters': 'Heslo musí mít alespoň 6 znaků',

    // Database Management
    'Database': 'Databáze',
    'Database Management': 'Správa databáze',
    'Tables Overview': 'Přehled tabulek',
    'Table Data': 'Data tabulky',
    'Schema Management': 'Správa schématu',
    'Manage': 'Spravovat',
    'Protected': 'Chráněno',
    'Add Column': 'Přidat sloupec',
    'Add Row': 'Přidat řádek',
    'Column Name': 'Název sloupce',
    'Data Type': 'Datový typ',
    'Nullable': 'Může být prázdný',
    'Default Value (optional)': 'Výchozí hodnota (nepovinná)',
    'Add New Column': 'Přidat nový sloupec',
    'Add New Row': 'Přidat nový řádek',
    'Edit Row': 'Upravit řádek',
    'Confirm Delete': 'Potvrdit smazání',
    'Are you sure you want to delete this row? This action cannot be undone.': 'Opravdu chcete smazat tento řádek? Tuto akci nelze vrátit zpět.',
    'Update Row': 'Aktualizovat řádek',
    'Primary Key': 'Primární klíč',
    'Cannot delete protected column': 'Nelze smazat chráněný sloupec',
    'Cannot modify protected table': 'Nelze upravit chráněnou tabulku',
    'Column added successfully': 'Sloupec byl úspěšně přidán',
    'Column deleted successfully': 'Sloupec byl úspěšně smazán',
    'Row added successfully': 'Řádek byl úspěšně přidán',
    'Row updated successfully': 'Řádek byl úspěšně aktualizován',
    'Row deleted successfully': 'Řádek byl úspěšně smazán',
    'SQL Query': 'SQL Dotaz',
    'SQL Query Console': 'SQL Konzole',
    'Execute Query': 'Spustit dotaz',
    'Clear': 'Vymazat',
    'Query Result': 'Výsledek dotazu',
    'No results returned.': 'Žádné výsledky.',
    'Changes:': 'Změny:',
    'Last Insert ID:': 'Poslední vložené ID:',
    'Please enter a SQL query': 'Prosím zadejte SQL dotaz',
    'Query executed successfully': 'Dotaz byl úspěšně proveden',
    'Query failed:': 'Dotaz selhal:',
    'Advanced Feature:': 'Pokročilá funkce:',
    'Execute custom SQL queries. Only SELECT, INSERT, UPDATE, DELETE operations are allowed.': 'Spouštějte vlastní SQL dotazy. Povoleny jsou pouze operace SELECT, INSERT, UPDATE, DELETE.',

    // Security Analytics
    '24-Hour Activity Monitor': '24hodinový monitor aktivity',
    'Time': 'Čas',
    'Time (24h)': 'Čas (24h)',
    'Login Activity': 'Aktivita přihlášení',
    'API Requests Today': 'API požadavky dnes',
    'Logins Today': 'Přihlášení dnes',
    'Failed Logins Today': 'Neúspěšná přihlášení dnes',
    'Suspicious IPs': 'Podezřelé IP adresy',

    // Books Page and Book Details - Missing translations
    'Search by title or author': 'Hledat podle názvu nebo autora',
    'All': 'Vše',
    'No books found matching your criteria.': 'Nebyly nalezeny žádné knihy odpovídající vašim kritériím.',
    'Book not found': 'Kniha nenalezena',
    'Back to Book Catalog': 'Zpět do katalogu knih',
    'By': 'Od',
    'copies': 'kopií',
    'Borrow This Book': 'Půjčit si tuto knihu',
    'Back to Catalog': 'Zpět do katalogu',
    'You need to sign in to borrow books.': 'Pro půjčování knih se musíte přihlásit.',
    'Sign in to borrow books or make suggestions.': 'Přihlaste se pro půjčování knih nebo navrhování nových.',
    'Sign in to borrow or suggest books': 'Přihlaste se pro půjčování nebo navrhování knih',
    'Sign In': 'Přihlásit se',
    'Book not found.': 'Kniha nenalezena.',
    'by': 'od',
    'N/A': 'Neuvedeno',
    'Lease This Book': 'Půjčit si tuto knihu',
    'Currently Unavailable': 'Momentálně nedostupné',
    'This book is currently leased by another user.': 'Tato kniha je momentálně půjčena jinému uživateli.',
    'Login to lease this book.': 'Přihlaste se pro půjčení této knihy.',
    'Borrow': 'Půjčit',
    'Your borrowing request has been submitted successfully! We will notify you when it is approved.': 'Vaše žádost o půjčení byla úspěšně odeslána! Budeme vás informovat, až bude schválena.',
    'Please confirm that you would like to borrow this book and specify for how long:': 'Potvrďte prosím, že si chcete půjčit tuto knihu a uveďte na jak dlouho:',

    // Barcode Scanner - Missing translations
    'Scanner stopped - Book details loaded': 'Skener zastaven - Detaily knihy načteny',
    'Barcode detected! Looking up ISBN...': 'Čárový kód detekován! Vyhledávám ISBN...',
    'Searching for book...': 'Hledám knihu...',
    '✓ Barcode Detected!': '✓ Čárový kód detekován!',
    'Manual Entry': 'Ruční zadání',
    'Enter ISBN (optional)': 'Zadejte ISBN (nepovinné)',
    'Enter an ISBN to auto-fill book details, or leave blank to enter manually.': 'Zadejte ISBN pro automatické vyplnění detailů knihy, nebo nechte prázdné pro ruční zadání.',
    'Search ISBN': 'Hledat ISBN',
    'Create Empty': 'Vytvořit prázdné',
    'Book not found. Please verify the barcode was scanned correctly or enter details manually.': 'Kniha nenalezena. Ověřte prosím, že byl čárový kód správně naskenován, nebo zadejte údaje ručně.',
    'Error during book lookup. Please try again or verify the barcode was scanned correctly.': 'Chyba při vyhledávání knihy. Zkuste to prosím znovu nebo ověřte, že byl čárový kód správně naskenován.',
    'Error checking local library. Please try again or verify the barcode was scanned correctly.': 'Chyba při kontrole místní knihovny. Zkuste to prosím znovu nebo ověřte, že byl čárový kód správně naskenován.',

    // Text Recognition
    'Recognizing': 'Rozpoznávání',
    'Point camera at text': 'Namiřte kameru na text',
    'Scan Now': 'Skenovat nyní',
    'Initializing text recognition...': 'Inicializace rozpoznávání textu...',
    'Error': 'Chyba',
    'Failed to start text recognition': 'Nepodařilo se spustit rozpoznávání textu',
    'Camera access denied. Please grant permission.': 'Přístup ke kameře byl odepřen. Prosím udělte oprávnění.',
    'No camera found on this device.': 'Na tomto zařízení nebyla nalezena žádná kamera.',
    'Align numbers within this box': 'Zarovnejte čísla v tomto rámečku',

    // Book Cover
    'No Cover Available': 'Obal není k dispozici',

    // Additional missing translations
    'Remaining Suggestions': 'Zbývající návrhy',
    'Book Information': 'Informace o knize',
    'Add New Book': 'Přidat novou knihu',
    'Book found! Please review and save.': 'Kniha nalezena! Prosím zkontrolujte a uložte.',
    'Book information found! Please review and save.': 'Informace o knize nalezeny! Prosím zkontrolujte a uložte.',

    // Navbar and Navigation
    'Account settings': 'Nastavení účtu',
    'Email not verified': 'E-mail není ověřený',
    'Email verified': 'E-mail ověřen',
    'Leases': 'Výpůjčky',

    // Admin Dashboard
    'Book Management': 'Správa knih',
    'Category Management': 'Správa kategorií',
    'User Management': 'Správa uživatelů',
    'Lease Management': 'Správa výpůjček',

    // Camera Error Messages
    'Camera access denied. Please allow camera permissions and try again.': 'Přístup ke kameře byl odepřen. Povolte prosím oprávnění kamery a zkuste to znovu.',
    'Camera initialization failed. Please try refreshing the page.': 'Inicializace kamery selhala. Zkuste prosím obnovit stránku.',
    'No camera found. Please ensure your device has a camera.': 'Nebyla nalezena žádná kamera. Ujistěte se prosím, že vaše zařízení má kameru.',
    'Camera permission denied. Please allow camera access in your browser settings.': 'Oprávnění kamery bylo odepřeno. Povolte prosím přístup ke kameře v nastavení prohlížeče.',
    'Camera is being used by another application. Please close other camera apps and try again.': 'Kamera je používána jinou aplikací. Zavřete prosím ostatní aplikace kamery a zkuste to znovu.',
    'Camera settings not supported. Trying with basic settings...': 'Nastavení kamery není podporováno. Zkouším se základním nastavením...',

    // reCAPTCHA Messages
    'Enhanced security is enabled. Additional verification required.': 'Zvýšené zabezpečení je aktivní. Vyžaduje se dodatečné ověření.',
    'Security verification unavailable. Please try again later.': 'Bezpečnostní ověření není dostupné. Zkuste to prosím později.',
    'reCAPTCHA is loading. Please wait and try again.': 'reCAPTCHA se načítá. Počkejte prosím a zkuste to znovu.',
    'reCAPTCHA verification failed. Please try again.': 'Ověření reCAPTCHA selhalo. Zkuste to prosím znovu.',
    'Security verification failed. Please try again.': 'Bezpečnostní ověření selhalo. Zkuste to prosím znovu.',
  }
};

// Create the context
export const LanguageContext = createContext();

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }

  // Return both translate and t (alias) for compatibility
  return {
    ...context,
    t: context.translate
  };
};

// Provider component
export const LanguageProvider = ({ children }) => {
  // Get stored language or default to English
  const [language, setLanguage] = useState(() => {
    const storedLanguage = localStorage.getItem('language');
    return storedLanguage || 'en';
  });

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  // Toggle between English and Czech
  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'cs' : 'en'));
  };

  // Translate function
  const translate = (text) => {
    return translations[language][text] || text;
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, translate }}>
      {children}
    </LanguageContext.Provider>
  );
};