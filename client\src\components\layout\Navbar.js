import React, { useState, useEffect, useContext } from 'react';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Tooltip,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  BottomNavigation,
  BottomNavigationAction,
  useMediaQuery,
  Badge,
  CircularProgress,
  Button,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Book as BookIcon,
  Close as CloseIcon,
  Home as HomeIcon,
  MenuBook as MenuBookIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  CalendarToday as CalendarTodayIcon,
  Translate as TranslateIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  VerifiedUser as VerifiedUserIcon,
  ErrorOutline as ErrorOutlineIcon
} from '@mui/icons-material';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import SearchIcon from '@mui/icons-material/Search';
import AccountCircle from '@mui/icons-material/AccountCircle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import LogoutIcon from '@mui/icons-material/Logout';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { AuthContext } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { motion } from 'framer-motion';
import { useTheme } from '@mui/material/styles';
import axios from 'axios';

const Navbar = () => {
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const { user, logout, loading, refreshUserData } = useContext(AuthContext);
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const { language, toggleLanguage, translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [mobileNavValue, setMobileNavValue] = useState(0);
  const [notificationCount, setNotificationCount] = useState(0);

  // Flag icons for language toggle
  const flags = {
    en: "🇬🇧",
    cs: "🇨🇿"
  };


  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    handleCloseUserMenu();
    navigate('/');
  };

  // Navigation items
  const navItems = [
    { name: translate('Home'), path: '/' },
    { name: translate('Books'), path: '/books' },
  ];

  // Effect to refresh user data periodically to check verification status
  useEffect(() => {
    if (user && user.email_verified === 0) {
      // If user is not verified, refresh their data every minute
      const intervalId = setInterval(() => {
        refreshUserData();
      }, 60000); // Check every minute

      return () => clearInterval(intervalId);
    }
  }, [user, refreshUserData]);

  // User menu items
  const userMenuItems = user
    ? [
        ...(user.email_verified === 0 ? [{
          name: translate('Verify Email'),
          action: () => navigate('/verify-email'),
          icon: <ErrorOutlineIcon fontSize="small" color="warning" />
        }] : []),
        { name: translate('Profile'), action: () => navigate('/profile') },
        { name: translate('Lease History'), action: () => navigate('/lease-history') },
        { name: translate('Logout'), action: handleLogout },
      ]
    : [
        { name: translate('Login'), action: () => navigate('/login') },
        { name: translate('Register'), action: () => navigate('/register') },
      ];

  // Add admin dashboard link if user is admin
  if (user?.role === 'admin') {
    userMenuItems.unshift({ name: translate('Admin Dashboard'), action: () => navigate('/admin') });
  }

  // Mobile drawer configuration - REMOVED on mobile, bottom navigation is sufficient
  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center', display: { xs: 'none', sm: 'block' } }}>
      <Typography variant="h6" sx={{ my: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <BookIcon sx={{ mr: 1 }} />
        {translate('Edu Bookshelf')}
        <IconButton
          sx={{ position: 'absolute', right: 8, top: 8 }}
          color="inherit"
        >
          <CloseIcon />
        </IconButton>
      </Typography>
      <Divider />
      <List>
        {navItems.map((item) => (
          <ListItem key={item.name} disablePadding>
            <ListItemButton component={RouterLink} to={item.path} sx={{ textAlign: 'center' }}>
              <ListItemText primary={item.name} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  // Set mobile navigation value based on current path
  React.useEffect(() => {
    const path = location.pathname;
    if (path === '/') setMobileNavValue(0);
    else if (path === '/books') setMobileNavValue(1);
    else if (path === '/lease-history') setMobileNavValue(2);
    else if (path === '/profile') setMobileNavValue(3);
    else if (path === '/admin') setMobileNavValue(4);
  }, [location.pathname]);

  return (
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <AppBar position="sticky" sx={{
          // ... styling ...
        }}>
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            {/* Logo for desktop */}
            <Typography
              variant="h6"
              noWrap
              component={RouterLink}
              to="/"
              sx={{
                mr: 2,
                display: { xs: 'none', sm: 'flex' },
                fontWeight: 700,
                color: 'inherit',
                textDecoration: 'none',
                alignItems: 'center'
              }}
            >
              <BookIcon sx={{ mr: 1 }} />
              {translate('Edu Bookshelf')}
            </Typography>

            {/* Desktop navigation buttons */}
            <Box sx={{ display: { xs: 'none', sm: 'flex' }, ml: 2 }}>
              <Button
                component={RouterLink}
                to="/"
                sx={{
                  color: 'white',
                  mx: 1,
                  '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
                  display: 'flex',
                  alignItems: 'center'
                }}
                startIcon={<HomeIcon />}
              >
                {translate('Home')}
              </Button>
              <Button
                component={RouterLink}
                to="/books"
                sx={{
                  color: 'white',
                  mx: 1,
                  '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
                  display: 'flex',
                  alignItems: 'center'
                }}
                startIcon={<MenuBookIcon />}
              >
                {translate('Books')}
              </Button>
            </Box>

            {/* Mobile Logo - Centered */}
            <Typography
              variant="h6"
              noWrap
              component={RouterLink}
              to="/"
              sx={{
                display: { xs: 'flex', sm: 'none' },
                justifyContent: 'center',
                alignItems: 'center',
                fontWeight: 700,
                letterSpacing: '.1rem',
                color: 'inherit',
                textDecoration: 'none',
                flexGrow: 1
              }}
            >
              <BookIcon sx={{ mr: 1 }} />
              {translate('Edu Bookshelf')}
            </Typography>

            {/* Spacer to push profile button to the right */}
            <Box sx={{ flexGrow: { xs: 0, sm: 1 } }} />

            {/* Language Toggle Button */}
            <Tooltip title={`${language === 'en' ? 'Switch to Czech' : 'Přepnout do angličtiny'}`}>
              <Chip
                icon={<TranslateIcon />}
                label={`${flags[language]} ${language.toUpperCase()}`}
                onClick={toggleLanguage}
                color="secondary"
                variant="outlined"
                size={isMobile ? "small" : "medium"}
                sx={{
                  mr: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(136, 136, 136, 0.2)',
                  }
                }}
              />
            </Tooltip>


            {/* User Menu Icon */}
            <Box sx={{ ml: 2 }}>
              <Tooltip title={user ? translate('Account settings') : translate('Login')}>
                <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : user ? (
                    <Badge
                      overlap="circular"
                      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      badgeContent={
                        user.email_verified === 0 ? (
                          <Tooltip title={translate('Email not verified')}>
                            <ErrorOutlineIcon fontSize="small" color="warning" />
                          </Tooltip>
                        ) : (
                          <Tooltip title={translate('Email verified')}>
                            <VerifiedUserIcon fontSize="small" color="success" />
                          </Tooltip>
                        )
                      }
                    >
                      {user.profile_image ? (
                        <Avatar alt={user.username} src={user.profile_image} />
                      ) : (
                        <Avatar alt={user.username}>
                          {user.username?.charAt(0)?.toUpperCase() || <AccountCircle />}
                        </Avatar>
                      )}
                    </Badge>
                  ) : (
                    <AccountCircle />
                  )}
                </IconButton>
              </Tooltip>
              <Menu
                sx={{ mt: '45px' }}
                id="menu-appbar"
                anchorEl={anchorElUser}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
              >
                {userMenuItems.map((item) => (
                  <MenuItem key={item.name} onClick={() => {
                    item.action();
                    handleCloseUserMenu();
                  }}>
                    {item.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>{item.icon}</Box>}
                    <Typography textAlign="center">{item.name}</Typography>
                  </MenuItem>
                ))}
              </Menu>
            </Box>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Navigation Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },
        }}
      >
        {drawer}
      </Drawer>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <Box sx={{ position: 'fixed', bottom: 0, left: 0, right: 0, zIndex: 1100, boxShadow: 3 }}>
          <BottomNavigation
            value={mobileNavValue}
            onChange={(event, newValue) => {
              setMobileNavValue(newValue);
              switch(newValue) {
                case 0:
                  navigate('/');
                  break;
                case 1:
                  navigate('/books');
                  break;
                case 2:
                  if (user) {
                    navigate('/lease-history');
                  } else {
                    navigate('/login');
                  }
                  break;
                case 3:
                  if (user) {
                    navigate('/profile');
                  } else {
                    navigate('/login');
                  }
                  break;
                default:
                  break;
              }
            }}
            showLabels
          >
            <BottomNavigationAction label={translate("Home")} icon={<HomeIcon />} />
            <BottomNavigationAction label={translate("Books")} icon={<MenuBookIcon />} />
            {user && (
              <BottomNavigationAction
                label={translate("Leases")}
                icon={<CalendarTodayIcon />}
              />
            )}
            <BottomNavigationAction
              label={user ? translate("Profile") : translate("Login")}
              icon={<PersonIcon />}
            />
            {user?.role === 'admin' && (
              <BottomNavigationAction
                label={translate("Admin")}
                icon={<Badge badgeContent={notificationCount} color="error"><NotificationsIcon /></Badge>}
                onClick={() => navigate('/admin')}
              />
            )}
          </BottomNavigation>
        </Box>
      )}


    </motion.div>
  );
};

export default Navbar;