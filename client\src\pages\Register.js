import React, { useState, useContext } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Box,
  Paper,
  Link,
  Alert,
  CircularProgress,
  Grid,
  Divider,
  IconButton,
  InputAdornment
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Person as PersonIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { AuthContext } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { useSecurity } from '../context/SecurityContext';
import GoogleAuth from '../components/auth/GoogleAuth';
import useRecaptcha from '../hooks/useRecaptcha';

const Register = () => {
  const { translate } = useLanguage();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registerError, setRegisterError] = useState('');
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { register, emailVerificationSent } = useContext(AuthContext);
  const navigate = useNavigate();
  const { securityStatus } = useSecurity();
  const { executeRecaptcha, isReady: recaptchaReady, error: recaptchaError } = useRecaptcha(securityStatus.requiresRecaptcha);



  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.username) {
      errors.username = translate('Username is required');
    } else if (formData.username.length < 3) {
      errors.username = translate('Username must be at least 3 characters');
    }

    if (!formData.email) {
      errors.email = translate('Email is required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = translate('Email is invalid');
    }

    if (!formData.password) {
      errors.password = translate('Password is required');
    } else if (formData.password.length < 6) {
      errors.password = translate('Password must be at least 6 characters');
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = translate('Passwords do not match');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);
      setRegisterError('');

      try {
        // Get reCAPTCHA token if required
        let recaptchaToken = null;
        if (securityStatus.requiresRecaptcha) {
          if (!recaptchaReady) {
            setRegisterError(translate('reCAPTCHA is loading. Please wait and try again.'));
            setIsSubmitting(false);
            return;
          }

          recaptchaToken = await executeRecaptcha('register');
          if (!recaptchaToken) {
            setRegisterError(translate('reCAPTCHA verification failed. Please try again.'));
            setIsSubmitting(false);
            return;
          }
        }

        // Remove confirmPassword before sending to API and add reCAPTCHA token if needed
        const { confirmPassword, ...registerData } = formData;
        const registerDataWithRecaptcha = {
          ...registerData,
          ...(recaptchaToken && { recaptchaToken })
        };

        await register(registerDataWithRecaptcha);

        // Set registration complete to show verification message
        setRegistrationComplete(true);
      } catch (err) {
        console.error('Registration error:', err);
        let errorMessage = translate('Registration failed. Please try again.');

        if (err.response?.data?.message) {
          const serverMessage = err.response.data.message;

          // Handle specific error messages
          if (serverMessage.includes('User already exists')) {
            errorMessage = translate('An account with this email or username already exists.');
          } else if (serverMessage.includes('reCAPTCHA')) {
            errorMessage = translate('Security verification failed. Please try again.');
          } else if (serverMessage.includes('All fields are required')) {
            errorMessage = translate('Please fill in all required fields.');
          } else if (serverMessage.includes('Email is invalid')) {
            errorMessage = translate('Please enter a valid email address.');
          } else {
            errorMessage = translate(serverMessage);
          }
        } else if (err.response?.status === 400) {
          errorMessage = translate('Invalid registration data. Please check your information.');
        } else if (err.response?.status === 500) {
          errorMessage = translate('Server error. Please try again later.');
        }

        setRegisterError(errorMessage);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // If registration is complete, show verification message
  if (registrationComplete) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Container maxWidth="sm">
          <Paper elevation={3} sx={{ p: 4, mt: 4 }}>
            <Typography variant="h4" component="h1" align="center" gutterBottom>
              {translate('Registration Complete')}
            </Typography>

            <Alert severity="success" sx={{ mb: 3 }}>
              {translate('Your account has been created successfully!')}
            </Alert>

            {emailVerificationSent ? (
              <Box>
                <Typography variant="body1" paragraph>
                  {translate('A verification email has been sent to')} <strong>{formData.email}</strong>.
                </Typography>
                <Typography variant="body1" paragraph>
                  {translate('Please check your inbox (and spam folder) and click the verification link to complete your registration.')}
                </Typography>
                <Typography variant="body1" paragraph>
                  {translate('You can still use the application, but some features may be limited until you verify your email address.')}
                </Typography>
              </Box>
            ) : (
              <Typography variant="body1" paragraph>
                {translate('You can now log in to your account.')}
              </Typography>
            )}

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate('/login')}
              >
                {translate('Go to Login')}
              </Button>
            </Box>
          </Paper>
        </Container>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 4 }}>
          <Typography variant="h4" component="h1" align="center" gutterBottom>
            {translate('Create an Account')}
          </Typography>

          {registerError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {registerError}
            </Alert>
          )}

          {securityStatus.requiresRecaptcha && (
            <Alert severity="info" sx={{ mb: 2 }}>
              {translate('Enhanced security is enabled. Additional verification required.')}
            </Alert>
          )}

          {recaptchaError && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {translate('Security verification unavailable. Please try again later.')}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} noValidate>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label={translate('Username')}
              name="username"
              autoComplete="username"
              autoFocus
              value={formData.username}
              onChange={handleChange}
              error={!!formErrors.username}
              helperText={formErrors.username || translate('Choose a unique username')}
              disabled={isSubmitting}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label={translate('Email Address')}
              name="email"
              type="email"
              autoComplete="email"
              value={formData.email}
              onChange={handleChange}
              error={!!formErrors.email}
              helperText={formErrors.email || translate('Enter a valid email address')}
              disabled={isSubmitting}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label={translate('Password')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password || translate('Minimum 6 characters')}
              disabled={isSubmitting}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                      disabled={isSubmitting}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label={translate('Confirm Password')}
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={!!formErrors.confirmPassword}
              helperText={formErrors.confirmPassword || translate('Must match password')}
              disabled={isSubmitting}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle confirm password visibility"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      edge="end"
                      disabled={isSubmitting}
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={isSubmitting}
            >
              {isSubmitting ? <CircularProgress size={24} /> : translate('Register')}
            </Button>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2">
                {translate('Already have an account?')}{' '}
                <Link component={RouterLink} to="/login" variant="body2">
                  {translate('Sign In')}
                </Link>
              </Typography>

              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {translate('OR')}
                </Typography>
              </Divider>

              <GoogleAuth />
            </Box>
          </Box>
        </Paper>
      </Container>
    </motion.div>
  );
};

export default Register;