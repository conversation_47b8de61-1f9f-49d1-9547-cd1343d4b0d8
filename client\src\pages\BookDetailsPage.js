import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Container, Typography, Box, Button, Grid, Paper, Chip, CircularProgress, Alert, Snackbar, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import BookCover from '../components/books/BookCover';

const BookDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { translate } = useLanguage();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [leaseLoading, setLeaseLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const fetchBookDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/books/${id}`);
      setBook(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching book details:', err);
      setError(translate('Failed to load book details. It might not exist or there was a server error.'));
      setBook(null);
    } finally {
      setLoading(false);
    }
  }, [id, translate]);

  useEffect(() => {
    fetchBookDetails();
  }, [fetchBookDetails]);

  const handleLease = async () => {
    if (!user) {
      setSnackbar({ open: true, message: translate('Please log in to lease books.'), severity: 'warning' });
      return;
    }
    try {
      setLeaseLoading(true);
      await axios.post('/api/leases', { book_id: id }, { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } });
      setSnackbar({ open: true, message: translate('Book leased successfully!'), severity: 'success' });
      fetchBookDetails();
    } catch (err) {
      console.error('Error leasing book:', err);
      const errorMessage = err.response?.data?.error || translate('Failed to lease book. Please try again.');
      setSnackbar({ open: true, message: translate(errorMessage), severity: 'error' });
    } finally {
      setLeaseLoading(false);
    }
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!book) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Typography variant="h6" align="center">{translate('Book not found.')}</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ my: 4 }}>
      <Paper elevation={3} sx={{ p: { xs: 2, md: 4 } }}>
        <Grid container spacing={{ xs: 2, md: 4 }}>
          <Grid item xs={12} md={4}>
            <BookCover 
              imageUrl={book.cover_image}
              title={book.title}
              sx={{ width: '100%', height: 'auto', maxHeight: 500, objectFit: 'contain', borderRadius: 2 }}
            />
          </Grid>

          <Grid item xs={12} md={8}>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
              {book.title}
            </Typography>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {translate('by')} {book.author}
            </Typography>
            <Chip 
              label={book.category} 
              color="secondary" 
              size="small" 
              sx={{ mb: 2 }} 
            />
            
            <InfoSection>
              <Typography variant="subtitle2" color="text.secondary">ISBN:</Typography>
              <Typography>{book.isbn || translate('N/A')}</Typography>
            </InfoSection>
            
            <InfoSection>
              <Typography variant="subtitle2" color="text.secondary">{translate('Publisher')}:</Typography>
              <Typography>{book.publisher || translate('N/A')}</Typography>
            </InfoSection>

            <InfoSection>
              <Typography variant="subtitle2" color="text.secondary">{translate('Year')}:</Typography>
              <Typography>{book.publication_year || translate('N/A')}</Typography>
            </InfoSection>

            <InfoSection>
              <Typography variant="subtitle2" color="text.secondary">{translate('Pages')}:</Typography>
              <Typography>{book.pages || translate('N/A')}</Typography>
            </InfoSection>

            <Typography variant="body1" paragraph sx={{ mt: 3 }}>
              {book.description || translate('No description available.')}
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Button 
                variant="contained" 
                color="primary" 
                size="large" 
                onClick={handleLease}
                disabled={!user || leaseLoading || !book.is_available}
              >
                {leaseLoading ? 
                  <CircularProgress size={24} sx={{ color: 'white' }} /> 
                 : book.is_available ? 
                  translate('Lease This Book')
                 : translate('Currently Unavailable')
                } 
              </Button>
              {!book.is_available && user && (
                 <Typography variant="caption" display="block" sx={{mt: 1, color: 'warning.main'}}>
                   {translate('This book is currently leased by another user.')}
                 </Typography>
              )}
              {!user && (
                 <Typography variant="caption" display="block" sx={{mt: 1, color: 'text.secondary'}}>
                   {translate('Login to lease this book.')}
                 </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          sx={{ width: '100%' }} 
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default BookDetailsPage; 