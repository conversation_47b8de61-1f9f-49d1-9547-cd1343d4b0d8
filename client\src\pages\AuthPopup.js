import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Button } from '@mui/material';
import { signInWithPopup, GoogleAuthProvider, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { firebaseInitPromise } from '../firebase';
import GoogleIcon from '@mui/icons-material/Google';
import { useLanguage } from '../context/LanguageContext';

/**
 * Dedicated page for Google authentication in a popup window.
 * Uses signInWithPopup and postMessage to communicate back to the main window.
 */
const AuthPopup = () => {
  const [status, setStatus] = useState('initial'); // initial, loading, success, error
  const [errorMessage, setErrorMessage] = useState('');
  const { translate } = useLanguage();

  // Function to initiate Google Sign-In within the popup
  const handleGoogleAuthInPopup = async () => {
    setStatus('loading');
    setErrorMessage(''); // Clear previous errors
    const googleProvider = new GoogleAuthProvider(); // Create a new provider instance

    try {
      // Explicitly set persistence before the popup operation
      const { auth } = await firebaseInitPromise;
      await setPersistence(auth, browserLocalPersistence);
      console.log("[AuthPopup] Persistence set to LOCAL before signInWithPopup.");

      console.log("[AuthPopup] Initiating signInWithPopup...");
      // Perform the sign-in using a popup (within this popup window)
      const result = await signInWithPopup(auth, googleProvider);
      console.log("[AuthPopup] signInWithPopup successful.");

      const { user } = result;
      const { displayName, email, uid, photoURL } = user;

      // IMPORTANT: Get the ID token
      const idToken = await user.getIdToken(true); // Force refresh to ensure a fresh token
      console.log("[AuthPopup] Retrieved ID token.");

      // Prepare the credential data to send back
      const credentialData = {
        type: 'GOOGLE_AUTH_CREDENTIAL', // Add a type for the listener to identify
        payload: {
          displayName,
          email,
          uid,
          photoURL,
          idToken, // Include the ID token
        },
      };

      // Check if window.opener exists before posting message
      if (window.opener) {
        console.log("[AuthPopup] Sending credential data to opener window:", credentialData);
        // Send the credential data back to the window that opened this popup
        window.opener.postMessage(credentialData, window.location.origin); // Use origin for security
        setStatus('success');

        // Optional: Auto-close after a delay
        // setTimeout(() => window.close(), 3000);
      } else {
        console.error("[AuthPopup] window.opener is not available. Cannot send message.");
        setErrorMessage('Could not communicate back to the main application. Please close this window and try again.');
        setStatus('error');
      }
    } catch (error) {
      console.error("[AuthPopup] signInWithPopup error:", error);
      setStatus('error');
      // Provide more specific error messages
      if (error.code === 'auth/popup-closed-by-user') {
        setErrorMessage('Authentication cancelled. You can close this window.');
      } else if (error.code === 'auth/cancelled-popup-request' || error.code === 'auth/popup-blocked') {
        setErrorMessage('Popup issue detected. Please ensure popups are allowed and try again.');
      } else {
        setErrorMessage(error.message || 'Authentication failed. Please try again or close this window.');
      }
      // Keep the popup open on error so the user sees the message
    }
  };

  // Automatically trigger the login when the popup loads
  useEffect(() => {
    console.log("[AuthPopup] Popup loaded, initiating authentication with a short delay...");
    // Add a small delay to potentially allow Firebase SDK to stabilize
    const timerId = setTimeout(() => {
      console.log("[AuthPopup] Delay finished, calling handleGoogleAuthInPopup.");
      handleGoogleAuthInPopup();
    }, 100); // 100ms delay, adjust if necessary

    // Cleanup the timer if the component unmounts before the delay finishes
    return () => clearTimeout(timerId);
  }, []); // Empty dependency array ensures this runs only once on mount

  // Render appropriate content based on the status
  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <Box sx={{ textAlign: 'center', mt: 10 }}>
            <CircularProgress size={40} sx={{ mb: 3 }} />
            <Typography variant="h6">{translate('Connecting to Google...')}</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {translate('Please complete the sign-in process in the Google window.')}
            </Typography>
          </Box>
        );
      case 'success':
        return (
          <Box sx={{ textAlign: 'center', mt: 10 }}>
            <Typography variant="h5" color="success.main" sx={{ mb: 2 }}>
              {translate('Authentication Successful!')}
            </Typography>
            <Typography variant="body1">
              {translate('Please wait while we log you in...')}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {translate('This window should close automatically. If not, you may close it.')}
            </Typography>
             <Button variant="text" onClick={() => window.close()} sx={{ mt: 2 }}>
               {translate('Close Window')}
             </Button>
          </Box>
        );
      case 'error':
        return (
          <Box sx={{ textAlign: 'center', mt: 10 }}>
            <Typography variant="h5" color="error" sx={{ mb: 2 }}>
              {translate('Authentication Failed')}
            </Typography>
            <Typography variant="body1" color="error.main" sx={{ mb: 3 }}>
              {translate(errorMessage) || translate('Authentication failed. Please try again or close this window.')}
            </Typography>
            <Button variant="contained" onClick={handleGoogleAuthInPopup} sx={{ mr: 1 }}>
              {translate('Try Again')}
            </Button>
            <Button variant="text" onClick={() => window.close()}>
              {translate('Close Window')}
            </Button>
          </Box>
        );
      default: // initial
         return (
          <Box sx={{ textAlign: 'center', mt: 10 }}>
            <Typography variant="h6">{translate('Preparing Google Sign-In...')}</Typography>
             <CircularProgress size={40} sx={{ my: 3 }} />
          </Box>
        ); // Should quickly move to loading
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 500, mx: 'auto', border: '1px solid #eee', mt: 5 }}>
      {renderContent()}
    </Box>
  );
};

export default AuthPopup; 