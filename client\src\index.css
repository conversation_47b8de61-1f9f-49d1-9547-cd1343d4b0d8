@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000000; /* Set default body background */
  color: #ffffff; /* Set default text color */

  /* Custom Scrollbar Styles */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: #555 #111; /* For Firefox */
}

/* For Chrome, Edge, and Safari */
body::-webkit-scrollbar {
  width: 8px;
}
body::-webkit-scrollbar-track {
  background: #111;
}
body::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
  border: 2px solid #111;
}
body::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Global link style adjustment if needed */
a {
  color: inherit; /* Ensure links inherit color unless specifically overridden */
  text-decoration: none; /* Remove underlines globally if desired */
}

a:hover {
  text-decoration: underline; /* Add underline on hover */
}

* {
  box-sizing: border-box;
}

.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Responsive breakpoints */
@media (max-width: 600px) {
  .container {
    padding: 16px;
  }
}

@media (min-width: 601px) and (max-width: 960px) {
  .container {
    padding: 24px;
  }
}

@media (min-width: 961px) {
  .container {
    padding: 32px;
  }
}