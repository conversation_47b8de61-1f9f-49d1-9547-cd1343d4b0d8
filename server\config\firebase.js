/**
 * Firebase Admin SDK Configuration
 */
const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

// Initialize Firebase Admin
const initializeFirebase = () => {
  try {
    // Check if Firebase is already initialized
    if (admin.apps.length > 0) {
      console.log('Firebase Admin already initialized');
      return true;
    }

    // Development environment setup
    if (process.env.NODE_ENV === 'development') {
      admin.initializeApp({
        projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || 'educanet-login',
        databaseURL: `https://${process.env.REACT_APP_FIREBASE_PROJECT_ID || 'educanet-login'}.firebaseio.com`
      });
      console.log('Firebase Admin initialized in development mode');
      return true;
    }

    // Production environment setup with credentials
    if (process.env.FIREBASE_ADMIN_CREDENTIALS) {
      const credentials = process.env.FIREBASE_ADMIN_CREDENTIALS;
      
      // Skip if using placeholder credentials
      if (credentials.includes('YOUR_PRIVATE_KEY') || credentials.includes('your_private_key_id')) {
        console.warn('Skipping Firebase Admin initialization - placeholder credentials detected');
        return false;
      }
      
      // Initialize with service account credentials
      admin.initializeApp({
        credential: admin.credential.cert(JSON.parse(credentials))
      });
      console.log('Firebase Admin initialized with service account credentials');
      return true;
    }
    
    console.warn('No Firebase Admin credentials found. Some features may not work.');
    return false;
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error.message);
    console.warn('Running without Firebase Admin authentication. Some features may not work.');
    return false;
  }
};

module.exports = {
  admin,
  initializeFirebase
}; 