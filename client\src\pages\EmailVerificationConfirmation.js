import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Alert,
  CircularProgress,
  <PERSON><PERSON>,
  Link
} from '@mui/material';
import { CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { AuthContext } from '../context/AuthContext';

const EmailVerificationConfirmation = () => {
  const { verifyEmail, refreshUserData } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const processVerification = async () => {
      setLoading(true);
      setError('');
      setSuccess(false);

      try {
        // Extract mode and oobCode from URL query parameters
        const searchParams = new URLSearchParams(location.search);
        const oobCode = searchParams.get('oobCode');
        const mode = searchParams.get('mode');
        const token = searchParams.get('token');
        
        // Log the received parameters (omitting sensitive data)
        console.log('Verification parameters received:', { 
          hasOobCode: !!oobCode, 
          hasMode: !!mode,
          hasToken: !!token,
          mode: mode,
          pathName: location.pathname
        });

        // Wait for auth context to be fully initialized
        if (!verifyEmail) {
          console.log('Waiting for auth context to initialize...');
          setLoading(true);
          return; // Will retry on next render when context is ready
        }

        if (!oobCode && !token) {
          // Check if the entire path contains a token (some email clients might break the URL)
          const pathParts = location.pathname.split('/');
          const possibleToken = pathParts[pathParts.length - 1];
          
          if (possibleToken && possibleToken.length > 10) {
            console.log('Found possible token in path:', possibleToken.substring(0, 5) + '...');
            try {
              const result = await verifyEmail(possibleToken);
              console.log('Verification result:', result);
              setSuccess(true);
            } catch (verifyErr) {
              console.error('Error verifying with token from path:', verifyErr);
              throw verifyErr;
            }
          } else {
            throw new Error('Invalid verification link. No token or code provided.');
          }
        } else if (mode === 'verifyEmail' && oobCode) {
          // Firebase verification link format
          console.log('Processing Firebase email verification');
          try {
            const result = await verifyEmail(oobCode);
            console.log('Firebase verification result:', result);
            setSuccess(true);
          } catch (firebaseVerifyErr) {
            console.error('Error with Firebase verification:', firebaseVerifyErr);
            throw firebaseVerifyErr;
          }
        } else if (token) {
          // Backend verification token format
          console.log('Processing backend email verification');
          try {
            const result = await verifyEmail(token);
            console.log('Backend verification result:', result);
            setSuccess(true);
          } catch (backendVerifyErr) {
            console.error('Error with backend verification:', backendVerifyErr);
            throw backendVerifyErr;
          }
        } else {
          // No valid verification parameters but we have a mode without oobCode
          throw new Error(`Invalid verification parameters. Mode: ${mode || 'none'}`);
        }
      } catch (err) {
        console.error('Verification error:', err);
        setError(err.message || 'Failed to verify email. The link may be invalid or expired.');
      } finally {
        setLoading(false);
      }
    };

    processVerification();
  }, [location, verifyEmail]);

  const handleRedirect = () => {
    // Refresh user data before redirecting
    refreshUserData && refreshUserData(true);
    
    // Navigate to external URL
    window.location.href = 'https://knihovna.educanet.me';
  };

  const handleGoToLogin = () => {
    navigate('/login');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Email Verification
            </Typography>
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 4 }}>
              <CircularProgress size={60} thickness={4} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Verifying your email...
              </Typography>
            </Box>
          )}

          {error && (
            <Box sx={{ my: 3 }}>
              <Alert 
                severity="error"
                icon={<ErrorIcon fontSize="inherit" />}
              >
                {error}
              </Alert>
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Button 
                  variant="outlined" 
                  color="primary" 
                  onClick={() => window.location.href = 'https://knihovna.educanet.me'}
                  sx={{ mr: 2 }}
                >
                  Go to Library
                </Button>
                <Button 
                  variant="contained" 
                  color="primary"
                  onClick={() => window.location.href = 'https://knihovna.educanet.me/verify-email'}
                >
                  Request New Verification
                </Button>
              </Box>
            </Box>
          )}

          {success && (
            <Box sx={{ my: 3 }}>
              <Alert 
                severity="success"
                icon={<CheckCircleIcon fontSize="inherit" />}
              >
                Your email has been successfully verified!
              </Alert>
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => window.location.href = 'https://knihovna.educanet.me'}
                >
                  Continue to Library
                </Button>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </motion.div>
  );
};

export default EmailVerificationConfirmation; 