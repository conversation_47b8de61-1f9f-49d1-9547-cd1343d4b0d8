import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import {
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Edit as EditIcon,
  AdminPanelSettings as AdminIcon,
  Person as PersonIcon,
  Delete as DeleteIcon,
  VerifiedUser as VerifiedUserIcon,
  MarkEmailRead as VerifyEmailIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import api from '../../utils/api';
import { AuthContext } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';

const UserManagement = ({ itemVariants }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [banDialogOpen, setBanDialogOpen] = useState(false);
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [verifyEmailDialogOpen, setVerifyEmailDialogOpen] = useState(false);
  const [profileEditDialogOpen, setProfileEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [banReason, setBanReason] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [profileFormData, setProfileFormData] = useState({
    class_teacher: '',
    grade: ''
  });

  const { verifyUserEmail } = useContext(AuthContext);
  const { translate } = useLanguage();

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Fetch all users
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await api.direct.get('/api/users');
      console.log('Users API response:', response);

      // Ensure users is always an array
      const userData = response.data?.users || response.data || [];
      const usersArray = Array.isArray(userData) ? userData : [];

      console.log('Parsed users:', usersArray);
      setUsers(usersArray);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(translate('Failed to load users. Please try again.'));
      setUsers([]); // Set to empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Open ban dialog
  const handleBanDialogOpen = (user) => {
    setSelectedUser(user);
    setBanReason(user.ban_reason || '');
    setBanDialogOpen(true);
  };

  // Close ban dialog
  const handleBanDialogClose = () => {
    setBanDialogOpen(false);
    setSelectedUser(null);
    setBanReason('');
  };

  // Open role dialog
  const handleRoleDialogOpen = (user) => {
    setSelectedUser(user);
    setSelectedRole(user.role);
    setRoleDialogOpen(true);
  };

  // Close role dialog
  const handleRoleDialogClose = () => {
    setRoleDialogOpen(false);
    setSelectedUser(null);
    setSelectedRole('');
  };

  // Open delete dialog
  const handleDeleteDialogOpen = (user) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  // Close delete dialog
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedUser(null);
  };

  // Open email verification dialog
  const handleVerifyEmailDialogOpen = (user) => {
    setSelectedUser(user);
    setVerifyEmailDialogOpen(true);
    setVerificationSuccess(false);
  };

  // Close email verification dialog
  const handleVerifyEmailDialogClose = () => {
    setVerifyEmailDialogOpen(false);
    setSelectedUser(null);
  };

  // Open profile edit dialog
  const handleProfileEditDialogOpen = (user) => {
    setSelectedUser(user);
    setProfileFormData({
      class_teacher: user.class_teacher || '',
      grade: user.grade || ''
    });
    setProfileEditDialogOpen(true);
  };

  // Close profile edit dialog
  const handleProfileEditDialogClose = () => {
    setProfileEditDialogOpen(false);
    setSelectedUser(null);
    setProfileFormData({
      class_teacher: '',
      grade: ''
    });
  };

  // Ban or unban a user
  const handleBanUser = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError('');

      const isBanning = !selectedUser.is_banned;

      await api.direct.put(`/api/users/${selectedUser.id}/ban`, {
        is_banned: isBanning,
        ban_reason: isBanning ? banReason : null
      });

      // Refresh users list
      await fetchUsers();

      // Close dialog
      handleBanDialogClose();
    } catch (err) {
      setError(translate(err.response?.data?.message || 'Failed to update user ban status. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  // Update user role
  const handleUpdateRole = async () => {
    if (!selectedUser || !selectedRole) return;

    try {
      setLoading(true);
      setError('');

      await api.direct.put(`/api/users/${selectedUser.id}/role`, {
        role: selectedRole
      });

      // Refresh users list
      await fetchUsers();

      // Close dialog
      handleRoleDialogClose();
    } catch (err) {
      setError(translate(err.response?.data?.message || 'Failed to update user role. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  // Delete a user account
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError('');

      // Prevent deleting admin accounts
      if (selectedUser.role === 'admin') {
        setError(translate('Admin accounts cannot be deleted.'));
        setLoading(false);
        return;
      }

      await api.direct.delete(`/api/users/${selectedUser.id}`);

      // Refresh users list
      await fetchUsers();

      // Close dialog
      handleDeleteDialogClose();
    } catch (err) {
      setError(translate(err.response?.data?.message || 'Failed to delete user account. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  // Verify a user's email
  const handleVerifyEmail = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError('');

      // Make a direct API call instead of using the context function
      const response = await api.direct.post(`/api/users/${selectedUser.id}/verify-email`);
      console.log('Verification response:', response.data);

      // Set success state
      setVerificationSuccess(true);

      // Update the user in the local state - only this specific user
      if (Array.isArray(users)) {
        setUsers(users.map(user =>
          user.id === selectedUser.id ? { ...user, email_verified: 1 } : user
        ));
      }

      // Don't close dialog yet - show success message
    } catch (err) {
      console.error('Email verification error:', err);
      setError(translate(err.response?.data?.message || 'Failed to verify user email. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const handleUpdateProfile = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError('');

      await api.direct.put(`/api/users/${selectedUser.id}/profile`, {
        class_teacher: profileFormData.class_teacher.trim(),
        grade: profileFormData.grade.trim()
      });

      // Refresh users list
      await fetchUsers();

      // Close dialog
      handleProfileEditDialogClose();
    } catch (err) {
      setError(translate(err.response?.data?.message || 'Failed to update user profile. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div variants={itemVariants}>
      {loading && users.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{translate('Username')}</TableCell>
                <TableCell>{translate('Email')}</TableCell>
                <TableCell>{translate('Role')}</TableCell>
                <TableCell>{translate('Status')}</TableCell>
                <TableCell>{translate('Verified')}</TableCell>
                <TableCell>{translate('Class Teacher')}</TableCell>
                <TableCell>{translate('Grade')}</TableCell>
                <TableCell>{translate('Created')}</TableCell>
                <TableCell align="right">{translate('Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(users) && users.length > 0 ? (
                users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.username}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        icon={user.role === 'admin' ? <AdminIcon fontSize="small" /> : <PersonIcon fontSize="small" />}
                        label={translate(user.role === 'admin' ? 'Admin' : 'User')}
                        color={user.role === 'admin' ? 'secondary' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={translate(user.is_banned ? 'Banned' : 'Active')}
                        color={user.is_banned ? 'error' : 'success'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={user.email_verified ? <VerifiedUserIcon fontSize="small" /> : null}
                        label={user.email_verified ? translate('Verified') : translate('Not Verified')}
                        color={user.email_verified ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {user.class_teacher || translate('Not specified')}
                    </TableCell>
                    <TableCell>
                      {user.grade || translate('Not specified')}
                    </TableCell>
                    <TableCell>
                      {new Date(user.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title={translate(user.is_banned ? 'Unban User' : 'Ban User')}>
                        <IconButton
                          size="small"
                          color={user.is_banned ? 'success' : 'error'}
                          onClick={() => handleBanDialogOpen(user)}
                          disabled={user.role === 'admin'}
                        >
                          <BlockIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={translate('Change Role')}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleRoleDialogOpen(user)}
                          disabled={user.role === 'admin'}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={translate('Edit Profile')}>
                        <IconButton
                          size="small"
                          color="secondary"
                          onClick={() => handleProfileEditDialogOpen(user)}
                        >
                          <SchoolIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      {!user.email_verified && (
                        <Tooltip title={translate('Verify Email')}>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleVerifyEmailDialogOpen(user)}
                            disabled={user.role === 'admin'}
                          >
                            <VerifyEmailIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title={translate('Delete User')}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteDialogOpen(user)}
                          disabled={user.role === 'admin'}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      {translate('No users found')}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {/* Ban User Dialog */}
      <Dialog
        open={banDialogOpen}
        onClose={handleBanDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {selectedUser?.is_banned ? translate('Unban User') : translate('Ban User')}
        </DialogTitle>
        <DialogContent>
          {selectedUser?.is_banned ? (
            <Typography>
              {translate('Are you sure you want to unban')} {selectedUser?.username}?
            </Typography>
          ) : (
            <>
              <Typography gutterBottom>
                {translate('Are you sure you want to ban')} {selectedUser?.username}?
              </Typography>
              <TextField
                fullWidth
                label={translate('Ban Reason')}
                value={banReason}
                onChange={(e) => setBanReason(e.target.value)}
                margin="normal"
                placeholder={translate('Reason for banning this user')}
                multiline
                rows={3}
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleBanDialogClose} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleBanUser}
            color={selectedUser?.is_banned ? 'success' : 'error'}
            variant="contained"
            disabled={loading}
          >
            {loading
              ? translate('Processing...')
              : selectedUser?.is_banned
                ? translate('Unban User')
                : translate('Ban User')
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Role Dialog */}
      <Dialog
        open={roleDialogOpen}
        onClose={handleRoleDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {translate('Change User Role')}
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            {translate('Update role for user:')} {selectedUser?.username}
          </Typography>
          <FormControl fullWidth margin="normal">
            <InputLabel>{translate('Role')}</InputLabel>
            <Select
              value={selectedRole || ''}
              onChange={(e) => setSelectedRole(e.target.value)}
              label={translate('Role')}
            >
              <MenuItem value="user">{translate('User')}</MenuItem>
              <MenuItem value="admin">{translate('Admin')}</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRoleDialogClose} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleUpdateRole}
            color="primary"
            variant="contained"
            disabled={loading || !selectedRole}
          >
            {loading ? translate('Updating...') : translate('Update Role')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {translate('Delete User Account')}
        </DialogTitle>
        <DialogContent>
          <Typography paragraph color="error">
            {translate('Warning: This action cannot be undone.')}
          </Typography>
          <Typography>
            {translate('Are you sure you want to permanently delete the account for')} {selectedUser?.username}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleDeleteUser}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? translate('Deleting...') : translate('Delete User')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Verify Email Dialog */}
      <Dialog
        open={verifyEmailDialogOpen}
        onClose={handleVerifyEmailDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {translate('Verify User Email')}
        </DialogTitle>
        <DialogContent>
          {verificationSuccess ? (
            <Alert severity="success" sx={{ mt: 2 }}>
              {translate('Email has been verified successfully for')} {selectedUser?.username}.
            </Alert>
          ) : (
            <>
              <Typography paragraph>
                {translate('Manually verify email for user:')} {selectedUser?.username}
              </Typography>
              <Typography paragraph>
                {translate('Email address:')} {selectedUser?.email}
              </Typography>
              <Typography color="info.main">
                {translate('This will mark the user email as verified without requiring them to go through the email verification process.')}
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleVerifyEmailDialogClose} color="inherit">
            {translate('Close')}
          </Button>
          {!verificationSuccess && (
            <Button
              onClick={handleVerifyEmail}
              color="primary"
              variant="contained"
              disabled={loading}
              startIcon={<VerifiedUserIcon />}
            >
              {loading ? translate('Verifying...') : translate('Verify Email')}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Profile Edit Dialog */}
      <Dialog
        open={profileEditDialogOpen}
        onClose={handleProfileEditDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {translate('Edit User Profile')}
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            {translate('Update profile information for user:')} {selectedUser?.username}
          </Typography>
          <TextField
            fullWidth
            label={translate('Class Teacher')}
            value={profileFormData.class_teacher}
            onChange={(e) => setProfileFormData(prev => ({ ...prev, class_teacher: e.target.value }))}
            margin="normal"
            placeholder={translate('Enter class teacher name')}
          />
          <TextField
            fullWidth
            label={translate('Grade/Class')}
            value={profileFormData.grade}
            onChange={(e) => setProfileFormData(prev => ({ ...prev, grade: e.target.value }))}
            margin="normal"
            placeholder={translate('Enter grade or class')}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleProfileEditDialogClose} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleUpdateProfile}
            color="primary"
            variant="contained"
            disabled={loading || !profileFormData.class_teacher.trim() || !profileFormData.grade.trim()}
          >
            {loading ? translate('Updating...') : translate('Update Profile')}
          </Button>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default UserManagement;