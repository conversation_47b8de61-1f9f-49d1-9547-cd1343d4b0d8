import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Grid,
  Paper,
  Snackbar,
  Alert,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Tabs,
  Tab,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  ListItemText,
  Divider,
  List,
  ListItem,
  ListItemSecondaryAction,
  AppBar,
  InputAdornment,
  LinearProgress,
  useMediaQuery,
  ToggleButtonGroup,
  ToggleButton
} from '@mui/material';
import {
  Add as AddIcon,
  CloudUpload as CloudUploadIcon,
  Save as SaveIcon,
  QrCodeScanner as QrCodeScannerIcon,
  Camera as CameraIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  FilterList as FilterListIcon,
  KeyboardArrowRight as KeyboardArrowRightIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Clear as ClearIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';
import api from '../../utils/api';
import PropTypes from 'prop-types';
import BarcodeScanner from '../books/BarcodeScanner';
import BookCover from '../books/BookCover';
import BookForm from '../books/BookForm';
import { useTheme } from '@mui/material/styles';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`book-management-tabpanel-${index}`}
      aria-labelledby={`book-management-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `book-management-tab-${index}`,
    'aria-controls': `book-management-tabpanel-${index}`,
  };
}

// Add these ISBN validation utilities after imports and before component
const isValidISBN = (isbn) => {
  // Remove any hyphens or spaces
  isbn = isbn.replace(/[- ]/g, '');

  // Check for ISBN-10 or ISBN-13 format
  if (isValidISBN10(isbn)) return { valid: true, format: 'ISBN-10', isbn };
  if (isValidISBN13(isbn)) return { valid: true, format: 'ISBN-13', isbn };

  return { valid: false };
};

// ISBN-10 validation (with checksum verification)
const isValidISBN10 = (isbn) => {
  if (!isbn || isbn.length !== 10) return false;

  // ISBN-10 can have 'X' as the last character
  if (!/^[0-9]{9}[0-9X]$/.test(isbn)) return false;

  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(isbn.charAt(i)) * (10 - i);
  }

  // Handle the last character (can be 'X' which equals 10)
  let last = isbn.charAt(9);
  if (last === 'X') {
    sum += 10;
  } else {
    sum += parseInt(last);
  }

  return sum % 11 === 0;
};

// ISBN-13 validation (with checksum verification)
const isValidISBN13 = (isbn) => {
  if (!isbn || isbn.length !== 13) return false;

  // ISBN-13 should only contain digits
  if (!/^[0-9]{13}$/.test(isbn)) return false;

  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(isbn.charAt(i)) * (i % 2 === 0 ? 1 : 3);
  }

  // Check digit is the value needed to make sum mod 10 = 0
  const checkDigit = (10 - (sum % 10)) % 10;

  return checkDigit === parseInt(isbn.charAt(12));
};

// Convert ISBN-10 to ISBN-13
const convertISBN10to13 = (isbn10) => {
  // Remove any hyphens or spaces
  isbn10 = isbn10.replace(/[- ]/g, '');

  if (!isValidISBN10(isbn10)) return null;

  // ISBN-13 starts with 978 followed by first 9 digits of ISBN-10
  const isbn13Base = '978' + isbn10.substring(0, 9);

  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(isbn13Base.charAt(i)) * (i % 2 === 0 ? 1 : 3);
  }

  // Check digit is the value needed to make sum mod 10 = 0
  const checkDigit = (10 - (sum % 10)) % 10;

  return isbn13Base + checkDigit;
};

// Convert ISBN-13 to ISBN-10 (only possible for ISBN-13 starting with 978)
const convertISBN13to10 = (isbn13) => {
  // Remove any hyphens or spaces
  isbn13 = isbn13.replace(/[- ]/g, '');

  if (!isValidISBN13(isbn13) || !isbn13.startsWith('978')) return null;

  // Extract the middle 9 digits (excluding the 978 prefix and the check digit)
  const isbn10Base = isbn13.substring(3, 12);

  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(isbn10Base.charAt(i)) * (10 - i);
  }

  // Calculate check digit
  let checkDigit = 11 - (sum % 11);
  if (checkDigit === 10) checkDigit = 'X';
  if (checkDigit === 11) checkDigit = '0';

  return isbn10Base + checkDigit;
};

const BookManagement = () => {
  const { translate } = useLanguage();
  const [addBookDialogOpen, setAddBookDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [categories, setCategories] = useState([]);
  const [books, setBooks] = useState([]);
  const [booksLoading, setBooksLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [editBookId, setEditBookId] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [error, setError] = useState('');

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Category management
  const [newCategory, setNewCategory] = useState('');
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [editCategoryId, setEditCategoryId] = useState(null);
  const [editCategoryName, setEditCategoryName] = useState('');
  const [categoryLoading, setCategoryLoading] = useState(false);

  // New book form state
  const [newBook, setNewBook] = useState({
    title: '',
    author: '',
    isbn: '',
    description: '',
    category: '',
    total_copies: 1,
    publisher: '',
    published_year: '',
    cover_image: '',
    image_source: 'upload' // 'upload' or 'url'
  });

  // CSV import state
  const [csvFile, setCsvFile] = useState(null);

  // Add state for scanner dialog
  const [scannerDialogOpen, setScannerDialogOpen] = useState(false);
  const [scannedISBN, setScannedISBN] = useState('');
  const [scannerError, setScannerError] = useState('');
  const [scannerLoading, setScannerLoading] = useState(false);

  // Add new state variables
  const [scannerBookFound, setScannerBookFound] = useState(false);
  const [editBookData, setEditBookData] = useState(null);
  const [newBookData, setNewBookData] = useState(null);
  const [isSavingScannedBook, setIsSavingScannedBook] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [bookCoverPreview, setBookCoverPreview] = useState('');
  const [barcodeDetected, setBarcodeDetected] = useState(false);
  const [coverImageFile, setCoverImageFile] = useState(null);

  // Use refs to track initial fetch
  const initialFetchDone = useRef(false);

  // Cleanup camera on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (window.barcodeScannerCleanup) {
        try {
          window.barcodeScannerCleanup();
          console.log('Camera cleanup called on page unload');
        } catch (error) {
          console.log('Error during camera cleanup on page unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Also cleanup when component unmounts
      if (window.barcodeScannerCleanup) {
        try {
          window.barcodeScannerCleanup();
          console.log('Camera cleanup called on component unmount');
        } catch (error) {
          console.log('Error during camera cleanup on component unmount:', error);
        }
      }
    };
  }, []);

  // Tab change handler
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Define showSnackbar before it's used in callbacks
  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Memoize fetch functions to avoid dependency warnings
  const fetchBooks = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.direct.get('/api/books');
      console.log('Books fetched successfully:', response.data);
      setBooks(response.data || []);
      setFilteredBooks(response.data || []);
    } catch (error) {
      console.error('Error fetching books:', error);
      showSnackbar(translate('Error fetching books'), 'error');
    } finally {
      setLoading(false);
    }
  }, [showSnackbar, translate]);

  const fetchCategories = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await api.direct.get('/api/books/categories', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.data && Array.isArray(response.data)) {
        // Handle array of category objects format
        setCategories(response.data.map(c => ({ id: c.id, name: c.name })));
        console.log('Categories fetched successfully:', response.data);
      } else {
        console.error('Unexpected categories response format:', response.data);
        showSnackbar(translate('Error fetching categories'), 'error');
      }
    } catch (error) {
      console.error('Error fetching categories:', error.response?.data || error.message);
      showSnackbar(translate('Error fetching categories'), 'error');
    }
  }, [showSnackbar, translate]);

  // Fetch both books and categories when component mounts
  useEffect(() => {
    if (!initialFetchDone.current) {
      fetchCategories();
      fetchBooks();
      initialFetchDone.current = true;
    }
  }, [fetchBooks, fetchCategories]);

  // Filter books when search term changes
  useEffect(() => {
    if (!books.length) return;

    if (searchTerm.trim() === '') {
      setFilteredBooks(books);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = books.filter(book =>
        book.title.toLowerCase().includes(term) ||
        book.author.toLowerCase().includes(term) ||
        (book.isbn && book.isbn.includes(term)) ||
        (book.category && book.category.toLowerCase().includes(term))
      );
      setFilteredBooks(filtered);
    }
  }, [searchTerm, books]);

  // Handle Add Book Dialog
  const handleOpenAddBookDialog = () => {
    setEditBookId(null);
    // Initialize with empty book for new book
    setNewBook({
      title: '',
      author: '',
      isbn: '',
      description: '',
      category: '',
      total_copies: 1,
      publisher: '',
      published_year: '',
      cover_image: '' // Add cover_image field
    });
    setAddBookDialogOpen(true);
  };

  const handleCloseAddBookDialog = () => {
    setAddBookDialogOpen(false);
    setEditBookId(null);
  };

  // Helper function to upload book cover image is defined later in the file

  // Handle saving book with possible image upload - improved for large images
  const handleSaveBook = async (bookData, coverImageFile) => {
    setLoading(true);

    try {
      // First, handle image upload if there's a file
      let finalBookData = { ...bookData };

      // Remove fields that cause SQL errors
      delete finalBookData.updated_at;
      delete finalBookData.created_at;

      // If we have a file, convert it to base64
      if (coverImageFile) {
        try {
          // Convert the file to base64
          const base64Image = await uploadBookCover(coverImageFile);
          if (base64Image) {
            // Store the base64 data directly
            finalBookData.cover_image = base64Image;
            finalBookData.image_url = base64Image;
          }
        } catch (uploadError) {
          console.error('Error processing image:', uploadError);
          showSnackbar(translate('Failed to process image, continuing with book save'), 'warning');
        }
      }

      // Ensure available_copies is set properly
      if (finalBookData.total_copies && !finalBookData.available_copies) {
        finalBookData.available_copies = finalBookData.total_copies;
      }

      const token = localStorage.getItem('token');

      // Edit existing book
      if (editBookId) {
        await api.direct.put(`/api/books/${editBookId}`, finalBookData, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        showSnackbar(translate('Book updated successfully'), 'success');
      }
      // Add new book
      else {
        await api.direct.post('/api/books', finalBookData, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        showSnackbar(translate('Book added successfully'), 'success');
      }

      handleCloseAddBookDialog();
      await fetchBooks();
    } catch (error) {
      console.error('Error saving book:', error);
      let errorMessage = translate('Error saving book');

      if (error.response) {
        if (error.response.status === 500) {
          // More specific message for entity too large errors
          if (error.response.data && typeof error.response.data === 'string' &&
              error.response.data.includes('entity too large')) {
            errorMessage = translate('The data is too large. Try with a smaller image.');
          } else if (error.response.data && typeof error.response.data === 'string' &&
                    error.response.data.includes('no column named updated_at')) {
            errorMessage = translate('Database schema error. Please contact administrator.');
            console.error('Database schema error - updated_at column missing:', error.response.data);
          } else {
            errorMessage = translate('Database error. The book could not be saved.');
            console.error('Server error details:', error.response.data);
          }
        } else if (error.response.status === 413) {
          errorMessage = translate('The image is too large. Please use a smaller image.');
        } else if (error.response.status === 404) {
          errorMessage = translate('Upload endpoint not found. Please check server configuration.');
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenEditBookDialog = (book) => {
    setEditBookId(book.id);
    setNewBook({
      title: book.title || '',
      author: book.author || '',
      isbn: book.isbn || '',
      description: book.description || '',
      category: book.category || '',
      total_copies: book.total_copies || 1,
      available_copies: book.available_copies || 1,
      publisher: book.publisher || '',
      published_year: book.published_year || ''
    });
    setAddBookDialogOpen(true);
  };

  const handleOpenImportDialog = () => {
    setImportDialogOpen(true);
  };

  const handleCloseImportDialog = () => {
    setImportDialogOpen(false);
    setCsvFile(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewBook(prev => ({ ...prev, [name]: value }));
  };

  const handleCsvFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setCsvFile(e.target.files[0]);
    }
  };

  const validateBookData = () => {
    if (!newBook.title || !newBook.author) {
      showSnackbar(translate('Title and author are required'), 'error');
      return false;
    }
    return true;
  };

  const handleAddBook = async () => {
    if (!validateBookData()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const method = editBookId ? 'put' : 'post';
      const endpoint = editBookId ? `/api/books/${editBookId}` : '/api/books';

      await api.direct[method](endpoint, newBook, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      await fetchBooks();
      handleCloseAddBookDialog();

      showSnackbar(
        editBookId
          ? translate('Book updated successfully')
          : translate('Book added successfully'),
        'success'
      );
    } catch (error) {
      console.error('Error saving book:', error);
      showSnackbar(
        editBookId
          ? translate('Failed to update book')
          : translate('Failed to add book'),
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBook = async (bookId) => {
    if (!window.confirm(translate('Are you sure you want to delete this book?'))) {
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      // Make sure the Authorization header is properly set
      await api.direct.delete(`/api/books/${bookId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      await fetchBooks();
      showSnackbar(translate('Book deleted successfully'), 'success');
    } catch (error) {
      console.error('Error deleting book:', error);
      let errorMessage = translate('Failed to delete book');
      if (error.response) {
        errorMessage += `: ${error.response.data?.message || error.message}`;
      }
      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleImportBooks = async () => {
    if (!csvFile) {
      showSnackbar(translate('Please select a CSV file'), 'error');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', csvFile);

    try {
      const token = localStorage.getItem('token');
      await api.direct.post('/api/books/import', formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      await fetchBooks();
      handleCloseImportDialog();
      showSnackbar(translate('Books imported successfully'), 'success');
    } catch (error) {
      console.error('Error importing books:', error);

      let errorMessage = translate('Failed to import books');
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      }

      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the scanner dialog
  const handleOpenScannerDialog = () => {
    setScannerDialogOpen(true);
    setScannedISBN('');
    setScannerError('');
  };

  // Handle closing the scanner dialog
  const handleCloseScannerDialog = () => {
    console.log('Closing scanner dialog and cleaning up camera...');

    // Call the barcode scanner cleanup function if it exists
    if (window.barcodeScannerCleanup) {
      try {
        window.barcodeScannerCleanup();
        console.log('Camera cleanup called successfully');
      } catch (error) {
        console.log('Error during camera cleanup:', error);
      }
    }

    // Reset all scanner-related state
    setScannerDialogOpen(false);
    setScannerLoading(false);
    setScannedISBN('');
    setScannerError('');
    setScannerBookFound(false);
    setEditBookData(null);
    setNewBookData(null);
    setBookCoverPreview('');
    setCoverImageFile(null);
    setBarcodeDetected(false);
    setSuccessMessage('');
  };

  // Handle ISBN input change
  const handleISBNChange = (e) => {
    setScannedISBN(e.target.value);
  };

  // Handle image source change
  const handleImageSourceChange = (e) => {
    const newImageSource = e.target.value;
    if (newBookData) {
      setNewBookData(prev => ({ ...prev, image_source: newImageSource }));

      // If switching to URL and we have a cover_image but no image_url, copy it over
      if (newImageSource === 'url' && !prev.image_url && prev.cover_image) {
        setNewBookData(prev => ({ ...prev, image_url: prev.cover_image }));
      }
    }
  };

  // Handle image URL change in scanner dialog
  const handleImageUrlChange = (e) => {
    const newImageUrl = e.target.value;
    if (newBookData) {
      setNewBookData(prev => ({
        ...prev,
        image_url: newImageUrl,
        cover_image: newImageUrl
      }));

      // Live render the image
      if (newImageUrl) {
        setBookCoverPreview(newImageUrl);
      } else {
        setBookCoverPreview('');
      }
    }
  };

  // Handle cover file upload in scanner dialog
  const handleCoverFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      console.log('File selected for upload:', file.name, file.size, 'bytes');

      // Create a preview URL for the uploaded image
      const imageUrl = URL.createObjectURL(file);
      setBookCoverPreview(imageUrl);
      setCoverImageFile(file);

      // Update the book data state
      if (newBookData) {
        console.log('Updating newBookData with image_source: upload');
        setNewBookData(prev => ({
          ...prev,
          image_source: 'upload',
          // Keep the original image_url as fallback but mark as upload
          // image_url: ''
        }));
      }
    }
  };

  // Handle saving the scanned book with cover image
  const handleSaveScannedBookWithCover = async () => {
    try {
      setIsSavingScannedBook(true);
      console.log('Saving scanned book with cover...');
      console.log('newBookData:', newBookData);
      console.log('coverImageFile:', coverImageFile);
      console.log('image_source:', newBookData?.image_source);

      let coverUrl = newBookData.image_url;

      // If using file upload and have a file, convert it to base64
      if (newBookData.image_source === 'upload' && coverImageFile) {
        console.log('Processing uploaded image file...');
        try {
          const base64Image = await uploadBookCover(coverImageFile);
          if (base64Image) {
            console.log('Successfully converted image to base64, length:', base64Image.length);
            coverUrl = base64Image; // Use the base64 data directly
          }
        } catch (imgError) {
          console.error('Error processing image:', imgError);
          showSnackbar(translate('Failed to process image, saving book without custom cover'), 'warning');
          // Continue with saving the book even if image processing fails
        }
      } else {
        console.log('Using original image URL or no image upload');
      }

      // Create the book with the cover URL or base64 data
      const bookToSave = {
        ...newBookData,
        cover_image: coverUrl || newBookData.cover_image,
        image_url: coverUrl || newBookData.image_url
      };

      await handleSaveBook(bookToSave);

      // Close the dialog and reset states (this will also cleanup camera)
      handleCloseScannerDialog();
      setSuccessMessage(translate('Book saved successfully'));
    } catch (error) {
      console.error('Error saving book:', error);
      setScannerError(translate('Failed to save book'));
    } finally {
      setIsSavingScannedBook(false);
    }
  };

  // Update handleScannedISBNWithValue to make ISBN optional
  const handleScannedISBNWithValue = async (isbn) => {
    // If ISBN is empty, just set up an empty book form without doing any lookup
    if (!isbn || isbn.trim() === '') {
      // Stop camera when transitioning to book details screen
      if (window.barcodeScannerCleanup) {
        try {
          window.barcodeScannerCleanup();
          console.log('Camera cleanup called when creating empty book form');
        } catch (error) {
          console.log('Error during camera cleanup:', error);
        }
      }

      setScannerError('');
      setScannerLoading(false);
      setSuccessMessage('');
      setScannerBookFound(true); // Show the book form
      setNewBookData({
        title: '',
        author: '',
        isbn: '',
        description: '',
        publisher: '',
        published_year: '',
        image_url: '',
        category: '',
        total_copies: 1
      });
      setEditBookData(null);
      return;
    }

    // Clean the ISBN (remove spaces, hyphens)
    const cleanISBN = isbn.replace(/[- ]/g, '');

    // Validate the ISBN
    const isbnValidation = isValidISBN(cleanISBN);

    if (!isbnValidation.valid) {
      setScannerError(translate('Invalid ISBN format. Please check and try again.'));
      return;
    }

    console.log(`[Direct ISBN Handler] Processing ${isbnValidation.format}:`, isbnValidation.isbn);
    setScannerError('');
    setScannerLoading(true);
    setSuccessMessage(''); // Clear previous success message
    setScannerBookFound(false); // Reset book found status
    setNewBookData(null);
    setEditBookData(null);

    // Use the validated ISBN for lookup
    const lookupISBN = isbnValidation.isbn;

    try {
      console.log('[ISBN Lookup] Looking up ISBN:', lookupISBN);
      try {
        // *** IMPORTANT: Use the correct endpoint that worked in the old version ***
        const response = await api.direct.get(`/api/books/isbn/${lookupISBN}`);
        console.log('[ISBN Lookup] Response:', response);
        console.log('[ISBN Lookup] Raw response data:', JSON.stringify(response.data));

        // Get the book data from the response
        let bookData = null;

        // Check if this is a book already in our database
        if (response.data && response.data.id) {
          // Book found in local database
          console.log('[ISBN Lookup] Book found in local database:', response.data);

          // Stop camera when transitioning to book details screen
          if (window.barcodeScannerCleanup) {
            try {
              window.barcodeScannerCleanup();
              console.log('Camera cleanup called when book found in database');
            } catch (error) {
              console.log('Error during camera cleanup:', error);
            }
          }

          setScannerError(translate('This book already exists in the library'));
          setScannerBookFound(true);
          setEditBookData(response.data); // Set data for existing book
          bookFoundLocally = true;
        }
        // Case 1: Book data is in the 'book' property (common format from the API)
        else if (response.data && response.data.book) {
          // Book found from external lookup
          bookData = response.data.book;
          console.log('[ISBN Lookup] Book found in nested structure:', bookData);

          // Stop camera when transitioning to book details screen
          if (window.barcodeScannerCleanup) {
            try {
              window.barcodeScannerCleanup();
              console.log('Camera cleanup called when book found from external API');
            } catch (error) {
              console.log('Error during camera cleanup:', error);
            }
          }

          // Book is available in the external API
          setScannerBookFound(true);
          setNewBookData({
            title: bookData.title || '',
            author: bookData.author || '',
            isbn: isbn,
            description: bookData.description || '',
            publisher: bookData.publisher || '',
            published_year: bookData.published_year || bookData.publishedDate || '',
            image_url: bookData.cover_image || bookData.image_url ||
              // Check for direct imageLinks
              (bookData.imageLinks ? (bookData.imageLinks.thumbnail || bookData.imageLinks.smallThumbnail) : '') ||
              // Check for Google Books API specific structure
              (bookData.volumeInfo && bookData.volumeInfo.imageLinks ? (bookData.volumeInfo.imageLinks.thumbnail || bookData.volumeInfo.imageLinks.smallThumbnail) : '') ||
              '',
            category: '', // Default category
            total_copies: 1, // Default copies
            image_source: 'url' // Default to URL source, can be changed to 'upload'
          });
          setSuccessMessage(translate('Book information found! Please review and save.'));
        }
        // Case 2: Book data is directly in the response (fallback)
        else if (response.data && response.data.title) {
          bookData = response.data;
          console.log('[ISBN Lookup] Book found directly in response:', bookData);

          // Stop camera when transitioning to book details screen
          if (window.barcodeScannerCleanup) {
            try {
              window.barcodeScannerCleanup();
              console.log('Camera cleanup called when book found directly in response');
            } catch (error) {
              console.log('Error during camera cleanup:', error);
            }
          }

          setScannerBookFound(true);
          setNewBookData({
            title: bookData.title || '',
            author: bookData.author || '',
            isbn: isbn,
            description: bookData.description || '',
            publisher: bookData.publisher || '',
            published_year: bookData.published_year || '',
            // Extract image URL from various possible locations and assign to both image_url and cover_image
            image_url: bookData.image_url || bookData.cover_image ||
              // Check for direct imageLinks
              (bookData.imageLinks ? (bookData.imageLinks.thumbnail || bookData.imageLinks.smallThumbnail) : '') ||
              // Check for Google Books API specific structure
              (bookData.volumeInfo && bookData.volumeInfo.imageLinks ? (bookData.volumeInfo.imageLinks.thumbnail || bookData.volumeInfo.imageLinks.smallThumbnail) : '') ||
              // Check for nested structure from the API response
              (bookData.googleBooks && bookData.googleBooks.imageLinks ? (bookData.googleBooks.imageLinks.thumbnail || bookData.googleBooks.imageLinks.smallThumbnail) : '') ||
              '',
            cover_image: bookData.image_url || bookData.cover_image ||
              // Check for direct imageLinks
              (bookData.imageLinks ? (bookData.imageLinks.thumbnail || bookData.imageLinks.smallThumbnail) : '') ||
              // Check for Google Books API specific structure
              (bookData.volumeInfo && bookData.volumeInfo.imageLinks ? (bookData.volumeInfo.imageLinks.thumbnail || bookData.volumeInfo.imageLinks.smallThumbnail) : '') ||
              // Check for nested structure from the API response
              (bookData.googleBooks && bookData.googleBooks.imageLinks ? (bookData.googleBooks.imageLinks.thumbnail || bookData.googleBooks.imageLinks.smallThumbnail) : '') ||
              '',
            category: '', // Default category
            total_copies: 1, // Default copies
            image_source: 'url' // Default to URL source, can be changed to 'upload'
          });
          setSuccessMessage(translate('Book information found! Please review and save.'));
        }
        // No book found in the response
        else {
          console.log('[ISBN Lookup] No book data found in response');
          setScannerError(translate('Book not found. Please enter details manually.'));
          setScannerBookFound(false);
          setNewBookData({ // Pre-fill ISBN for manual entry
            title: '',
            author: '',
            isbn: isbn,
            description: '',
            publisher: '',
            published_year: '',
            image_url: '',
            category: '',
            total_copies: 1
          });
        }
      } catch (error) {
        console.error('[ISBN Lookup] Error:', error);
        setScannerError(translate('Error looking up book information. Please try again or enter details manually.'));
        setScannerBookFound(false);
        setNewBookData({ // Pre-fill ISBN for manual entry
          title: '',
          author: '',
          isbn: isbn,
          description: '',
          publisher: '',
          published_year: '',
          image_url: '',
          cover_image: '',
          category: '',
          total_copies: 1
        });
      }
    } finally {
      setScannerLoading(false);
    }
  };

  // Update handleScannedISBN with ISBN validation
  const handleScannedISBN = async () => {
    if (!scannedISBN) {
      setScannerError(translate('Please enter or scan an ISBN'));
      return;
    }

    // Clean the ISBN (remove spaces, hyphens)
    const cleanISBN = scannedISBN.replace(/[- ]/g, '');

    // Validate the ISBN
    const isbnValidation = isValidISBN(cleanISBN);

    if (!isbnValidation.valid) {
      setScannerError(translate('Invalid ISBN format. Please check and try again.'));
      return;
    }

    setScannerError('');
    setScannerLoading(true);
    setSuccessMessage(''); // Clear previous success message
    setScannerBookFound(false); // Reset book found status
    setNewBookData(null);
    setEditBookData(null);

    // Use the validated ISBN for lookup
    const lookupISBN = isbnValidation.isbn;

    let bookFoundLocally = false;

    try {
      // === Step 1: Check Local Database ===
      console.log('[Local Check] Looking up ISBN:', lookupISBN);
      try {
        // Make request without modifying the baseURL that's already set in App.js
      const localResponse = await api.direct.get(`/api/books/isbn/${lookupISBN}`);

      if (localResponse.data && localResponse.data.id) {
        // Book found in local database
          console.log('[Local Check] Book found:', localResponse.data);

        // Stop camera when transitioning to book details screen
        if (window.barcodeScannerCleanup) {
          try {
            window.barcodeScannerCleanup();
            console.log('Camera cleanup called when book found in local database');
          } catch (error) {
            console.log('Error during camera cleanup:', error);
          }
        }

        setScannerError(translate('This book already exists in the library'));
        setScannerBookFound(true);
          setEditBookData(localResponse.data); // Set data for existing book
          setNewBookData(null); // Clear new book data
          bookFoundLocally = true;
        } else {
           // Even if status is 200, if no data/ID, treat as not found locally
          console.log('[Local Check] Received 200 but no book data found, proceeding to external lookup.');
        }

      } catch (localError) {
        // Specifically handle 404 as "not found locally"
        if (localError.response && localError.response.status === 404) {
          console.log('[Local Check] Book not found locally (404). Proceeding to external lookup.');
          // This is expected if the book isn't in our DB, so no error message yet.
        } else {
          // Handle other errors during local check (network, server error, etc.)
          console.error('[Local Check] Error:', localError);
          setScannerError(translate('Error checking local library. Please try again or verify the barcode was scanned correctly.'));
        setScannerLoading(false);
          return; // Stop processing if local check failed unexpectedly
        }
      }

      // === Step 2: Check External API (if not found locally) ===
      if (!bookFoundLocally) {
        console.log('[External Lookup] Book not found locally, checking external API...');
        try {
          // Add debug information
          console.log('[External Lookup] Requesting from:', api.direct.baseURL + '/api/books/lookup/' + lookupISBN);
          // Make request with proper URL structure
          const response = await api.direct.get(`/api/books/lookup/${lookupISBN}`, {
            // Ensure authorization header is included
            headers: {
              'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
            }
          });
          console.log('[External Lookup] Response:', response.data);

      if (response.data && response.data.title) {
        // Book found in Google Books API
            console.log('[External Lookup] Book found:', response.data);

        // Stop camera when transitioning to book details screen
        if (window.barcodeScannerCleanup) {
          try {
            window.barcodeScannerCleanup();
            console.log('Camera cleanup called when book found in external lookup');
          } catch (error) {
            console.log('Error during camera cleanup:', error);
          }
        }

        setScannerBookFound(true);
            setNewBookData({ // Set data for a *new* book entry
          title: response.data.title,
          author: response.data.author || '',
          isbn: lookupISBN,
          description: response.data.description || '',
          publisher: response.data.publisher || '',
          published_year: response.data.published_year || '',
          image_url: response.data.image_url || response.data.cover_image ||
              // Check for nested imageLinks in various possible locations
              (response.data.imageLinks ? (response.data.imageLinks.thumbnail || response.data.imageLinks.smallThumbnail) : '') ||
              (response.data.book && response.data.book.imageLinks ? (response.data.book.imageLinks.thumbnail || response.data.book.imageLinks.smallThumbnail) : '') ||
              // Check for Google Books API specific structure
              (response.data.googleBooks && response.data.googleBooks.imageLinks ? (response.data.googleBooks.imageLinks.thumbnail || response.data.googleBooks.imageLinks.smallThumbnail) : '') ||
              // Check for nested volumeInfo structure from Google Books API
              (response.data.volumeInfo && response.data.volumeInfo.imageLinks ? (response.data.volumeInfo.imageLinks.thumbnail || response.data.volumeInfo.imageLinks.smallThumbnail) : '') ||
              '',
          cover_image: response.data.image_url || response.data.cover_image ||
              // Check for nested imageLinks in various possible locations
              (response.data.imageLinks ? (response.data.imageLinks.thumbnail || response.data.imageLinks.smallThumbnail) : '') ||
              (response.data.book && response.data.book.imageLinks ? (response.data.book.imageLinks.thumbnail || response.data.book.imageLinks.smallThumbnail) : '') ||
              // Check for Google Books API specific structure
              (response.data.googleBooks && response.data.googleBooks.imageLinks ? (response.data.googleBooks.imageLinks.thumbnail || response.data.googleBooks.imageLinks.smallThumbnail) : '') ||
              // Check for nested volumeInfo structure from Google Books API
              (response.data.volumeInfo && response.data.volumeInfo.imageLinks ? (response.data.volumeInfo.imageLinks.thumbnail || response.data.volumeInfo.imageLinks.smallThumbnail) : '') ||
              '',
              category: '', // Default category
              total_copies: 1 // Default copies
        });
            setEditBookData(null); // Clear existing book data
            setSuccessMessage(translate('Book information found! Please review and save.'));
      } else {
        // Book not found in Google Books API
            console.log('[External Lookup] Book not found.');
        setScannerError(translate('Book not found. Please verify the barcode was scanned correctly or enter details manually.'));
        setScannerBookFound(false);
            setNewBookData({ // Pre-fill ISBN for manual entry
          title: '',
          author: '',
          isbn: lookupISBN,
          description: '',
          publisher: '',
          published_year: '',
          image_url: '',
          category: '',
          total_copies: 1
        });
        setEditBookData(null);
      }
        } catch (externalError) {
           console.error('[External Lookup] Error:', externalError);
           // Handle 404 on external lookup specifically
           if (externalError.response && externalError.response.status === 404) {
             setScannerError(translate('Book not found. Please verify the barcode was scanned correctly or enter details manually.'));
           } else {
             setScannerError(translate('Error during book lookup. Please try again or verify the barcode was scanned correctly.'));
           }
      setScannerBookFound(false);
           setNewBookData({ // Pre-fill ISBN for manual entry even on error
        title: '',
        author: '',
        isbn: lookupISBN,
        description: '',
        publisher: '',
        published_year: '',
        image_url: '',
        category: '',
        total_copies: 1
      });
      setEditBookData(null);
         }
      }

    } finally {
      // This finally block runs regardless of success or caught errors within the try
      setScannerLoading(false);
    }
  };

  // Function to upload book cover image with image resizing
  const uploadBookCover = async (file) => {
    if (!file) return null;

    try {
      // Resize and compress the image
      const resizedImage = await resizeAndCompressImage(file);

      // Convert the image to base64 instead of uploading to server
      // This is a workaround for the server upload issues
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          // The result is the base64 string
          const base64String = reader.result;
          console.log('Image converted to base64 successfully');
          resolve(base64String);
        };
        reader.onerror = (error) => {
          console.error('Error converting image to base64:', error);
          reject(error);
        };
        reader.readAsDataURL(resizedImage);
      });

      // Commented out server upload code that was causing 404 errors
      /*
      // Create form data for the file upload
      const formData = new FormData();
      formData.append('coverImage', resizedImage);

      // Upload the file to the server
      const token = localStorage.getItem('token');

      try {
        console.log('Attempting to upload file to server...');
        const response = await api.direct.post('/api/upload/coverImage', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
          maxContentLength: 5 * 1024 * 1024, // 5MB limit
          maxBodyLength: 5 * 1024 * 1024
        });

        // Return the URL path to the uploaded file
        if (response.data && response.data.filePath) {
          console.log('File uploaded successfully, server returned path:', response.data.filePath);
          return response.data.filePath;
        } else {
          throw new Error('Server did not return file path');
        }
      } catch (error) {
        console.error('Error uploading image to server:', error);
        throw error;
      }
      */
    } catch (error) {
      console.error('Error processing book cover:', error);
      showSnackbar(translate('Failed to process cover image'), 'warning');
      throw error;
    }
  };

  // Convert data URL to File
  const dataURLtoFile = async (dataUrl, filename) => {
    const res = await fetch(dataUrl);
    const blob = await res.blob();
    return new File([blob], filename, { type: blob.type });
  };

  // Function to resize and compress image
  const resizeAndCompressImage = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Create canvas
          const canvas = document.createElement('canvas');

          // Set maximum dimensions
          const MAX_WIDTH = 600;
          const MAX_HEIGHT = 900;

          // Calculate new dimensions while maintaining aspect ratio
          let width = img.width;
          let height = img.height;

          if (width > height) {
            if (width > MAX_WIDTH) {
              height = Math.round(height * (MAX_WIDTH / width));
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width = Math.round(width * (MAX_HEIGHT / height));
              height = MAX_HEIGHT;
            }
          }

          // Set canvas dimensions
          canvas.width = width;
          canvas.height = height;

          // Draw image on canvas
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);

          // Convert canvas to blob with compression
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Canvas to Blob conversion failed'));
              return;
            }

            // Create a new file from the blob
            const newFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            });

            resolve(newFile);
          }, 'image/jpeg', 0.7); // 70% quality for JPEG
        };

        img.onerror = () => {
          reject(new Error('Error loading image'));
        };

        img.src = event.target.result;
      };

      reader.onerror = () => {
        reject(new Error('Error reading file'));
      };

      reader.readAsDataURL(file);
    });
  };

  // Function to reset the scanner form
  const resetScannerBookForm = () => {
    console.log('Resetting scanner form...');

    // Don't cleanup camera here since we're going back to scanner view
    // The camera should continue running for the user to scan again
    setScannerBookFound(false);
    setNewBookData(null);
    setBookCoverPreview('');
    setCoverImageFile(null);
    setScannerError('');
    setSuccessMessage('');
  };



  // Category management functions
  const handleOpenCategoryDialog = (category = null) => {
    if (category) {
      // Handle both object and string formats
      if (typeof category === 'object') {
        setEditCategoryId(category.id);
        setEditCategoryName(category.name);
      } else {
        // For string categories, we don't have an ID
        setEditCategoryId(null);
        setEditCategoryName(category);
      }
    } else {
      setEditCategoryId(null);
      setNewCategory('');
    }
    setCategoryDialogOpen(true);
  };

  const handleCloseCategoryDialog = () => {
    setCategoryDialogOpen(false);
    setNewCategory('');
    setEditCategoryId(null);
    setEditCategoryName('');
  };

  const handleCategoryChange = (e) => {
    if (editCategoryId) {
      setEditCategoryName(e.target.value);
    } else {
      setNewCategory(e.target.value);
    }
  };

  const handleAddCategory = async () => {
    const categoryName = editCategoryId ? editCategoryName.trim() : newCategory.trim();
    if (!categoryName) {
      showSnackbar(translate('Category name is required'), 'error');
      return;
    }

    // Check if category already exists locally to prevent unnecessary API call
    if (!editCategoryId && categories.some(c => c.name === categoryName)) {
      showSnackbar(translate('Category already exists'), 'error');
      return;
    }

    setCategoryLoading(true);
    try {
      const token = localStorage.getItem('token');

      if (editCategoryId) {
        // Update existing category
        await api.direct.put(`/api/books/categories/${editCategoryId}`,
          { name: categoryName },
          { headers: { 'Authorization': `Bearer ${token}` } }
        );
        showSnackbar(translate('Category updated successfully'), 'success');
      } else {
        const response = await api.direct.post('/api/books/categories',
          { name: categoryName },
          { headers: { 'Authorization': `Bearer ${token}` } }
        );

        console.log('Category creation response:', response.data);
        showSnackbar(translate('Category added successfully'), 'success');

        // Immediately update local state with the new category
        if (response.data && response.data.name) {
          setCategories(prev => [...prev, { id: response.data.id, name: response.data.name }]);
        }
      }

      // Refresh categories from server to ensure consistency
      await fetchCategories();
      setCategoryDialogOpen(false);
      setNewCategory('');
      setEditCategoryId(null);
      setEditCategoryName('');
    } catch (error) {
      console.error('Category operation failed:', {
        error: error.response?.data || error.message,
        status: error.response?.status
      });

      const errorMessage = error.response?.data?.message ||
        translate(editCategoryId ? 'Failed to update category' : 'Failed to add category');
      showSnackbar(errorMessage, 'error');
    } finally {
      setCategoryLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm(translate('Are you sure you want to delete this category?'))) {
      return;
    }

    setCategoryLoading(true);
    try {
      const token = localStorage.getItem('token');
      await api.direct.delete(`/api/books/categories/${categoryId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      await fetchCategories();
      showSnackbar(translate('Category deleted successfully'), 'success');
    } catch (error) {
      console.error('Error deleting category:', error);
      showSnackbar(translate('Failed to delete category'), 'error');
    } finally {
      setCategoryLoading(false);
    }
  };



  // Add responsive styles for the action buttons
  const actionButtonStyle = {
    mb: { xs: 1, sm: 0 },
    mx: { xs: 1, sm: 1 },
    minWidth: { xs: '100%', sm: 'auto' }
  };

  const mobileActionButtonStyle = {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    py: 1.5,
    px: 2,
    '& .MuiButton-startIcon': {
      marginRight: 2
    }
  };

  const theme = useTheme();

  return (
    <Box sx={{ width: '100%' }}>
      <Paper elevation={3} sx={{ mb: 4, p: 3 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'stretch', sm: 'center' },
          gap: { xs: 2, sm: 1 }
        }}>
          <Typography
            variant="h6"
            component="h2"
            sx={{
              fontSize: { xs: '1.125rem', sm: '1.25rem' }
            }}
          >
            {translate('Book Management')}
          </Typography>

          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 2 },
            width: { xs: '100%', sm: 'auto' }
          }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenAddBookDialog}
              sx={{
                ...actionButtonStyle,
                ...mobileActionButtonStyle,
                display: 'flex'
              }}
            >
              {translate('Add Book')}
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              startIcon={<QrCodeScannerIcon />}
              onClick={handleOpenScannerDialog}
              sx={{
                ...actionButtonStyle,
                ...mobileActionButtonStyle,
                display: 'flex'
              }}
            >
              {translate('Scan ISBN')}
            </Button>

            <Button
              variant="outlined"
              color="info"
              startIcon={<CloudUploadIcon sx={{ transform: 'translateY(-1px)' }} />}
              onClick={handleOpenImportDialog}
              sx={{
                ...actionButtonStyle,
                ...mobileActionButtonStyle,
                display: 'flex'
              }}
            >
              {translate('Import')}
            </Button>
          </Box>
        </Box>
      </Paper>

      <Typography variant="h4" gutterBottom>
        {translate('Book Management')}
      </Typography>

      <Box sx={{ mb: 2 }}>
        <AppBar position="static" color="default" elevation={1}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label={translate('Books List')} {...a11yProps(0)} />
            <Tab label={translate('Categories')} {...a11yProps(1)} />
          </Tabs>
        </AppBar>
      </Box>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Books Tab */}
      <TabPanel value={tabValue} index={0}>
        <Paper elevation={2} sx={{ mb: 3, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12}>
              <TextField
                fullWidth
                placeholder={translate('Search by title, author, ISBN...')}
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton size="small" onClick={() => setSearchTerm('')}>
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
          </Grid>
        </Paper>

        <Paper elevation={3}>
          {booksLoading ? (
            <Box sx={{ p: 2 }}>
              <LinearProgress />
              <Typography align="center" sx={{ mt: 2 }}>
                {translate('Loading books...')}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer sx={{ maxHeight: 440 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>{translate('Title')}</TableCell>
                      <TableCell>{translate('Author')}</TableCell>
                      <TableCell>{translate('ISBN')}</TableCell>
                      <TableCell>{translate('Category')}</TableCell>
                      <TableCell align="center">{translate('Copies')}</TableCell>
                      <TableCell align="center">{translate('Available')}</TableCell>
                      <TableCell align="right">{translate('Actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredBooks
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((book) => (
                        <TableRow key={book.id} hover>
                          <TableCell component="th" scope="row">
                            {book.title}
                          </TableCell>
                          <TableCell>{book.author}</TableCell>
                          <TableCell>{book.isbn}</TableCell>
                          <TableCell>
                            {book.category?.name || book.category || ''}
                          </TableCell>
                          <TableCell align="center">
                            {book.total_copies || 1}
                          </TableCell>
                          <TableCell align="center">
                            {book.available_copies > 0 ? (
                              <CheckCircleIcon color="success" />
                            ) : (
                              <CancelIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell align="right">
                            <IconButton
                              color="primary"
                              onClick={() => handleOpenEditBookDialog(book)}
                              size="small"
                              title={translate('Edit')}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              color="error"
                              onClick={() => handleDeleteBook(book.id)}
                              size="small"
                              title={translate('Delete')}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredBooks.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          {translate('No books found')}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={filteredBooks.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage={translate('Rows per page:')}
              />
            </>
          )}
        </Paper>
      </TabPanel>

      {/* Categories Tab */}
      <TabPanel value={tabValue} index={1}>
        <Paper elevation={2} sx={{ mb: 3, p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">{translate('Book Categories')}</Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenCategoryDialog()}
              size="large"
              sx={{ minWidth: 150, px: 3, py: 1.2 }}
            >
              {translate('Add Category')}
            </Button>
          </Box>
        </Paper>

        <Paper elevation={3}>
          {categoryLoading ? (
            <Box sx={{ p: 2 }}>
              <LinearProgress />
              <Typography align="center" sx={{ mt: 2 }}>
                {translate('Loading categories...')}
              </Typography>
            </Box>
          ) : (
            <List>
              {categories.length > 0 ? (
                categories.map(category => (
                  <ListItem key={category.id}>
                    <ListItemText primary={category.name} />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        color="error"
                        onClick={() => handleDeleteCategory(category.id)}
                        size="small"
                        title={translate('Delete')}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))
              ) : (
                <Typography>{translate('No categories found')}</Typography>
              )}
            </List>
          )}
        </Paper>
      </TabPanel>

      {/* ISBN Scanner Dialog */}
      <Dialog
        open={scannerDialogOpen}
        onClose={handleCloseScannerDialog}
        fullWidth
        maxWidth="md"
        fullScreen={useMediaQuery(theme.breakpoints.down('sm'))}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pr: 1  // Reduced right padding to accommodate close button
        }}>
          {scannerBookFound
            ? translate('Add New Book')
            : translate('Scan or Enter ISBN')}
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseScannerDialog}
            aria-label={translate('Close')}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {scannerBookFound ? (
            // Book found view - show form with prefilled data
            <Box sx={{ mt: 2 }}>
              {editBookData ? (
                // Book exists in our database
                <Alert severity="info" sx={{ mb: 2 }}>
                  {translate('This book already exists in the library')}
                </Alert>
              ) : (
                // New book form with improved layout
                <Grid container spacing={3}>
                  {/* Left side - Cover and image controls */}
                  <Grid item xs={12} md={4}>
                    <Paper
                      elevation={2}
                      sx={{
                        p: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: 2,
                        height: '100%'
                      }}
                    >
                      {/* Book cover preview with improved handling */}
                      <Box sx={{
                        width: '100%',
                        aspectRatio: '2/3',
                        display: 'flex',
                        justifyContent: 'center',
                        mb: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        overflow: 'hidden',
                        position: 'relative'
                      }}>
                        {bookCoverPreview || newBookData.image_url || newBookData.cover_image ? (
                          <img
                            src={bookCoverPreview || newBookData.image_url || newBookData.cover_image}
                            alt={newBookData.title || translate('Book Cover')}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'contain',
                              maxWidth: '450px',
                            }}
                            onError={(e) => {
                              console.error('Error loading image:', e);
                              e.target.src = '/placeholder-cover.svg'; // Fallback image
                            }}
                          />
                        ) : (
                          <BookCover
                            src={null}
                            alt={translate('No Cover Available')}
                            height="100%"
                          />
                        )}
                      </Box>

                      {/* Image source toggle */}
                      <ToggleButtonGroup
                        value={newBookData.image_source || 'url'}
                        exclusive
                        onChange={handleImageSourceChange}
                        color="primary"
                        fullWidth
                        sx={{ mb: 2 }}
                      >
                        <ToggleButton value="url">
                          {translate('Use URL')}
                        </ToggleButton>
                        <ToggleButton value="upload">
                          {translate('Upload Image')}
                        </ToggleButton>
                      </ToggleButtonGroup>

                      {/* Image upload controls */}
                      <Grid container spacing={2} alignItems="center">
                        {newBookData.image_source === 'upload' ? (
                          <Grid item xs={12}>
                            <Button
                              variant="outlined"
                              component="label"
                              startIcon={<CloudUploadIcon />}
                              fullWidth
                            >
                              {translate('Upload Cover')}
                              <input
                                type="file"
                                accept="image/*"
                                hidden
                                onChange={handleCoverFileChange}
                              />
                            </Button>
                          </Grid>
                        ) : (
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label={translate('Image URL')}
                              value={newBookData.image_url || ''}
                              onChange={handleImageUrlChange}
                              placeholder="https://..."
                              helperText={translate('URL of the book cover image')}
                            />
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Grid>

                  {/* Right side - Book form fields */}
                  <Grid item xs={12} md={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          name="title"
                          label={translate('Title')}
                          value={newBookData.title || ''}
                          onChange={(e) => setNewBookData({...newBookData, title: e.target.value})}
                          fullWidth
                          required
                          error={!newBookData.title}
                          helperText={!newBookData.title ? translate('Title is required') : ''}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          name="author"
                          label={translate('Author')}
                          value={newBookData.author || ''}
                          onChange={(e) => setNewBookData({...newBookData, author: e.target.value})}
                          fullWidth
                          required
                          error={!newBookData.author}
                          helperText={!newBookData.author ? translate('Author is required') : ''}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="isbn"
                          label={translate('ISBN')}
                          value={newBookData.isbn || ''}
                          onChange={(e) => setNewBookData({...newBookData, isbn: e.target.value})}
                          fullWidth
                          placeholder="Optional"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel id="category-select-label">{translate('Category')}</InputLabel>
                          <Select
                            labelId="category-select-label"
                            name="category"
                            value={newBookData.category || ''}
                            onChange={(e) => setNewBookData({...newBookData, category: e.target.value})}
                            label={translate('Category')}
                          >
                            <MenuItem value="">{translate('None')}</MenuItem>
                            {categories.map(category => (
                              <MenuItem key={category.id} value={category.name}>
                                {category.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="publisher"
                          label={translate('Publisher')}
                          value={newBookData.publisher || ''}
                          onChange={(e) => setNewBookData({...newBookData, publisher: e.target.value})}
                          fullWidth
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="published_year"
                          label={translate('Publication Year')}
                          value={newBookData.published_year || ''}
                          onChange={(e) => setNewBookData({...newBookData, published_year: e.target.value})}
                          fullWidth
                          type="number"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="total_copies"
                          label={translate('Total Copies')}
                          value={newBookData.total_copies || 1}
                          onChange={(e) => setNewBookData({...newBookData, total_copies: e.target.value})}
                          fullWidth
                          type="number"
                          InputProps={{ inputProps: { min: 1 } }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          name="image_url"
                          label={translate('Cover Image URL')}
                          value={newBookData.image_url || ''}
                          onChange={handleImageUrlChange}
                          fullWidth
                          placeholder="https://..."
                          disabled={newBookData.image_source !== 'url'}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          name="description"
                          label={translate('Description')}
                          value={newBookData.description || ''}
                          onChange={(e) => setNewBookData({...newBookData, description: e.target.value})}
                          fullWidth
                          multiline
                          rows={3}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              )}
            </Box>
          ) : (
            // Scanner view with improved layout
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={7}>
                  <Paper
                    elevation={2}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      overflow: 'hidden',
                      height: 400,
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative'
                    }}
                  >
                    {/* Scanner with loading overlay */}
                    {scannerLoading && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'rgba(255, 255, 255, 0.8)',
                          zIndex: 10
                        }}
                      >
                        <CircularProgress size={60} sx={{ mb: 2 }} />
                        <Typography variant="h6" align="center">
                          {translate('Searching for book...')}
                        </Typography>
                      </Box>
                    )}

                    {/* We need to ensure the scanner container fills the full height */}
                    <Box sx={{
                      flexGrow: 1,
                      position: 'relative',
                      minHeight: 350, // Ensure minimum height for the scanner
                      '& canvas, & video': { height: '100% !important' } // Force canvas/video to full height
                    }}>
                      {barcodeDetected && !scannerLoading && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            backgroundColor: 'success.main',
                            color: 'success.contrastText',
                            padding: '16px 24px',
                            borderRadius: '8px',
                            textAlign: 'center',
                            zIndex: 5,
                            maxWidth: '80%',
                            fontSize: '20px',
                            fontWeight: 'bold',
                            boxShadow: 4
                          }}
                        >
                          {translate('✓ Barcode Detected!')}
                        </Box>
                      )}

                      {/* Only render BarcodeScanner when not showing book details */}
                      {!scannerBookFound && (
                        <BarcodeScanner
                          embedded={true}
                          onDetected={(result) => {
                            console.log("Barcode detected from scanner with full result:", result);
                            if (result && result.isbn) {
                              // Process the detected ISBN
                              const detectedISBN = result.isbn.trim();
                              console.log("Detected ISBN:", detectedISBN);

                              // Set the ISBN in the state
                              setScannedISBN(detectedISBN);

                              // Show immediate feedback
                              setBarcodeDetected(true);
                              setSuccessMessage(translate('Barcode detected! Looking up ISBN...'));

                              // Call the handler directly with the detected ISBN
                              // This handler will be called after a 1-second delay in the scanner
                              handleScannedISBNWithValue(detectedISBN);

                              // Reset the detector state after a delay
                              setTimeout(() => setBarcodeDetected(false), 2000);
                            } else {
                              console.error("Received invalid barcode result:", result);
                            }
                          }}
                          onClose={() => {}}
                        />
                      )}

                      {/* Show message when scanner is hidden */}
                      {scannerBookFound && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '100%',
                            bgcolor: 'grey.100',
                            borderRadius: 1
                          }}
                        >
                          <Typography variant="body1" color="text.secondary">
                            {translate('Scanner stopped - Book details loaded')}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={5}>
                  <Paper
                    elevation={2}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      p: 3,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <Typography variant="h6" gutterBottom sx={{ mb: 3, textAlign: 'center' }}>
                      {translate('Manual Entry')}
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1, gap: 3 }}>
                      <TextField
                        fullWidth
                        value={scannedISBN}
                        onChange={handleISBNChange}
                        placeholder={translate('Enter ISBN (optional)')}
                        variant="outlined"
                        label={translate('ISBN')}
                        InputProps={{
                          endAdornment: scannedISBN && (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setScannedISBN('')}
                                edge="end"
                              >
                                <ClearIcon />
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />

                      <Typography variant="body2" color="text.secondary">
                        {translate('Enter an ISBN to auto-fill book details, or leave blank to enter manually.')}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 2, mt: 'auto' }}>
                        <Button
                          variant="contained"
                          onClick={() => handleScannedISBNWithValue(scannedISBN)}
                          disabled={scannerLoading}
                          fullWidth
                          startIcon={<SearchIcon />}
                        >
                          {translate('Search ISBN')}
                        </Button>

                        <Button
                          variant="outlined"
                          onClick={() => handleScannedISBNWithValue('')}
                          fullWidth
                          startIcon={<AddIcon />}
                        >
                          {translate('Create Empty')}
                        </Button>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>

              {/* Feedback messages */}
              <Box sx={{ mt: 2 }}>
                {scannerError && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {scannerError}
                  </Alert>
                )}

                {successMessage && !scannerError && !scannerBookFound && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    {successMessage}
                  </Alert>
                )}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          {scannerBookFound && !editBookData ? (
            // Actions for new book form
            <>
              <Button onClick={resetScannerBookForm} color="inherit">
                {translate('Back')}
              </Button>
              <Button
                onClick={handleSaveScannedBookWithCover}
                color="primary"
                variant="contained"
                disabled={isSavingScannedBook}
              >
                {isSavingScannedBook ? translate('Saving...') : translate('Save Book')}
              </Button>
            </>
          ) : (
            // Actions for scanner view or existing book view
            <Button onClick={handleCloseScannerDialog} color="inherit">
              {translate('Close')}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Category Dialog */}
      <Dialog
        open={categoryDialogOpen}
        onClose={handleCloseCategoryDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editCategoryId
            ? translate('Edit Category')
            : translate('Add New Category')}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label={translate('Category Name')}
            value={editCategoryId ? editCategoryName : newCategory}
            onChange={handleCategoryChange}
            margin="normal"
            required
            autoFocus
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCategoryDialog} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleAddCategory}
            color="primary"
            variant="contained"
            disabled={categoryLoading}
          >
            {categoryLoading
              ? translate('Saving...')
              : (editCategoryId ? translate('Update') : translate('Add'))}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Book Dialog */}
      <Dialog
        open={addBookDialogOpen}
        onClose={handleCloseAddBookDialog}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {editBookId ? translate('Edit Book') : translate('Add Book')}
        </DialogTitle>
        <DialogContent>
          <BookForm
            bookData={newBook}
            categories={categories}
            onSave={handleSaveBook}
            onCancel={handleCloseAddBookDialog}
            isLoading={loading}
          />
        </DialogContent>
      </Dialog>

      {/* Import Books Dialog */}
      <Dialog
        open={importDialogOpen}
        onClose={handleCloseImportDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>{translate('Import Books from CSV')}</DialogTitle>
        <DialogContent>
          <Typography paragraph sx={{ mt: 2 }}>
            {translate('Upload a CSV file with the following columns:')}
          </Typography>
          <Typography variant="body2" component="div">
            <Box component="ul" sx={{ pl: 2 }}>
              <li>title (required)</li>
              <li>author (required)</li>
              <li>isbn</li>
              <li>description</li>
              <li>category</li>
              <li>publisher</li>
              <li>published_year</li>
              <li>total_copies</li>
            </Box>
          </Typography>
          <Box sx={{ mt: 3 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<CloudUploadIcon />}
            >
              {translate('Select CSV File')}
              <input
                type="file"
                accept=".csv"
                hidden
                onChange={handleCsvFileChange}
              />
            </Button>
            {csvFile && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                {translate('Selected file:')} {csvFile.name}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseImportDialog} color="inherit">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleImportBooks}
            color="primary"
            variant="contained"
            disabled={!csvFile || loading}
          >
            {loading ? translate('Importing...') : translate('Import')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BookManagement;