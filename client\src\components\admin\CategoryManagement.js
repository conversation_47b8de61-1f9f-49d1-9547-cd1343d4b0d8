import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import axios from 'axios';

const CategoryManagement = ({ itemVariants }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [categoryName, setCategoryName] = useState('');

  // Fetch categories on component mount
  React.useEffect(() => {
    fetchCategories();
  }, []);

  // Load categories
  const fetchCategories = async () => {
    setLoading(true);
    try {
      console.log('Fetching categories for CategoryManagement...');
      const res = await axios.get('/api/books/categories');
      console.log('Categories API response:', res.data);
      
      // Process category data based on response format
      let categoriesData = [];
      if (Array.isArray(res.data)) {
        // Direct array format (new approach)
        categoriesData = res.data.map(cat => {
          // For display, convert string to object with id and name
          if (typeof cat === 'string') {
            return { id: cat, name: cat };
          }
          // Or use the object as is if it already has id and name
          return cat;
        });
      } else if (res.data && Array.isArray(res.data.categories)) {
        // Legacy format with nested categories property
        categoriesData = res.data.categories.map(cat => {
          if (typeof cat === 'string') {
            return { id: cat, name: cat };
          }
          return cat;
        });
      }
      
      console.log('Processed categories:', categoriesData);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  // Open dialog for adding a new category
  const handleAddCategory = () => {
    setIsEditing(false);
    setCurrentCategory(null);
    setCategoryName('');
    setDialogOpen(true);
  };

  // Open dialog for editing a category
  const handleEditCategory = (category) => {
    setIsEditing(true);
    setCurrentCategory(category);
    setCategoryName(typeof category === 'object' ? category.name : category);
    setDialogOpen(true);
  };

  // Open dialog for confirming category deletion
  const handleDeleteConfirm = (category) => {
    setCurrentCategory(category);
    setDeleteDialogOpen(true);
  };

  // Close the category form dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Close the delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setCurrentCategory(null);
  };

  // Add/Edit category
  const handleSaveCategory = async () => {
    if (!categoryName.trim()) {
      setError('Category name is required');
      return;
    }
    
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      
      if (editingCategory) {
        // Update existing category
        await axios.put(`/books/categories/${editingCategory.id}`, 
          { name: categoryName }, 
          { headers: { 'Authorization': `Bearer ${token}` } }
        );
      } else {
        // Add new category
        await axios.post('/books/categories', 
          { name: categoryName }, 
          { headers: { 'Authorization': `Bearer ${token}` } }
        );
      }
      
      fetchCategories();
      setDialogOpen(false);
      setCategoryName('');
      setEditingCategory(null);
    } catch (error) {
      console.error('Error saving category:', error);
      setError('Failed to save category');
    } finally {
      setSaving(false);
    }
  };

  // Delete a category
  const handleDeleteCategory = async () => {
    if (!currentCategory) return;
    
    try {
      setLoading(true);
      setError('');
      setSuccess('');
      
      // Get the category ID or name
      const categoryId = typeof currentCategory === 'object' ? currentCategory.id : null;
      const categoryName = typeof currentCategory === 'object' ? currentCategory.name : currentCategory;
      
      // Find the category ID if we don't have it
      if (!categoryId) {
        const categoriesRes = await axios.get('/api/books/categories');
        const allCategories = categoriesRes.data.categories;
        const category = allCategories.find(cat => {
          return typeof cat === 'object' 
            ? cat.name === categoryName
            : cat === categoryName;
        });
        
        if (category) {
          // Delete the category
          await axios.delete(`/books/categories/${category.id}`);
          setSuccess('Category deleted successfully');
        } else {
          throw new Error('Category not found');
        }
      } else {
        // We have the ID directly
        await axios.delete(`/books/categories/${categoryId}`);
        setSuccess('Category deleted successfully');
      }
      
      // Refresh categories list
      await fetchCategories();
      
      // Close dialog
      handleCloseDeleteDialog();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete category. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div variants={itemVariants}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}
      
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" component="h2">
          Book Categories ({categories.length})
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddCategory}
        >
          Add Category
        </Button>
      </Box>
      
      {loading && categories.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Category Name</TableCell>
                <TableCell>Books Count</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {categories.map((category) => (
                <TableRow key={typeof category === 'object' ? category.name : category}>
                  <TableCell>{typeof category === 'object' ? category.name : category}</TableCell>
                  <TableCell>
                    {typeof category === 'object' && category.count !== undefined ? (
                      <Chip 
                        size="small"
                        label={`${category.count} book${category.count !== 1 ? 's' : ''}`}
                        color={category.count > 0 ? 'primary' : 'default'}
                        variant="outlined"
                      />
                    ) : (
                      <Chip 
                        size="small" 
                        label="Count not available" 
                        variant="outlined" 
                      />
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditCategory(category)}
                      title="Edit Category"
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteConfirm(category)}
                      title="Delete Category"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {categories.length === 0 && (
                <TableRow>
                  <TableCell colSpan={2} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      No categories found. Add your first category!
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Category Form Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog}>
        <DialogTitle>
          {isEditing ? 'Edit Category' : 'Add New Category'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, pt: 1 }}>
            <TextField
              fullWidth
              label="Category Name"
              value={categoryName}
              onChange={(e) => setCategoryName(e.target.value)}
              margin="normal"
              autoFocus
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSaveCategory}
            color="primary"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : (isEditing ? 'Update' : 'Add')}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the category "{currentCategory}"? This action cannot be undone.
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            Note: This will not remove the category from books that are already using it.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button
            onClick={handleDeleteCategory}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default CategoryManagement;