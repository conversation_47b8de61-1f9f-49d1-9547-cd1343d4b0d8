// Load environment variables from .env file
const dotenv = require('dotenv');
const path = require('path');

// Configure dotenv with the path to the .env file in the server directory
const envPath = path.resolve(__dirname, '.env');
console.log('Loading .env from:', envPath);
dotenv.config({ path: envPath });

// Log environment variables loading status
console.log('Environment variables loaded:', Object.keys(process.env).includes('GOOGLE_BOOKS_API_KEY') ? 'GOOGLE_BOOKS_API_KEY exists' : 'GOOGLE_BOOKS_API_KEY missing');

// Import Firebase Admin (but don't initialize again - it's handled in the module)
const firebaseAdmin = require('./utils/firebaseAdmin');

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const fs = require('fs');
const fileUpload = require('express-fileupload');

// Import routes
const { router: authRoutes, authenticateToken } = require('./routes/auth');
const booksRoutes = require('./routes/books');
const booksIsbnRoute = require('./routes/books/isbn');
const leasesRoutes = require('./routes/leases');
const usersRoutes = require('./routes/users');
const { router: securityRoutes, trackIPRequests, securityModeCheck, trackLoginAttempt, getSecurityStats } = require('./routes/security');
const suggestionsRoutes = require('./routes/suggestions');
const databaseRoutes = require('./routes/database');

const app = express();
const PORT = process.env.NODE_ENV === 'production' ? (process.env.PORT || 80) : 8080;

// Configure CORS
app.use(cors({
  origin: ['http://localhost', 'http://localhost:80', 'http://localhost:8080', 'http://127.0.0.1', 'http://127.0.0.1:80', 'http://127.0.0.1:8080'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Log all requests for debugging
app.use((req, res, next) => {
  console.log(`[SERVER] ${req.method} ${req.url}`);
  next();
});

// Database setup
const dbPath = path.resolve(__dirname, 'database', 'bookleasing.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
  } else {
    console.log('Connected to the SQLite database');
    initializeDatabase();
  }
});

// Make database available to routes
app.locals.db = db;

// Middleware
app.use(express.json());
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`);
  // Log authorization header (without the actual token)
  if (req.headers.authorization) {
    console.log(`Authorization header present: ${req.headers.authorization.substring(0, 15)}...`);
  } else {
    console.log('No Authorization header');
  }
  next();
});

// Apply security tracking middleware early
app.use(trackIPRequests);

// Apply security mode check middleware
app.use(securityModeCheck);

// Serve static files from the React app in production
// This must be before API routes to take precedence
console.log(`NODE_ENV is set to: "${process.env.NODE_ENV}"`);
if (process.env.NODE_ENV === 'production') {
  console.log('Production mode detected - setting up static file serving');
  const clientBuildPath = path.join(__dirname, '../client/build');
  console.log(`Static files will be served from: ${clientBuildPath}`);
  console.log(`Checking if directory exists: ${fs.existsSync(clientBuildPath)}`);

  // List what's in the build directory
  if (fs.existsSync(clientBuildPath)) {
    console.log('Contents of build directory:');
    fs.readdirSync(clientBuildPath).forEach(file => {
      console.log(` - ${file}`);
    });
  }

  // Serve static files from the React app build directory
  app.use(express.static(clientBuildPath));
}

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create directory for book covers if it doesn't exist
    const bookCoversDir = path.join(uploadsDir, 'book_covers');
    if (!fs.existsSync(bookCoversDir)) {
      fs.mkdirSync(bookCoversDir, { recursive: true });
    }
    cb(null, bookCoversDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'book-cover-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: function (req, file, cb) {
    // Accept images only (case-insensitive)
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Add fileupload middleware
app.use(fileUpload({
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
  createParentPath: true,
  useTempFiles: true,
  tempFileDir: '/tmp/'
}));

// Initialize database tables
function initializeDatabase() {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    is_banned INTEGER DEFAULT 0,
    ban_reason TEXT,
    google_id TEXT,
    profile_image TEXT,
    email_verified INTEGER DEFAULT 0,
    class_teacher TEXT,
    grade TEXT,
    profile_completed INTEGER DEFAULT 0,
    firebase_uid TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating users table:', err.message);
      return;
    }
    console.log('Users table created or already exists');

    // Add new columns if they don't exist (for existing databases)
    db.all("PRAGMA table_info(users)", (err, columns) => {
      if (err) {
        console.error('Error checking table structure:', err.message);
        return;
      }

      const columnNames = columns.map(col => col.name);

      // Add class_teacher column if it doesn't exist
      if (!columnNames.includes('class_teacher')) {
        db.run('ALTER TABLE users ADD COLUMN class_teacher TEXT', (err) => {
          if (err) {
            console.error('Error adding class_teacher column:', err.message);
          } else {
            console.log('Added class_teacher column to users table');
          }
        });
      }

      // Add grade column if it doesn't exist
      if (!columnNames.includes('grade')) {
        db.run('ALTER TABLE users ADD COLUMN grade TEXT', (err) => {
          if (err) {
            console.error('Error adding grade column:', err.message);
          } else {
            console.log('Added grade column to users table');
          }
        });
      }

      // Add firebase_uid column if it doesn't exist
      if (!columnNames.includes('firebase_uid')) {
        db.run('ALTER TABLE users ADD COLUMN firebase_uid TEXT', (err) => {
          if (err) {
            console.error('Error adding firebase_uid column:', err.message);
          } else {
            console.log('Added firebase_uid column to users table');
          }
        });
      }

      // Add profile_completed column if it doesn't exist
      if (!columnNames.includes('profile_completed')) {
        db.run('ALTER TABLE users ADD COLUMN profile_completed INTEGER DEFAULT 0', (err) => {
          if (err) {
            console.error('Error adding profile_completed column:', err.message);
          } else {
            console.log('Added profile_completed column to users table');
          }
        });
      }
    });

    // Check if google_id column exists and add it if it doesn't
    db.all("PRAGMA table_info(users)", (err, columns) => {
      if (err) {
        console.error('Error checking table schema:', err.message);
        return;
      }

      // Check if google_id column exists
      const googleIdExists = columns.some(col => col.name === 'google_id');
      const profileImageExists = columns.some(col => col.name === 'profile_image');
      const emailVerifiedExists = columns.some(col => col.name === 'email_verified');
      const isBannedExists = columns.some(col => col.name === 'is_banned');
      const banReasonExists = columns.some(col => col.name === 'ban_reason');

      if (!googleIdExists) {
        console.log('Adding missing google_id column to users table...');
        db.run('ALTER TABLE users ADD COLUMN google_id TEXT', (err) => {
          if (err) {
            console.error('Error adding google_id column:', err.message);
          } else {
            console.log('Successfully added google_id column to users table');
          }
        });
      }

      if (!profileImageExists) {
        console.log('Adding missing profile_image column to users table...');
        db.run('ALTER TABLE users ADD COLUMN profile_image TEXT', (err) => {
          if (err) {
            console.error('Error adding profile_image column:', err.message);
          } else {
            console.log('Successfully added profile_image column to users table');
          }
        });
      }

      if (!emailVerifiedExists) {
        console.log('Adding missing email_verified column to users table...');
        db.run('ALTER TABLE users ADD COLUMN email_verified INTEGER DEFAULT 0', (err) => {
          if (err) {
            console.error('Error adding email_verified column:', err.message);
          } else {
            console.log('Successfully added email_verified column to users table');
          }
        });
      }

      if (!isBannedExists) {
        console.log('Adding missing is_banned column to users table...');
        db.run('ALTER TABLE users ADD COLUMN is_banned INTEGER DEFAULT 0', (err) => {
          if (err) {
            console.error('Error adding is_banned column:', err.message);
          } else {
            console.log('Successfully added is_banned column to users table');
          }
        });
      }

      if (!banReasonExists) {
        console.log('Adding missing ban_reason column to users table...');
        db.run('ALTER TABLE users ADD COLUMN ban_reason TEXT', (err) => {
          if (err) {
            console.error('Error adding ban_reason column:', err.message);
          } else {
            console.log('Successfully added ban_reason column to users table');
          }
        });
      }

      // Removed unused password reset and verification token columns

      // Initialize book_suggestions table if it doesn't exist
      db.run(`CREATE TABLE IF NOT EXISTS book_suggestions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        isbn TEXT NOT NULL,
        title TEXT NOT NULL,
        author TEXT NOT NULL,
        description TEXT,
        cover_image TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        rejection_reason TEXT,
        created_at TEXT NOT NULL,
        processed_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`, (err) => {
        if (err) {
          console.error('Error creating book_suggestions table:', err.message);
        } else {
          console.log('book_suggestions table initialized');
        }
      });

      // Categories table
      db.run(`CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`, (err) => {
        if (err) {
          console.error('Error creating categories table:', err.message);
          return;
        }
        console.log('Categories table created or already exists');

        // Books table
        db.run(`CREATE TABLE IF NOT EXISTS books (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          author TEXT NOT NULL,
          isbn TEXT UNIQUE,
          category TEXT,
          description TEXT,
          cover_image TEXT,
          available_copies INTEGER DEFAULT 1,
          total_copies INTEGER DEFAULT 1,
          published_year TEXT,
          publisher TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
          if (err) {
            console.error('Error creating books table:', err.message);
            return;
          }
          console.log('Books table created or already exists');

          // Leases table
          db.run(`CREATE TABLE IF NOT EXISTS leases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            book_id INTEGER NOT NULL,
            lease_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            due_date DATETIME NOT NULL,
            return_date DATETIME,
            status TEXT DEFAULT 'pending',
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (book_id) REFERENCES books (id)
          )`, (err) => {
            if (err) {
              console.error('Error creating leases table:', err.message);
              return;
            }
            console.log('Leases table created or already exists');

            // Check if necessary columns exist in the leases table
            db.all("PRAGMA table_info(leases)", (err, columns) => {
              if (err) {
                console.error('Error checking leases table schema:', err.message);
                return;
              }

              // Check for each required column
              const approvalStatusExists = columns.some(col => col.name === 'approval_status');
              const approvalDateExists = columns.some(col => col.name === 'approval_date');
              const rejectionReasonExists = columns.some(col => col.name === 'rejection_reason');
              const customMessageExists = columns.some(col => col.name === 'custom_message');

              if (!approvalStatusExists) {
                console.log('Adding missing approval_status column to leases table...');
                db.run('ALTER TABLE leases ADD COLUMN approval_status TEXT DEFAULT "pending"', (err) => {
                  if (err) {
                    console.error('Error adding approval_status column:', err.message);
                  } else {
                    console.log('Successfully added approval_status column to leases table');
                  }
                });
              }

              if (!approvalDateExists) {
                console.log('Adding missing approval_date column to leases table...');
                db.run('ALTER TABLE leases ADD COLUMN approval_date DATETIME', (err) => {
                  if (err) {
                    console.error('Error adding approval_date column:', err.message);
                  } else {
                    console.log('Successfully added approval_date column to leases table');
                  }
                });
              }

              if (!rejectionReasonExists) {
                console.log('Adding missing rejection_reason column to leases table...');
                db.run('ALTER TABLE leases ADD COLUMN rejection_reason TEXT', (err) => {
                  if (err) {
                    console.error('Error adding rejection_reason column:', err.message);
                  } else {
                    console.log('Successfully added rejection_reason column to leases table');
                  }
                });
              }

              if (!customMessageExists) {
                console.log('Adding missing custom_message column to leases table...');
                db.run('ALTER TABLE leases ADD COLUMN custom_message TEXT', (err) => {
                  if (err) {
                    console.error('Error adding custom_message column:', err.message);
                  } else {
                    console.log('Successfully added custom_message column to leases table');
                  }
                });
              }

              // Insert some sample data
              insertSampleData();
            });
          });
        });
      });
    });
  });
}

// Insert sample data for testing
function insertSampleData() {
  // Check if we already have data
  db.get('SELECT COUNT(*) as count FROM books', (err, result) => {
    if (err) {
      console.error('Error checking for existing data:', err.message);
      return;
    }

    // Only insert sample data if no books exist
    if (result.count === 0) {
      // Sample books
      const books = [
        {
          title: 'The Great Gatsby',
          author: 'F. Scott Fitzgerald',
          isbn: '9780743273565',
          category: 'Fiction',
          description: 'A story of wealth, love, and the American Dream in the 1920s.',
          cover_image: 'https://covers.openlibrary.org/b/id/8432047-L.jpg',
          available_copies: 5,
          total_copies: 5
        },
        {
          title: 'To Kill a Mockingbird',
          author: 'Harper Lee',
          isbn: '9780061120084',
          category: 'Fiction',
          description: 'A classic of modern American literature about racial inequality.',
          cover_image: 'https://covers.openlibrary.org/b/id/8314135-L.jpg',
          available_copies: 3,
          total_copies: 3
        },
        {
          title: '1984',
          author: 'George Orwell',
          isbn: '9780451524935',
          category: 'Science Fiction',
          description: 'A dystopian novel about totalitarianism and surveillance.',
          cover_image: 'https://covers.openlibrary.org/b/id/8575708-L.jpg',
          available_copies: 2,
          total_copies: 2
        }
      ];

      // Insert books
      const insertBookStmt = db.prepare(`
        INSERT INTO books (title, author, isbn, category, description, cover_image, available_copies, total_copies)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      books.forEach(book => {
        insertBookStmt.run(
          book.title,
          book.author,
          book.isbn,
          book.category,
          book.description,
          book.cover_image,
          book.available_copies,
          book.total_copies
        );
      });

      insertBookStmt.finalize();
      console.log('Sample books inserted successfully');

      // Create an admin user
      bcrypt.hash('admin123', 10, (err, hash) => {
        if (err) {
          console.error('Error hashing password:', err.message);
          return;
        }

        db.run(`
          INSERT INTO users (username, email, password, role)
          VALUES (?, ?, ?, ?)
        `, ['admin', '<EMAIL>', hash, 'admin'], function(err) {
          if (err) {
            console.error('Error creating admin user:', err.message);
          } else {
            console.log('Admin user created successfully');
          }
        });
      });
    }
  });
}

// Make db available to routes
app.locals.db = db;

// Error handling middleware for multer errors
app.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred when uploading
    return res.status(400).json({
      message: 'File upload error',
      error: err.message
    });
  } else if (err) {
    // An unknown error occurred
    console.error('Server error:', err.message);
    return res.status(500).json({
      message: 'Server error',
      error: err.message
    });
  }
  next();
});

// Use API prefix for all routes
const apiPrefix = '/api';

// Log API routes being registered
console.log('Registering API routes:');
console.log(`- ${apiPrefix}/auth`);
console.log(`- ${apiPrefix}/books`);
console.log(`- ${apiPrefix}/books/isbn`);
console.log(`- ${apiPrefix}/leases`);
console.log(`- ${apiPrefix}/users`);
console.log(`- ${apiPrefix}/security`);
console.log(`- ${apiPrefix}/suggestions`);
console.log(`- ${apiPrefix}/admin/database`);

// Add request logging middleware
app.use((req, res, next) => {
  // Log all requests for debugging
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);

  // For API routes, also log the request body (except for password fields)
  if (req.originalUrl.startsWith('/api/') && req.body) {
    const logBody = { ...req.body };

    // Remove sensitive fields for security
    if (logBody.password) logBody.password = '[REDACTED]';
    if (logBody.token) logBody.token = '[REDACTED]';
    if (logBody.oobCode) logBody.oobCode = logBody.oobCode.substring(0, 10) + '...';

    console.log('Request body:', logBody);
  }

  next();
});

// Routes with proper logging
app.use(`${apiPrefix}/auth`, (req, res, next) => {
  console.log(`[server] Auth route: ${req.method} ${req.path}`);
  console.log(`[server] Auth request full URL: ${req.originalUrl}`);
  next();
}, authRoutes);

app.use(`${apiPrefix}/books`, (req, res, next) => {
  console.log(`[server] Books route: ${req.method} ${req.path}`);
  next();
}, booksRoutes);

app.use(`${apiPrefix}/books/isbn`, booksIsbnRoute);
app.use(`${apiPrefix}/leases`, leasesRoutes);
app.use(`${apiPrefix}/users`, usersRoutes);
app.use(`${apiPrefix}/security`, securityRoutes);
app.use(`${apiPrefix}/suggestions`, suggestionsRoutes);
app.use(`${apiPrefix}/admin/database`, databaseRoutes);

// Add temporary fallback for suggestions/remaining endpoint
app.get('/api/suggestion-limit', (req, res) => {
  console.log('Direct suggestion limit endpoint called');
  // Return a default value of 5 remaining suggestions
  res.json({
    remaining: 5,
    count: 0,
    timestamp: Date.now(),
    message: 'Direct response from server'
  });
});

// Add suggestions/remaining endpoint for checking suggestion limits
app.get('/api/suggestions/remaining', authenticateToken, (req, res) => {
  console.log('Suggestions remaining endpoint called');
  const userId = req.user.id;
  const db = req.app.locals.db;

  // Get the count of suggestions by this user in the last week
  const weekAgo = new Date();
  weekAgo.setDate(weekAgo.getDate() - 7);

  db.get(
    'SELECT COUNT(*) as count FROM book_suggestions WHERE user_id = ? AND created_at > ?',
    [userId, weekAgo.toISOString()],
    (err, result) => {
      if (err) {
        console.error('Error checking suggestion count:', err);
        return res.status(500).json({
          error: 'Database error',
          message: 'Could not retrieve suggestion count',
          remaining: 5 // Default to max in case of error
        });
      }

      const count = result ? result.count : 0;
      const remaining = Math.max(0, 5 - count); // Max 5 suggestions per week

      return res.json({
        remaining,
        count,
        timestamp: Date.now()
      });
    }
  );
});

// Add image upload route for book covers
app.post('/api/upload/image', upload.single('coverImage'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Return the URL to the uploaded file
    const imageUrl = `/uploads/book_covers/${req.file.filename}`;

    // Log the upload
    console.log(`Book cover image uploaded: ${imageUrl}`);

    return res.status(200).json({
      imageUrl,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({ message: 'Error uploading file' });
  }
});

// Add book cover image upload endpoint
app.post('/api/upload/cover', authenticateToken, (req, res) => {
  console.log('Book cover upload endpoint called');

  // Check if file was uploaded
  if (req.files && req.files.image) {
    const file = req.files.image;
    const timestamp = Date.now();
    const filename = `cover_${timestamp}_${file.name}`;

    // Return a mock URL
    res.json({
      url: `/images/covers/${filename}`,
      success: true
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }
});

// Make uploads folder accessible
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Root route
app.get('/api', (req, res) => {
  console.log("API root route accessed");
  res.json({ message: 'Welcome to the Book Leasing API. Use /api/auth, /api/books, or /api/leases to access the API endpoints.' });
});

// Debug route for API connectivity testing
app.get('/api/debug', (req, res) => {
  console.log("API debug route accessed");
  res.json({
    success: true,
    timestamp: new Date().toISOString(),
    message: 'API server is running and reachable',
    server: {
      version: process.env.npm_package_version || 'unknown',
      environment: process.env.NODE_ENV || 'development',
      address: req.socket.localAddress,
      port: PORT
    },
    request: {
      method: req.method,
      path: req.path,
      ip: req.ip,
      headers: req.headers
    }
  });
});

// Endpoint to serve Firebase config to the client
app.get('/api/firebase-config', (req, res) => {
  console.log('HIT /api/firebase-config');
  res.json({
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APP_ID,
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
  });
});

// Add catch-all route for production at the end (after all API routes)
if (process.env.NODE_ENV === 'production') {
  const clientBuildPath = path.join(__dirname, '../client/build');

  // For any request that doesn't match one above, send the index.html file
  app.get('*', (req, res) => {
    console.log(`Catch-all route accessed: ${req.path}`);
    res.sendFile(path.join(clientBuildPath, 'index.html'));
  });
}

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on 0.0.0.0:${PORT}`);
});

// Handle process termination
process.on('SIGINT', () => {
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err.message);
    } else {
      console.log('Database connection closed');
    }
    process.exit(0);
  });
});

app.get('/firebase-config', (req, res) => {
  console.log('HIT /firebase-config');
  res.json({
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APP_ID,
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
  });
});