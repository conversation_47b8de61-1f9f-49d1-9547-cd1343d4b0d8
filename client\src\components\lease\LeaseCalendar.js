import React, { useState, useEffect, useMemo, useContext, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  TextField,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  useMediaQuery
} from '@mui/material';
import moment from 'moment';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import RefreshIcon from '@mui/icons-material/Refresh';
import EventIcon from '@mui/icons-material/Event';
import AnnouncementIcon from '@mui/icons-material/Announcement';
import WatchLaterIcon from '@mui/icons-material/WatchLater';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import TodayIcon from '@mui/icons-material/Today';
import { AuthContext } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { useTheme } from '@mui/material/styles';
import api from '../../utils/api';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Set up the localizer for the calendar
const localizer = momentLocalizer(moment);

const LeaseCalendar = ({ bookId = null, isAdmin = false, initialLeases = [] }) => {
  // Add refs to prevent multiple fetches
  const hasInitiallyFetched = useRef(false);
  const hasFetchedBooks = useRef(false);
  const hasFetchedUsers = useRef(false);
  
  const [rawLeaseData, setRawLeaseData] = useState(initialLeases || []);
  const [loading, setLoading] = useState(!initialLeases || initialLeases.length === 0);
  const [error, setError] = useState('');
  const [books, setBooks] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedBookId, setSelectedBookId] = useState(bookId ? String(bookId) : '');
  const [selectedUserId, setSelectedUserId] = useState('');
  const [selectedStatusFilter, setSelectedStatusFilter] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const { user } = useContext(AuthContext);
  const { translate } = useLanguage();
  const theme = useTheme();
  
  const [view, setView] = useState('month');
  const [date, setDate] = useState(new Date());
  
  // Move calendarCss inside component where theme is available
  const calendarCss = `
    .rbc-calendar {
      width: 100%;
      font-family: inherit;
      color: ${theme.palette.text.primary};
      background-color: ${theme.palette.background.paper};
    }
    
    .rbc-toolbar {
      margin-bottom: 10px;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    
    .rbc-toolbar button {
      color: ${theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.primary.main};
      border: 1px solid ${theme.palette.primary.main};
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 0.875rem;
      background-color: ${theme.palette.mode === 'dark' ? theme.palette.action.selected : theme.palette.common.white};
      margin: 0 2px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .rbc-toolbar button:hover {
      background-color: ${theme.palette.primary.main};
      color: ${theme.palette.common.white};
    }
    
    .rbc-toolbar button.rbc-active {
      background-color: ${theme.palette.primary.main};
      color: ${theme.palette.common.white};
    }
    
    .rbc-event {
      background-color: ${theme.palette.primary.main};
      border-radius: 4px;
      padding: 2px 5px;
      font-size: 0.85rem;
    }
    
    .rbc-day-bg.rbc-today {
      background-color: ${theme.palette.mode === 'dark' 
        ? theme.palette.action.selected 
        : `${theme.palette.primary.light}30`};
    }
    
    .rbc-header {
      padding: 8px 3px;
      font-weight: bold;
      font-size: 0.875rem;
      border-bottom: 1px solid ${theme.palette.divider};
    }
    
    .rbc-date-cell {
      padding: 4px;
      font-size: 0.875rem;
      color: ${theme.palette.text.primary};
    }
    
    .rbc-off-range {
      color: ${theme.palette.text.disabled} !important;
    }
    
    .rbc-agenda-empty {
      text-align: center;
      padding: 20px;
      font-style: italic;
      color: ${theme.palette.text.secondary};
    }
    
    .rbc-month-view, .rbc-agenda-view {
      background-color: ${theme.palette.background.paper};
      border: 1px solid ${theme.palette.divider};
    }
    
    .rbc-month-row {
      border-bottom: 1px solid ${theme.palette.divider};
    }
    
    .rbc-day-bg {
      border-right: 1px solid ${theme.palette.divider};
    }
    
    .rbc-time-header-content {
      border-left: 1px solid ${theme.palette.divider};
    }
    
    .rbc-toolbar-label {
      font-weight: bold;
      color: ${theme.palette.text.primary};
    }
    
    /* Mobile optimizations */
    @media (max-width: 600px) {
      .rbc-toolbar {
        flex-direction: column;
        align-items: center;
        gap: 8px;
      }
      
      .rbc-toolbar-label {
        margin: 8px 0;
        font-size: 1rem;
        font-weight: bold;
      }
      
      .rbc-toolbar button {
        padding: 4px 8px;
        font-size: 0.75rem;
      }
      
      .rbc-header {
        padding: 4px 2px;
        font-size: 0.75rem;
      }
      
      .rbc-date-cell {
        font-size: 0.75rem;
      }
      
      .rbc-event {
        font-size: 0.75rem;
        padding: 1px 3px;
      }
    }
  `;
  
  // Replace the calendarStyles object with a simpler one that doesn't use dot notation
  const calendarStyles = {
    height: '100%',
    width: '100%',
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#f5f5f5'
  };

  // Custom day styling to normalize day appearance
  const dayPropGetter = (date) => {
    return {
      style: {
        backgroundColor: theme.palette.mode === 'dark' ? 'transparent' : '#f5f5f5',
        color: theme.palette.text.primary
      }
    };
  };

  // Fetch main lease data - ONLY ONCE on initial mount
  useEffect(() => {
    // Only fetch if we haven't already AND initialLeases are not provided
    if (!hasInitiallyFetched.current && (!initialLeases || initialLeases.length === 0)) {
        hasInitiallyFetched.current = true; // Mark as fetched to prevent future fetches
        
        const fetchLeaseData = async () => {
            setLoading(true);
            setError('');
            try {
                console.log('Fetching lease data (once)...');
                const res = await api.direct.get('/api/leases', {
                  params: { includeAll: true } // Request all leases including rejected, canceled, returned
                });
                const leaseData = res.data?.leases || res.data || [];
                console.log('Lease data fetched (once):', leaseData);
                setRawLeaseData(leaseData);
            } catch (err) {
                setError(translate('Failed to load calendar data. Please try again later.'));
                console.error('Error fetching calendar data:', err);
                setRawLeaseData([]);
            } finally {
                setLoading(false);
            }
        };
        fetchLeaseData();
    } else if (initialLeases && initialLeases.length > 0 && !hasInitiallyFetched.current) {
        // Use provided initial leases only on the first render
        hasInitiallyFetched.current = true;
        setRawLeaseData(initialLeases);
        setLoading(false);
    }
  }, []);

  // Memoize the formatted calendar events
  const calendarData = useMemo(() => {
    console.log('Recalculating calendar events...');
    return rawLeaseData
      .filter(lease => {
        // Apply user filter
        if (isAdmin && selectedUserId !== '') {
          return String(lease.user_id) === selectedUserId;
        }
        return true;
      })
      .filter(lease => {
        // Apply book filter
        if (selectedBookId !== '') {
          return String(lease.book_id) === selectedBookId;
        }
        return true;
      })
      .filter(lease => {
        // Apply status filter
        if (selectedStatusFilter !== '') {
          // Check both status and approval_status
          if (selectedStatusFilter === 'rejected') {
            return lease.approval_status === 'rejected';
          } else if (selectedStatusFilter === 'pending') {
            return lease.approval_status === 'pending';
          } else {
            return lease.status === selectedStatusFilter;
          }
        }
        return true;
      })
      .map(lease => ({
        id: lease.id,
        title: `${lease.title || lease.book_title || translate('Unknown Book')} - ${lease.username || translate('User')}`,
        start: new Date(lease.lease_date),
        end: new Date(lease.due_date),
        allDay: true,
        status: lease.status || 'unknown',
        approval_status: lease.approval_status || 'unknown',
        user_id: lease.user_id,
        username: lease.username,
        resource: lease
      }));
  }, [rawLeaseData, isAdmin, selectedUserId, selectedBookId, selectedStatusFilter, translate]);

  // Fetch books for filter only once
  useEffect(() => {
    const fetchBooks = async () => {
      if (!hasFetchedBooks.current && !bookId) {
        hasFetchedBooks.current = true;
        try {
          const res = await api.direct.get('/api/books');
          setBooks(Array.isArray(res.data) ? res.data : []);
        } catch (err) {
          console.error('Error fetching books:', err);
          setBooks([]);
        }
      }
    };
    
    fetchBooks();
  }, [bookId]);

  // Fetch users for admin filter only once
  useEffect(() => {
    const fetchUsers = async () => {
      if (!hasFetchedUsers.current && isAdmin) {
        hasFetchedUsers.current = true;
        try {
          const res = await api.direct.get('/api/users');
          setUsers(Array.isArray(res.data) ? res.data : []);
        } catch (err) {
          console.error('Error fetching users:', err);
          setUsers([]);
        }
      }
    };
    
    fetchUsers();
  }, [isAdmin]);

  // Memoize the list of expiring leases
  const expiringLeases = useMemo(() => {
    console.log('Recalculating expiring leases...');
    const now = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(now.getDate() + 7);

    return calendarData
      .filter(event => {
        if (!event || !event.end) return false;
        const dueDate = new Date(event.end);
        return dueDate > now && dueDate < nextWeek && event.status === 'active';
      })
      .sort((a, b) => new Date(a.end) - new Date(b.end));
  }, [calendarData]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle book filter change
  const handleBookChange = (event) => {
    setSelectedBookId(event.target.value);
  };

  // Handle user filter change
  const handleUserChange = (event) => {
    setSelectedUserId(event.target.value);
  };

  // Handle status filter change
  const handleStatusChange = (event) => {
    setSelectedStatusFilter(event.target.value);
  };

  // Custom event styling based on status
  const eventStyleGetter = (event) => {
    let backgroundColor = theme.palette.primary.main;
    let fontWeight = 'normal';
    
    if (event.status === 'active') {
      backgroundColor = theme.palette.info.main;
    } else if (event.status === 'returned') {
      backgroundColor = theme.palette.success.main;
    } else if (event.status === 'overdue') {
      backgroundColor = theme.palette.error.main;
    } else if (event.status === 'cancelled' || event.approval_status === 'rejected') {
      backgroundColor = theme.palette.grey[500];
    } else if (event.approval_status === 'pending') {
      backgroundColor = theme.palette.warning.main;
    }

    // Check if lease is expiring soon (within 3 days)
    const now = new Date();
    const timeDiff = event.end - now;
    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    if (event.status === 'active' && daysDiff <= 3 && daysDiff > 0) {
      fontWeight = 'bold';
      backgroundColor = theme.palette.warning.dark;
    }
    
    return {
      style: {
        backgroundColor,
        color: '#fff',
        fontWeight,
        border: 'none',
        display: 'block',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whitespace: 'nowrap'
      }
    };
  };

  // Refresh calendar data
  const handleRefresh = () => {
    hasInitiallyFetched.current = false;
    hasFetchedBooks.current = false;
    hasFetchedUsers.current = false;
    setSelectedBookId(bookId ? String(bookId) : '');
    setSelectedUserId('');
    setSelectedStatusFilter('');
    
    const fetchLeaseData = async () => {
      setLoading(true);
      setError('');
      try {
        const res = await api.direct.get('/api/leases', {
          params: { includeAll: true } // Request all leases including rejected, canceled, returned
        });
        const leaseData = res.data?.leases || res.data || [];
        setRawLeaseData(leaseData);
      } catch (err) {
        setError(translate('Failed to refresh calendar data.'));
        console.error('Error refreshing calendar data:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchLeaseData();
  };

  // Navigation handlers
  const handleNavigate = (newDate) => {
    console.log('Navigate to:', newDate);
    setDate(newDate);
  };
  
  const handleViewChange = (newView) => {
    console.log('View changed to:', newView);
    setView(newView);
  };

  return (
    <Box sx={{ height: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Paper elevation={3} sx={{ p: { xs: 1, sm: 2 }, mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            {translate('Lease Calendar')}
          </Typography>
          
          <Box>
            <Button
              variant={filterOpen ? "contained" : "outlined"}
              startIcon={<FilterAltIcon />}
              size="small"
              onClick={() => setFilterOpen(!filterOpen)}
              sx={{ mr: 1 }}
            >
              {translate('Filters')}
            </Button>
            
            <Tooltip title={translate('Refresh Calendar')}>
              <IconButton onClick={handleRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        {filterOpen && (
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth size="small">
                <InputLabel>{translate('Book')}</InputLabel>
                <Select
                  value={selectedBookId}
                  onChange={handleBookChange}
                  label={translate('Book')}
                >
                  <MenuItem value="">{translate('All Books')}</MenuItem>
                  {books.map(book => (
                    <MenuItem key={book.id} value={String(book.id)}>
                      {book.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {isAdmin && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth size="small">
                  <InputLabel>{translate('User')}</InputLabel>
                  <Select
                    value={selectedUserId}
                    onChange={handleUserChange}
                    label={translate('User')}
                  >
                    <MenuItem value="">{translate('All Users')}</MenuItem>
                    {users.map(user => (
                      <MenuItem key={user.id} value={String(user.id)}>
                        {user.username || user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : translate('Unknown User')}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12} sm={isAdmin ? 4 : 8}>
              <FormControl fullWidth size="small">
                <InputLabel>{translate('Status')}</InputLabel>
                <Select
                  value={selectedStatusFilter}
                  onChange={handleStatusChange}
                  label={translate('Status')}
                >
                  <MenuItem value="">{translate('All Statuses')}</MenuItem>
                  <MenuItem value="active">{translate('Active')}</MenuItem>
                  <MenuItem value="pending">{translate('Pending Approval')}</MenuItem>
                  <MenuItem value="rejected">{translate('Rejected')}</MenuItem>
                  <MenuItem value="returned">{translate('Returned')}</MenuItem>
                  <MenuItem value="overdue">{translate('Overdue')}</MenuItem>
                  <MenuItem value="cancelled">{translate('Cancelled')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        )}
      </Paper>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', height: 'calc(100% - 80px)' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="calendar view tabs">
          <Tab label={translate('Calendar')} />
          <Tab 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AnnouncementIcon fontSize="small" sx={{ mr: 0.5 }} />
                {translate('Expiring Soon')} {expiringLeases.length > 0 && `(${expiringLeases.length})`}
              </Box>
            } 
          />
        </Tabs>
        
        {/* Loading Indicator */}
        {loading && (
          <Box display="flex" justifyContent="center" alignItems="center" height="400px">
            <CircularProgress />
          </Box>
        )}

        {/* Calendar View */}
        {!loading && tabValue === 0 && (
          <Box sx={{ 
            height: { xs: '400px', sm: '500px', md: '550px' }, 
            position: 'relative'
          }}>
            <style>{calendarCss}</style>
            <Tooltip title={translate('Refresh Calendar Data')}>
              <IconButton 
                onClick={handleRefresh}
                size="small"
                sx={{ 
                  position: 'absolute', 
                  top: { xs: -35, sm: -45 }, 
                  right: 0, 
                  zIndex: 10,
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(66, 66, 66, 0.8)' : 'rgba(255, 255, 255, 0.8)',
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(66, 66, 66, 0.9)' : 'rgba(255, 255, 255, 0.9)'
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Calendar
              localizer={localizer}
              events={calendarData}
              startAccessor="start"
              endAccessor="end"
              style={calendarStyles}
              eventPropGetter={eventStyleGetter}
              dayPropGetter={dayPropGetter}
              views={['month', 'agenda']}
              view={view}
              date={date}
              onNavigate={handleNavigate}
              onView={handleViewChange}
              popup
              messages={{
                next: translate('Next'),
                previous: translate('Previous'),
                today: translate('Today'),
                month: translate('Month'),
                week: translate('Week'),
                day: translate('Day'),
                agenda: translate('Agenda'),
                date: translate('Date'),
                time: translate('Time'),
                event: translate('Event'),
                noEventsInRange: translate('No events in this range'),
                showMore: (count) => translate(`+${count} more`)
              }}
            />
          </Box>
        )}

        {/* Expiring Leases List View */}
        {!loading && tabValue === 1 && (
          <Box sx={{ maxHeight: '550px', overflowY: 'auto' }}>
            {expiringLeases.length > 0 ? (
              <Grid container spacing={2}>
                {expiringLeases.map(lease => (
                  <Grid item xs={12} sm={6} md={4} key={`expiring-${lease.id}`}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1">{lease.title}</Typography>
                        <Typography variant="body2" color="text.secondary">Due: {moment(lease.end).format('MMM D, YYYY')}</Typography>
                        {isAdmin && <Typography variant="caption">User: {lease.username}</Typography>}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Typography sx={{ textAlign: 'center', p: 3 }}>{translate('No leases are expiring within the next 7 days.')}</Typography>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default LeaseCalendar;
