const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const PORT = process.env.PORT || 80;

// API proxy for production
app.use('/api', createProxyMiddleware({
  target: process.env.API_URL || 'http://localhost:8080',
  changeOrigin: true,
}));

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'build')));

// For any request that doesn't match one above, send index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`Client server running on port ${PORT}`);
  console.log(`API proxy target: ${process.env.API_URL || 'http://localhost:8080'}`);
}); 