import React, { useState, useEffect } from 'react';
import { Container, Typo<PERSON>, Box, Button, Grid, Card, CardContent, Paper, useTheme, useMediaQuery, Avatar, CircularProgress, Alert } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import api from '../utils/api';
import BookCover from '../components/books/BookCover';
import { useLanguage } from '../context/LanguageContext';

const Home = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const { translate } = useLanguage();
  
  // State for most leased books (using first 5 as placeholder)
  const [mostLeasedBooks, setMostLeasedBooks] = useState([]); // Renamed from featuredBooks
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        // Removed stagger/delay from here as children handle their own animation
      }
    }
  };

  // Fetch most leased books (placeholder: first 5)
  useEffect(() => {
    const fetchMostLeasedBooks = async () => {
      setLoading(true);
      setError('');
      
      try {
        console.log('Fetching most leased books...');
        let data;
        
        // Try the normal API approach first
        try {
          // Use the regular API instance which handles environments correctly
          const response = await api.get('/books');
          console.log('API call succeeded:', response.data);
          data = response.data;
        } catch (apiErr) {
          console.error('API call failed:', apiErr);
          
          // Try with direct API as fallback
          try {
            const directRes = await api.direct.get('/api/books');
            console.log('Direct API call succeeded:', directRes.data);
            data = directRes.data;
          } catch (directErr) {
            console.error('Direct API call failed:', directErr);
            
            // Use relative URL for production environment
            const isProduction = process.env.NODE_ENV === 'production';
            const fallbackUrl = isProduction ? '/api/books' : 'http://localhost:8080/api/books';
            
            // Last resort - use native fetch
            console.log(`Trying native fetch to ${fallbackUrl} as last resort...`);
            const fetchRes = await fetch(fallbackUrl);
            
            if (!fetchRes.ok) {
              throw new Error(`Server responded with ${fetchRes.status}: ${fetchRes.statusText}`);
            }
            
            data = await fetchRes.json();
            console.log('Native fetch succeeded:', data);
          }
        }
        
        // Handle different response formats - ensure we get an array
        let booksData = [];
        if (Array.isArray(data)) {
          booksData = data;
        } else if (data && Array.isArray(data.books)) {
          booksData = data.books;
        } else {
          console.error('Unexpected books response format:', data);
        }
        
        // Take the first 5 books as placeholder for 'most leased'
        const mostLeasedData = booksData.slice(0, 5);
        console.log('Most leased books (placeholder):', mostLeasedData);
        
        setMostLeasedBooks(mostLeasedData);
      } catch (err) {
        console.error('Error fetching most leased books:', err);
        setError('Failed to load most leased books.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMostLeasedBooks();
  }, []);

  // Testimonials data
  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Avid Reader",
      quote: "This book leasing app has transformed how I read. The selection is amazing and the process is so simple!",
      avatar: "https://example.com/sarah-johnson.jpg"
    },
    {
      name: "Michael Chen",
      role: "Book Club Organizer",
      quote: "Our book club relies on this platform for all our monthly picks. The interface is beautiful and user-friendly.",
      avatar: "https://example.com/michael-chen.jpg"
    },
    {
      name: "Emily Rodriguez",
      role: "Student",
      quote: "As a student on a budget, this app lets me access all the books I need without breaking the bank!",
      avatar: "https://example.com/emily-rodriguez.jpg"
    }
  ];

  // Render when loading
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 3 }}>
          {translate('Loading books...')}
        </Typography>
      </Container>
    );
  }

  // Render when error
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 4 }}>
          {translate(error) || translate('Failed to load data.')}
        </Alert>
        {/* Consider rendering other static content even on error */}
      </Container>
    );
  }

  // Render when no most leased books (updated check)
  if (mostLeasedBooks.length === 0 && !loading) { // Added !loading check
    console.log('No most leased books to display');
    // Fallback UI will be handled within the component structure below
  }

  return (
    <motion.div>
      {/* Hero Section with Enhanced Glass Morphism */}
      <Box
        sx={{
          position: 'relative',
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
          color: 'white',
          py: { xs: 8, md: 12 },
          mb: 0,
          borderRadius: { xs: 0, md: '0 0 30px 30px' },
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Enhanced Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.15,
            background: `
              radial-gradient(circle at 20% 30%, ${theme.palette.secondary.light} 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, ${theme.palette.primary.light} 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, white 0%, transparent 70%),
              linear-gradient(60deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%)
            `,
            animation: 'pulse 15s infinite alternate',
            '@keyframes pulse': {
              '0%': { opacity: 0.1 },
              '100%': { opacity: 0.2 }
            }
          }}
        />
        
        {/* Floating Particles */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            overflow: 'hidden',
            pointerEvents: 'none'
          }}
        >
          {[...Array(6)].map((_, i) => (
            <Box
              key={i}
              sx={{
                position: 'absolute',
                width: ['20px', '30px', '40px', '50px', '35px', '25px'][i],
                height: ['20px', '30px', '40px', '50px', '35px', '25px'][i],
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(5px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                top: [`${10 + i * 15}%`, `${60 - i * 10}%`, `${20 + i * 10}%`, `${70 - i * 5}%`, `${40 + i * 8}%`, `${30 - i * 7}%`][i],
                left: [`${5 + i * 15}%`, `${80 - i * 12}%`, `${30 + i * 10}%`, `${60 - i * 8}%`, `${20 + i * 12}%`, `${90 - i * 10}%`][i],
                animation: `float${i} ${8 + i * 2}s infinite ease-in-out alternate`,
                '@keyframes float0': {
                  '0%': { transform: 'translateY(0) rotate(0deg)' },
                  '100%': { transform: 'translateY(-20px) rotate(10deg)' }
                },
                '@keyframes float1': {
                  '0%': { transform: 'translateY(0) rotate(0deg)' },
                  '100%': { transform: 'translateY(15px) rotate(-8deg)' }
                },
                '@keyframes float2': {
                  '0%': { transform: 'translateX(0) rotate(0deg)' },
                  '100%': { transform: 'translateX(20px) rotate(15deg)' }
                },
                '@keyframes float3': {
                  '0%': { transform: 'translateX(0) rotate(0deg)' },
                  '100%': { transform: 'translateX(-15px) rotate(-12deg)' }
                },
                '@keyframes float4': {
                  '0%': { transform: 'translate(0, 0) rotate(0deg)' },
                  '100%': { transform: 'translate(10px, -10px) rotate(20deg)' }
                },
                '@keyframes float5': {
                  '0%': { transform: 'translate(0, 0) rotate(0deg)' },
                  '100%': { transform: 'translate(-10px, 15px) rotate(-15deg)' }
                }
              }}
            />
          ))}
        </Box>
        
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div>
                <Typography 
                  variant="h2" 
                  component="h1" 
                  gutterBottom
                  sx={{
                    fontWeight: 700,
                    background: `linear-gradient(45deg, ${theme.palette.primary.light}, ${theme.palette.secondary.light})`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 3
                  }}
                >
                  {translate('Discover Your Next Great Read')}
                </Typography>
                <Typography variant="h5" paragraph sx={{ mb: 4 }}>
                  {translate('Explore our curated collection of books and lease them with just a few clicks.')}
                </Typography>
                <Button
                  component={RouterLink}
                  to="/books"
                  variant="contained"
                  color="secondary"
                  size="large"
                  sx={{ 
                    mt: 2,
                    px: 4,
                    py: 1.5,
                    borderRadius: 8,
                    boxShadow: '0 8px 20px rgba(156, 39, 176, 0.3)',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: '0 12px 25px rgba(156, 39, 176, 0.4)'
                    }
                  }}
                >
                  {translate('Browse Books')}
                </Button>
              </motion.div>
            </Grid>
            
            {/* Most Leased Books Stack Section */}
            <Grid item xs={12} md={6}>
              <Box sx={{ 
                position: 'relative', 
                height: { xs: 380, md: 450 }, 
                mt: { xs: 6, md: 0 },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                {mostLeasedBooks.length > 0 ? (
                    <motion.div
                      style={{
                      position: 'relative',
                      width: '400px', // Further increased width for more spread
                      height: '450px', // Further increased height for more spread
                        display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    {mostLeasedBooks.map((book, index) => {
                      // Calculate rotation and offsets for stacked appearance
                      const isEven = index % 2 === 0;
                      const rotation = isEven 
                        ? -8 + (index * 1.2) 
                        : 8 - (index * 1.2);
                      const xOffset = isEven 
                        ? -40 + (index * 5) 
                        : 40 - (index * 5);
                      const yOffset = index * 25; // Even more vertical spread
                      
                      return (
                        <motion.div
                          key={book.id || index}
                          initial={{ 
                            opacity: 0, 
                            y: 50,
                            rotateZ: rotation * 2, 
                          }}
                          animate={{ 
                            opacity: 1,
                            y: yOffset,
                            x: xOffset,
                            rotateZ: rotation,
                            scale: 1 - (index * 0.015), // Lighter scale difference for less overlap
                            zIndex: mostLeasedBooks.length - index
                          }}
                          whileHover={{
                            y: -30, // Move even higher when hovered
                            rotateZ: 0,
                            scale: 1.1, // Bigger scale on hover
                            zIndex: 30,
                            boxShadow: '0px 30px 60px rgba(0, 0, 0, 0.5)',
                            transition: { duration: 0.2, ease: 'easeOut' }, // Faster transition
                            hover: true
                          }}
                          transition={{
                            type: 'spring',
                            stiffness: 400, // Higher stiffness for snappier animation
                            damping: 25,
                            delay: index * 0.05 // Faster stagger
                          }}
                          onClick={() => window.location.href = `/books/${book.id}`}
                          style={{
                            width: '220px',
                            height: '320px',
                            position: 'absolute',
                            cursor: 'pointer',
                            transformOrigin: 'center bottom',
                          }}
                          // Faster and more dramatic movement of other books
                          onHoverStart={() => {
                            document.querySelectorAll('.book-card').forEach(el => {
                              if (el.getAttribute('data-book-index') !== index.toString()) {
                                const bookIndex = parseInt(el.getAttribute('data-book-index'));
                                // Much more dramatic movement
                                if (bookIndex < index) {
                                  el.style.transform = `translate(${isEven ? '-75px' : '75px'}, -50px) rotate(${rotation * 2}deg)`;
                                } 
                                else {
                                  el.style.transform = `translate(${isEven ? '75px' : '-75px'}, 50px) rotate(${rotation * 2}deg)`;
                                }
                                // Faster transition
                                el.style.transition = 'transform 0.15s ease-out';
                              }
                            });
                          }}
                          onHoverEnd={() => {
                            document.querySelectorAll('.book-card').forEach(el => {
                              el.style.transform = '';
                              // Faster return animation
                              el.style.transition = 'transform 0.25s ease';
                            });
                          }}
                          className="book-card"
                          data-book-index={index}
                        >
                          <Paper
                            elevation={5}
                          sx={{ 
                              width: '100%',
                              height: '100%',
                              backgroundImage: book.cover_image ? `url(${book.cover_image})` : 'none',
                              backgroundSize: 'cover',
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat',
                            display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'flex-end',
                              borderRadius: 2,
                              position: 'relative',
                              overflow: 'hidden',
                              boxShadow: theme => `0 10px 30px ${theme.palette.mode === 'dark' 
                                ? 'rgba(0, 0, 0, 0.6)' 
                                : 'rgba(0, 0, 0, 0.15)'
                              }`,
                              transition: 'all 0.3s ease',
                              '&:after': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                background: 'linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.8) 100%)',
                                pointerEvents: 'none',
                              }
                            }}
                          >
                            {/* Fallback content if image doesn't load */}
                            {!book.cover_image && (
                              <Box sx={{ 
                                p: 2, 
                                bgcolor: 'background.paper', 
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                textAlign: 'center',
                                background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
                              }}>
                                <Typography variant="h6" sx={{ color: 'white', mb: 1, fontWeight: 600 }}>
                                  {book.title}
                                </Typography>
                                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                                  {book.author}
                                </Typography>
                              </Box>
                            )}
                            
                            {/* Book info overlay at bottom */}
                            <Box
                              sx={{ 
                                position: 'absolute',
                                bottom: 0,
                                width: '100%',
                                p: 2,
                                zIndex: 2,
                              }}
                            >
                              <Typography 
                                variant="subtitle1" 
                                sx={{ 
                                  color: 'white', 
                                  display: 'block',
                                  textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                                  fontWeight: 600,
                                  mb: 0.5
                                }}
                              >
                            {book.title}
                          </Typography>
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  color: 'rgba(255,255,255,0.8)', 
                                  display: 'block',
                                  textShadow: '0 1px 3px rgba(0,0,0,0.5)',
                                }}
                              >
                                {book.author}
                              </Typography>
                            </Box>
                          </Paper>
                        </motion.div>
                      );
                    })}
                  </motion.div>
                ) : (
                  // Fallback when no books are available
                  <Paper 
                    elevation={3}
                    sx={{ 
                      p: 3, 
                      width: '80%', 
                      height: 'auto',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 2,
                      backgroundColor: 'rgba(0, 0, 0, 0.2)', // Darker, more Vercel-like
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      textAlign: 'center'
                    }}
                  >
                    <Typography variant="h6" gutterBottom sx={{ color: 'white' }}>
                      {translate('No popular books available yet')}
                          </Typography>
                          <Button
                            component={RouterLink}
                      to="/books"
                            variant="outlined"
                      color="secondary" 
                      sx={{ mt: 2, borderColor: 'rgba(255, 255, 255, 0.5)', color: 'white' }}
                          >
                      {translate('Browse Catalog')}
                          </Button>
                      </Paper>
                )}
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Testimonials Section - KEEP */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h4" align="center" gutterBottom sx={{ mb: 6 }}>
            {translate('What Our Users Say')}
          </Typography>
        <Grid container spacing={4} justifyContent="center">
          {testimonials.map((testimonial, index) => (
            <Grid key={index} xs={12} md={4}>
              <motion.div 
                whileHover={{ y: -10, transition: { duration: 0.3 } }}
              >
                <Paper
                  elevation={0}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    p: 4,
                    backgroundColor: 'background.paper',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: theme.shape.borderRadius * 0.7,
                    transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '5px',
                      background: index === 0 
                        ? `linear-gradient(90deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})` 
                        : index === 1 
                          ? `linear-gradient(90deg, ${theme.palette.success.main}, ${theme.palette.success.light})` 
                          : `linear-gradient(90deg, ${theme.palette.info.main}, ${theme.palette.info.light})`
                    },
                    '&:hover': {
                      borderColor: '#555'
                    }
                  }}
                >
                  <Typography variant="body1" sx={{ fontStyle: 'italic', mb: 3, color: 'text.secondary' }}>
                    "{testimonial.quote}"
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                    <Avatar
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      sx={{ mr: 2, width: 56, height: 56 }}
                    />
                    <Box>
                      <Typography variant="h6" component="div" sx={{ color: 'text.primary' }}>
                        {testimonial.name}
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {testimonial.role}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </Container>
    </motion.div>
  );
};

export default Home;