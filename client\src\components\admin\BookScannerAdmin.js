import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Alert,
  IconButton,
  CircularProgress,
  Container,
  Stack,
  Divider,
  Snackbar,
  Collapse,
  useMediaQuery,
  useTheme,
  Tabs,
  Tab,
  ToggleButtonGroup,
  ToggleButton,
  TextField
} from '@mui/material';
import {
  Close as CloseIcon,
  AddCircleOutline as AddIcon,
  QrCodeScanner as ScannerIcon,
  CameraAlt as CameraIcon,
  Check as CheckIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Numbers as NumbersIcon
} from '@mui/icons-material';
import api from '../../utils/api';
import BarcodeScanner from '../books/BarcodeScanner';
import TextRecognizer from '../books/TextRecognizer';
import BookForm from '../books/BookForm';
import { useLanguage } from '../../context/LanguageContext';

const BookScannerAdmin = () => {
  // State variables
  const [showScanner, setShowScanner] = useState(false);
  const [scannerKey, setScannerKey] = useState(Date.now());
  const [activeTab, setActiveTab] = useState(0);
  const [categories, setCategories] = useState([]);
  const [bookData, setBookData] = useState(null);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  const [error, setError] = useState('');
  const [showEditForm, setShowEditForm] = useState(false);
  const [isSavingBook, setIsSavingBook] = useState(false);
  const [manualIsbn, setManualIsbn] = useState('');
  const [showIsbnInput, setShowIsbnInput] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [scannerType, setScannerType] = useState('barcode'); // 'barcode' or 'text'
  
  // Theme and responsive layout
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // Language translation
  const { translate } = useLanguage();
  
  // Display snackbar message
  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  }, []);
  
  // Get all book categories
  const fetchCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('Fetching categories for BookScannerAdmin...');
      const response = await api.direct.get('/api/books/categories');
      
      // Process category data based on response format
      let categoriesData = [];
      if (Array.isArray(response.data)) {
        // Direct array format (new approach)
        categoriesData = response.data.map(cat => {
          // Handle both {id, name} format and direct string format
          return typeof cat === 'string' ? cat : (cat.name || cat);
        });
      } else if (response.data && response.data.categories) {
        // Legacy format with nested categories property
        categoriesData = response.data.categories.map(cat => {
          return typeof cat === 'string' ? cat : (cat.name || cat);
        });
      }
      
      console.log('Categories fetched:', categoriesData);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
      showSnackbar(translate('Failed to load book categories'), 'error');
      // Set empty array as fallback
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  }, [showSnackbar, translate]);
  
  // Load categories when component mounts
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);
  
  // Handle tab change
  const handleTabChange = useCallback((event, newValue) => {
    setActiveTab(newValue);
    // Hide scanner when switching to manual tab
    if (newValue === 1) {
      setShowScanner(false);
    }
  }, []);
  
  // Change scanner type
  const handleScannerTypeChange = useCallback((event, newScannerType) => {
    if (newScannerType !== null) {
      setScannerType(newScannerType);
      // Restart scanner with new type
      if (showScanner) {
        setScannerKey(Date.now());
      }
      showSnackbar(
        newScannerType === 'barcode' 
          ? translate('Switched to barcode scanner') 
          : translate('Switched to text recognition')
      );
    }
  }, [showScanner, showSnackbar, translate]);
  
  // Start scanner
  const startScanner = useCallback(() => {
    setShowScanner(true);
    setScannerKey(Date.now()); // Force remount of scanner
    setError('');
    setSnackbarMessage('');
  }, []);
  
  // Stop scanner
  const stopScanner = useCallback(() => {
    setShowScanner(false);
  }, []);
  
  // Fetch book details by ISBN
  const fetchBookByISBN = useCallback(async (isbn) => {
    if (!isbn) {
      setError("No ISBN provided");
      return;
    }

    console.log(`BookScannerAdmin.js: Fetching book with ISBN: ${isbn}`);
    setIsLoading(true);
    setError(null);

    try {
      // First check if book exists in our database
      console.log(`Checking if book exists in database: /books/isbn/${isbn}`);
      const existsResponse = await api.direct.get(`/api/books/isbn/${isbn}`);
      
      if (existsResponse.data.success && existsResponse.data.book) {
        // Book found in our database
        console.log("Book found in database:", existsResponse.data.book);
        setBookData(existsResponse.data.book);
        setActiveTab(1); // Switch to edit tab
        showSnackbar(`Book found: ${existsResponse.data.book.title}`, 'success');
        return;
      }
      
      // If not in database, check external APIs
      console.log(`Looking up book in external APIs: /books/isbn/external/${isbn}`);
      const response = await api.direct.get(`/api/books/isbn/external/${isbn}`);
      
      if (response.data.success && response.data.book) {
        // Book found in external API
        console.log("Book found in external API:", response.data.book);
        
        // Add missing properties required for our database
        const bookWithDefaults = {
          ...response.data.book,
          // Add additional fields needed for our database
          quantity: 1,
          available: 1,
          status: 'available',
          category: response.data.book.category || 'Uncategorized',
          location: '',
          notes: '',
          added_date: new Date().toISOString().split('T')[0]
        };
        
        setBookData(bookWithDefaults);
        setActiveTab(1); // Switch to edit tab
        showSnackbar('Book found in external database. Please review the details.', 'info');
      } else {
        // Book not found in any database
        console.log("Book not found in any database");
        setBookData({
          isbn: isbn,
          isbn_10: isbn.length === 10 ? isbn : '',
          isbn_13: isbn.length === 13 ? isbn : '',
          title: '',
          author: '',
          description: '',
          publisher: '',
          published_year: '',
          category: 'Uncategorized',
          cover_image: '',
          quantity: 1,
          available: 1,
          status: 'available',
          location: '',
          notes: '',
          added_date: new Date().toISOString().split('T')[0]
        });
        setActiveTab(1); // Switch to manual entry tab
        showSnackbar('Book not found. Please enter details manually.', 'warning');
      }
    } catch (error) {
      console.error("Error fetching book data:", error);
      setError(`Error fetching book data: ${error.response?.data?.message || error.message}`);
      showSnackbar('Failed to fetch book information', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showSnackbar, setActiveTab, setBookData, setError, setIsLoading, translate]);
  
  // Handle barcode or text detection
  const handleDetection = useCallback((detectedData) => {
    if (!detectedData) {
      console.error("No data detected");
      setError("No data detected");
      return;
    }

    // Log detected data for troubleshooting
    console.log("BookScannerAdmin: Detected data:", detectedData);
    
    // Extract ISBN based on the data format
    let isbn = '';
    
    // Check what format the data is in
    if (typeof detectedData === 'string') {
      isbn = detectedData;
    } else if (detectedData.isbn) {
      // Our standard format
      isbn = detectedData.isbn;
    } else if (detectedData.isbn_13) {
      // ISBN-13 format
      isbn = detectedData.isbn_13;
    } else if (detectedData.isbn_10) {
      // ISBN-10 format
      isbn = detectedData.isbn_10;
    } else if (detectedData.rawResult) {
      // Direct barcode text
      isbn = detectedData.rawResult;
    }
    
    // Clean ISBN (remove hyphens, spaces)
    isbn = isbn.replace(/[-\s]/g, '');
    
    if (!isbn) {
      setError("Could not extract ISBN from detected data");
      return;
    }
    
    console.log(`BookScannerAdmin.js: Fetching book with ISBN: ${isbn}`);

    // Stop scanner first to prevent additional scans
    stopScanner();
    
    // Add a small delay to ensure the scanner is fully stopped before proceeding
    // This helps prevent state conflicts between the scanner and the API call
    setTimeout(() => {
      // Double-check we're not already loading
      if (!isLoading) {
        // Fetch book info using the ISBN
        fetchBookByISBN(isbn);
      } else {
        console.log('Skipping fetchBookByISBN call - already loading');
      }
    }, 100); // Small delay to ensure clean state transition
  }, [fetchBookByISBN, stopScanner, setError, isLoading]);
  
  // Handle manual ISBN input
  const handleManualIsbnSearch = useCallback(() => {
    if (!manualIsbn || manualIsbn.trim() === '') {
      showSnackbar(translate('Please enter an ISBN'), 'error');
      return;
    }
    
    // Clean the ISBN (remove hyphens, spaces)
    const cleanedIsbn = manualIsbn.replace(/[-\s]/g, '');
    
    // Validate ISBN format
    if (!/^(\d{10}|\d{13})$/.test(cleanedIsbn)) {
      showSnackbar(translate('Invalid ISBN format. Please use a 10 or 13-digit ISBN'), 'error');
      return;
    }
    
    // Fetch book info using the ISBN
    fetchBookByISBN(cleanedIsbn);
    
    // Hide the input field after search
    setShowIsbnInput(false);
  }, [manualIsbn, fetchBookByISBN, showSnackbar, translate, setShowIsbnInput]);
  
  // Render scanner based on selected type
  const renderScanner = useCallback(() => {
    if (scannerType === 'barcode') {
      return <BarcodeScanner key={scannerKey} onDetected={handleDetection} onStop={stopScanner} />; 
    } else if (scannerType === 'text') {
      return <TextRecognizer key={scannerKey} onDetected={handleDetection} onStop={stopScanner} />;
    }
    return null;
  }, [scannerKey, scannerType, handleDetection, stopScanner]);
  
  // Save book to database
  const handleSaveBook = useCallback(async (bookToSave, coverFile) => {
    setIsSavingBook(true);
    setError(null);
    
    try {
      console.log('Saving book with data:', bookToSave);

      // Handle new category
      let finalBook = { ...bookToSave };
      
      // Create FormData if we have a cover file
      let formData = null;
      if (coverFile) {
        console.log('Preparing cover file upload:', coverFile.name);
        formData = new FormData();
        
        // Add the cover file under 'coverFile' field name (must match server expectation)
        formData.append('coverFile', coverFile);
        
        // Add the book data as a JSON string in a field called 'book'
        formData.append('book', JSON.stringify(finalBook));
      }

      let savedBook;
      if (finalBook.id) {
        // Update existing book
        if (formData) {
          // Send multipart form data with file
          const response = await api.direct.put(`/api/books/${finalBook.id}`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          savedBook = response.data.book || response.data;
        } else {
          // Send JSON data without file
          const response = await api.direct.put(`/api/books/${finalBook.id}`, finalBook);
          savedBook = response.data.book || response.data;
        }
      } else {
        // Add new book
        if (formData) {
          // Send multipart form data with file
          const response = await api.direct.post('/api/books', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          savedBook = response.data.book || response.data;
        } else {
          // Send JSON data without file
          const response = await api.direct.post('/api/books', finalBook);
          savedBook = response.data.book || response.data;
        }
      }

      console.log('Book saved successfully:', savedBook);
      showSnackbar('Book saved successfully!', 'success');
      setBookData(savedBook); // Update local state with saved book (including ID)
      setActiveTab(0); // Go back to scanner tab
      
      // Refresh categories in case a new one was added
      fetchCategories();
      
    } catch (error) {
      console.error('Error saving book:', error);
      showSnackbar(`Error saving book: ${error.response?.data?.message || error.message}`, 'error');
    } finally {
      setIsSavingBook(false);
    }
  }, [showSnackbar, setIsSavingBook, setError, setBookData, fetchCategories, setActiveTab]);
  
  // Handle snackbar close
  const handleSnackbarClose = useCallback(() => {
    setSnackbarOpen(false);
  }, []);

  // Upload a book cover image
  const uploadCoverImage = async (file) => {
    // Create a form data object
    const formData = new FormData();
    formData.append('coverImage', file);
    
    try {
      const uploadResponse = await api.direct.post('/api/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });
      
      return uploadResponse.data.imageUrl;
    } catch (error) {
      console.error('Error uploading cover image:', error);
      showSnackbar(translate('Failed to upload cover image'), 'error');
      return null;
    }
  };

  // Add a book to the database
  const addBook = async (bookData) => {
    try {
      const response = await api.direct.post('/api/books', bookData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error adding book:', error);
      throw error;
    }
  };

  return (
    <Container 
      maxWidth="lg" 
      sx={{ 
        mt: { xs: 2, sm: 4 }, 
        mb: { xs: 2, sm: 4 },
        px: isMobile ? 1 : 3
      }}
    >
      <Paper elevation={3} sx={{ p: { xs: 2, sm: 3 }, mb: { xs: 2, sm: 4 } }}>
        <Typography variant="h5" gutterBottom>
          {translate('Book Scanner Admin')}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          {translate('Scan book barcodes to fetch information automatically, then add them to your library.')}
        </Typography>
        
        {/* Tabs for scanner/manual entry */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab 
              icon={<ScannerIcon />} 
              label={translate('Start Scanner')} 
              id="scanner-tab"
            />
            <Tab 
              icon={<EditIcon />} 
              label={translate('Add ISBN Manually')} 
              id="manual-tab"
            />
          </Tabs>
          
          {/* Scanner Tab */}
          <Box role="tabpanel" hidden={activeTab !== 0} id="scanner-tab-panel" sx={{ p: 3 }}>
            <Box>
              {!showScanner && (
                <Container maxWidth="sm">
                  <Paper 
                    elevation={3} 
                    sx={{ 
                      p: 3, 
                      mt: 3,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 3
                    }}
                  >
                    <Typography variant="h5" component="h2" gutterBottom>
                      {translate('Book Scanner')}
                    </Typography>
                    
                    <Alert severity="info" sx={{ width: '100%' }}>
                      {translate('Scan a book barcode to quickly add or update book information.')}
                    </Alert>
                    
                    <Stack spacing={1} sx={{ width: '100%' }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {translate('Select scanner type:')}
                      </Typography>
                      
                      <ToggleButtonGroup
                        value={scannerType}
                        exclusive
                        onChange={handleScannerTypeChange}
                        aria-label="scanner type"
                        fullWidth
                        size="small"
                        sx={{ mb: 2 }}
                      >
                        <ToggleButton value="barcode" aria-label="barcode scanner">
                          <ScannerIcon sx={{ mr: 1 }} />
                          {translate('Barcode')}
                        </ToggleButton>
                        <ToggleButton value="text" aria-label="text recognition">
                          <NumbersIcon sx={{ mr: 1 }} />
                          {translate('Numbers')}
                        </ToggleButton>
                      </ToggleButtonGroup>
                    </Stack>
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', gap: 2 }}>
                      <Button
                        variant="contained"
                        color="primary"
                        fullWidth
                        startIcon={scannerType === 'barcode' ? <ScannerIcon /> : <NumbersIcon />}
                        onClick={startScanner}
                      >
                        {scannerType === 'barcode' 
                          ? translate('Scan Barcode') 
                          : translate('Scan Numbers')}
                      </Button>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Divider sx={{ flex: 1 }} />
                        <Typography variant="body2" sx={{ mx: 1 }}>
                          {translate('OR')}
                        </Typography>
                        <Divider sx={{ flex: 1 }} />
                      </Box>

                      <Button
                        variant="outlined"
                        color="primary"
                        fullWidth
                        startIcon={<SearchIcon />}
                        onClick={() => setShowIsbnInput(true)}
                      >
                        {translate('Enter ISBN Manually')}
                      </Button>
                    </Box>
                    
                    <Collapse in={showIsbnInput} sx={{ width: '100%' }}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                        <Stack spacing={2}>
                          <Typography variant="body2">
                            {translate('Enter the 10 or 13-digit ISBN:')}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <input
                              type="text"
                              value={manualIsbn}
                              onChange={(e) => setManualIsbn(e.target.value)}
                              onKeyPress={(e) => e.key === 'Enter' && handleManualIsbnSearch()}
                              placeholder="ISBN"
                              style={{ 
                                flex: 1,
                                padding: '8px 12px',
                                border: '1px solid #ccc',
                                borderRadius: '4px'
                              }}
                            />
                            <Button 
                              variant="contained"
                              color="primary"
                              onClick={handleManualIsbnSearch}
                              disabled={isLoading}
                            >
                              {isLoading ? <CircularProgress size={24} /> : translate('Search')}
                            </Button>
                          </Box>
                        </Stack>
                      </Paper>
                    </Collapse>
                  </Paper>

                  {error && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                      {error}
                    </Alert>
                  )}
                </Container>
              )}
              
              {renderScanner()}
            </Box>
          </Box>
          
          {/* Manual Entry Tab */}
          <Box role="tabpanel" hidden={activeTab !== 1} id="manual-tab-panel" sx={{ p: 3 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box flexGrow={1}>
                <input
                  type="text"
                  value={manualIsbn}
                  onChange={(e) => setManualIsbn(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleManualIsbnSearch()}
                  placeholder={translate('Enter 10 or 13 digit ISBN')}
                  style={{
                    width: '100%',
                    padding: '10px',
                    fontSize: '16px',
                    border: '1px solid #ccc',
                    borderRadius: '4px'
                  }}
                />
              </Box>
              <Button 
                variant="contained" 
                onClick={handleManualIsbnSearch}
                size="large"
              >
                {translate('Search')}
              </Button>
            </Stack>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {translate('Example:')} 9781234567897 {translate('or')} 1234567890
            </Typography>
          </Box>
        </Paper>
        
        {/* Error message */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }}
            action={
              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={() => setError(null)}
              >
                <CloseIcon fontSize="inherit" />
              </IconButton>
            }
          >
            {error}
          </Alert>
        )}
        
        {/* Book form for editing or adding */}
        {bookData && activeTab === 1 && (
          <Box sx={{ mt: 3 }}>
            <BookForm
              bookData={bookData}
              categories={categories}
              onSave={handleSaveBook}
              onCancel={() => {
                setBookData(null);
                setActiveTab(0);
              }}
              isSaving={isSavingBook}
              isLoading={isLoading}
            />
          </Box>
        )}
      </Paper>
      
      {/* Snackbar notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ 
          vertical: 'bottom', 
          horizontal: isMobile ? 'center' : 'right' 
        }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity} 
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default BookScannerAdmin;

