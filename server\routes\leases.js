const express = require('express');
const router = express.Router();
const { authenticateToken } = require('./auth');
const { isAdmin } = require('../middleware/admin');
const { requireCompleteProfile } = require('../middleware/profileCheck');

// Middleware to ensure user is authenticated
// const { authenticateToken } = authRoutes;

// Middleware to check for lease spam attempts
const leaseRequestLimiter = new Map(); // Store user IDs and their recent requests

// Middleware to check if user is verified
const isVerified = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (req.user.email_verified === 0) {
    return res.status(403).json({
      message: 'You need to verify your email before borrowing books',
      error: 'email_not_verified'
    });
  }

  next();
};

function checkLeaseSpam(req, res, next) {
  const userId = req.user.id;
  const now = Date.now();

  // Get user's request history or create new one
  if (!leaseRequestLimiter.has(userId)) {
    leaseRequestLimiter.set(userId, {
      requests: [],
      blocked: false,
      blockedUntil: 0
    });
  }

  const userData = leaseRequestLimiter.get(userId);

  // If user is blocked, check if block has expired
  if (userData.blocked) {
    if (now < userData.blockedUntil) {
      const timeLeft = Math.ceil((userData.blockedUntil - now) / 1000);
      return res.status(429).json({
        message: `Too many lease requests. Please try again later.`,
        timeLeft: timeLeft
      });
    } else {
      // Block expired, reset status
      userData.blocked = false;
      userData.requests = [];
    }
  }

  // Clean up old requests (older than 5 minutes)
  userData.requests = userData.requests.filter(timestamp => now - timestamp < 5 * 60 * 1000);

  // Add current request
  userData.requests.push(now);

  // Check if user has made too many requests (more than 5 in 5 minutes)
  if (userData.requests.length > 5) {
    // Block user for 10 minutes
    userData.blocked = true;
    userData.blockedUntil = now + (10 * 60 * 1000);

    const timeLeft = Math.ceil((userData.blockedUntil - now) / 1000);
    return res.status(429).json({
      message: `Too many lease requests. You're temporarily blocked from making new lease requests.`,
      timeLeft: timeLeft
    });
  }

  // If user made more than 1 request in 30 seconds, enforce delay
  const recentRequests = userData.requests.filter(timestamp => now - timestamp < 30 * 1000);
  if (recentRequests.length > 1) {
    return res.status(429).json({
      message: `Please wait before making another lease request.`,
      timeLeft: 30
    });
  }

  next();
}

router.get('/', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const { status } = req.query;

    let query = `
      SELECT l.*, b.title, b.author, b.cover_image, u.username
      FROM leases l
      JOIN books b ON l.book_id = b.id
      JOIN users u ON l.user_id = u.id
    `;

    const params = [];
    const conditions = [];

    // Regular users can only see their own leases
    if (req.user.role !== 'admin') {
      conditions.push('l.user_id = ?');
      params.push(req.user.id);
    }

    // Filter by status if provided
    if (status) {
      conditions.push('l.status = ?');
      params.push(status);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY l.lease_date DESC';

    db.all(query, params, (err, leases) => {
      if (err) {
        console.error('Database error fetching leases:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      res.json({ leases });
    });
  } catch (error) {
    console.error('Uncaught exception in fetching leases:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get lease history/archive for a user
router.get('/history', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const user_id = req.user.id;

    // Query includes joined book data for better display
    db.all(
      `SELECT l.*, b.title as book_title, b.author as book_author, b.cover_image
       FROM leases l
       JOIN books b ON l.book_id = b.id
       WHERE l.user_id = ? AND
       (l.status = 'returned' OR l.status = 'cancelled' OR l.status = 'overdue')
       ORDER BY l.lease_date DESC`,
      [user_id],
      (err, leases) => {
        if (err) {
          console.error('Database error fetching lease archive:', err.message);
          return res.status(500).json({ message: 'Error fetching lease archive', error: err.message });
        }

        res.json({ leases });
      }
    );
  } catch (error) {
    console.error('Uncaught exception in fetching lease archive:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get lease history/archive for a user (backward compatibility)
router.get('/archive', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const user_id = req.user.id;

    // Query includes joined book data for better display
    db.all(
      `SELECT l.*, b.title as book_title, b.author as book_author, b.cover_image
       FROM leases l
       JOIN books b ON l.book_id = b.id
       WHERE l.user_id = ? AND
       (l.status = 'returned' OR l.status = 'cancelled' OR l.status = 'overdue')
       ORDER BY l.lease_date DESC`,
      [user_id],
      (err, leases) => {
        if (err) {
          console.error('Database error fetching lease archive:', err.message);
          return res.status(500).json({ message: 'Error fetching lease archive', error: err.message });
        }

        res.json({ leases });
      }
    );
  } catch (error) {
    console.error('Uncaught exception in fetching lease archive:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get all lease dates for calendar view (for a specific book or all books)
router.get('/calendar', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const user_id = req.user.id;
    const book_id = req.query.book_id; // Optional parameter

    let query = `
      SELECT l.id, l.book_id, b.title as book_title,
             l.lease_date, l.due_date, l.status, l.approval_status
      FROM leases l
      JOIN books b ON l.book_id = b.id
      WHERE (l.status = 'active' OR l.status = 'pending')
    `;

    let params = [];

    // If book_id is specified, filter by it
    if (book_id) {
      query += ' AND l.book_id = ? ';
      params.push(book_id);
    }

    // If user is not admin, only show their own leases
    if (req.user.role !== 'admin') {
      query += ' AND l.user_id = ? ';
      params.push(user_id);
    }

    query += ' ORDER BY l.lease_date';

    db.all(query, params, (err, leases) => {
      if (err) {
        console.error('Database error fetching calendar data:', err.message);
        return res.status(500).json({ message: 'Error fetching calendar data', error: err.message });
      }

      res.json({ leases });
    });
  } catch (error) {
    console.error('Uncaught exception in fetching calendar data:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Create a new lease
router.post('/', [authenticateToken, isVerified, requireCompleteProfile], checkLeaseSpam, (req, res) => {
  const { book_id, days } = req.body;
  const user_id = req.user.id;

  console.log(`Lease request - User ID: ${user_id}, Book ID: ${book_id}, Days: ${days}`);

  if (!book_id || !days) {
    console.log('Missing required fields: book_id or days');
    return res.status(400).json({ message: 'Book ID and lease duration (days) are required' });
  }

  // Ensure parameters are integer values
  const bookId = parseInt(book_id);
  const leaseDays = parseInt(days);

  // Validate data types
  if (isNaN(bookId) || isNaN(leaseDays)) {
    console.log(`Invalid data types - book_id: ${book_id}, days: ${days}`);
    return res.status(400).json({ message: 'Book ID and days must be valid numbers' });
  }

  // Validate lease days range
  const MIN_LEASE_DAYS = 3;
  const MAX_LEASE_DAYS = 30;

  if (leaseDays < MIN_LEASE_DAYS || leaseDays > MAX_LEASE_DAYS) {
    console.log(`Invalid lease duration: ${leaseDays} days`);
    return res.status(400).json({
      message: `Lease duration must be between ${MIN_LEASE_DAYS} and ${MAX_LEASE_DAYS} days`
    });
  }

  // Calculate due date (current date + days)
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + leaseDays);

  try {
    const db = req.app.locals.db;
    if (!db) {
      console.error('Database not available');
      return res.status(500).json({ message: 'Database connection error' });
    }

    // First check if user is banned
    db.get('SELECT is_banned, ban_reason FROM users WHERE id = ?', [user_id], (err, user) => {
      if (err) {
        console.error('Database error checking user status:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (user && user.is_banned) {
        console.log(`User ${user_id} is banned. Ban reason: ${user.ban_reason}`);
        return res.status(403).json({
          message: 'Your account has been banned. You cannot lease books.',
          ban_reason: user.ban_reason || 'No reason provided'
        });
      }

      // Check if book exists and is available
      db.get('SELECT * FROM books WHERE id = ?', [bookId], (err, book) => {
        if (err) {
          console.error('Database error checking book:', err.message);
          return res.status(500).json({ message: 'Database error', error: err.message });
        }

        if (!book) {
          console.log(`Book with ID ${bookId} not found`);
          return res.status(404).json({ message: 'Book not found' });
        }

        if (book.available_copies <= 0) {
          console.log(`Book ${bookId} has no available copies`);
          return res.status(400).json({ message: 'Book is not available for leasing' });
        }

        // Check if user already has an active lease for this book
        db.get(
          'SELECT * FROM leases WHERE user_id = ? AND book_id = ? AND (status = "active" OR (status = "pending" AND approval_status = "pending"))',
          [user_id, bookId],
          (err, existingLease) => {
            if (err) {
              console.error('Database error checking existing lease:', err.message);
              return res.status(500).json({ message: 'Database error', error: err.message });
            }

            if (existingLease) {
              console.log(`User ${user_id} already has a lease for book ${bookId}`);
              return res.status(400).json({ message: 'You already have an active or pending lease for this book' });
            }

            // Create new lease transaction with pending status
            const query = 'INSERT INTO leases (user_id, book_id, due_date, status, approval_status) VALUES (?, ?, ?, "pending", "pending")';
            const params = [user_id, bookId, dueDate.toISOString()];

            console.log('Executing query:', query);
            console.log('With params:', JSON.stringify(params));

            db.run(
              query,
              params,
              function(err) {
                if (err) {
                  console.error('Database error creating lease:', err.message);
                  return res.status(500).json({ message: 'Error creating lease', error: err.message });
                }

                console.log(`Lease created successfully - ID: ${this.lastID}`);
                // Don't update book available copies until lease is approved
                res.status(201).json({
                  message: 'Lease request submitted successfully. Waiting for approval.',
                  lease: {
                    id: this.lastID,
                    user_id,
                    book_id: bookId,
                    lease_date: new Date().toISOString(),
                    due_date: dueDate.toISOString(),
                    status: 'pending',
                    approval_status: 'pending'
                  }
                });
              }
            );
          }
        );
      });
    });
  } catch (error) {
    console.error('Uncaught exception in lease creation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Create a new lease (alternative route for client compatibility)
router.post('/request', [authenticateToken, requireCompleteProfile], checkLeaseSpam, (req, res) => {
  const { book_id, duration, custom_message } = req.body;
  const user_id = req.user.id;

  console.log(`Lease request via /request endpoint - User ID: ${user_id}, Book ID: ${book_id}, Duration: ${duration} days`);

  if (!book_id || !duration) {
    console.log('Missing required fields: book_id or duration');
    return res.status(400).json({ message: 'Book ID and lease duration (days) are required' });
  }

  // Ensure parameters are integer values
  const bookId = parseInt(book_id);
  const leaseDays = parseInt(duration);

  // Validate data types
  if (isNaN(bookId) || isNaN(leaseDays)) {
    console.log(`Invalid data types - book_id: ${book_id}, duration: ${duration}`);
    return res.status(400).json({ message: 'Book ID and duration must be valid numbers' });
  }

  // Validate lease days range
  const MIN_LEASE_DAYS = 3;
  const MAX_LEASE_DAYS = 30;

  if (leaseDays < MIN_LEASE_DAYS || leaseDays > MAX_LEASE_DAYS) {
    console.log(`Invalid lease duration: ${leaseDays} days`);
    return res.status(400).json({
      message: `Lease duration must be between ${MIN_LEASE_DAYS} and ${MAX_LEASE_DAYS} days`
    });
  }

  // Calculate due date (current date + days)
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + leaseDays);

  try {
    const db = req.app.locals.db;
    if (!db) {
      console.error('Database not available');
      return res.status(500).json({ message: 'Database connection error' });
    }

    // First check if user is banned
    db.get('SELECT is_banned, ban_reason FROM users WHERE id = ?', [user_id], (err, user) => {
      if (err) {
        console.error('Database error checking user status:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (user && user.is_banned) {
        console.log(`User ${user_id} is banned. Ban reason: ${user.ban_reason}`);
        return res.status(403).json({
          message: 'Your account has been banned. You cannot lease books.',
          ban_reason: user.ban_reason || 'No reason provided'
        });
      }

      // Then check if book exists and is available
      db.get('SELECT * FROM books WHERE id = ?', [bookId], (err, book) => {
        if (err) {
          console.error('Database error checking book:', err.message);
          return res.status(500).json({ message: 'Database error', error: err.message });
        }

        if (!book) {
          console.log(`Book with ID ${bookId} not found`);
          return res.status(404).json({ message: 'Book not found' });
        }

        if (book.available_copies <= 0) {
          console.log(`Book ${bookId} has no available copies`);
          return res.status(400).json({ message: 'This book is currently unavailable for lease' });
        }

        // Check if user already has an active or pending lease for this book
        db.get(
          'SELECT * FROM leases WHERE user_id = ? AND book_id = ? AND (status = "active" OR (status = "pending" AND approval_status = "pending"))',
          [user_id, bookId],
          (err, existingLease) => {
            if (err) {
              console.error('Database error checking existing lease:', err.message);
              return res.status(500).json({ message: 'Database error', error: err.message });
            }

            if (existingLease) {
              console.log(`User ${user_id} already has a lease for book ${bookId}`);
              return res.status(400).json({ message: 'You already have an active or pending lease for this book' });
            }

            // Create new lease transaction with pending status
            const query = 'INSERT INTO leases (user_id, book_id, due_date, status, approval_status, custom_message) VALUES (?, ?, ?, "pending", "pending", ?)';
            const params = [user_id, bookId, dueDate.toISOString(), custom_message || ''];

            console.log('Executing query:', query);
            console.log('With params:', JSON.stringify(params));

            db.run(
              query,
              params,
              function(err) {
                if (err) {
                  console.error('Database error creating lease:', err.message);
                  return res.status(500).json({ message: 'Error creating lease', error: err.message });
                }

                console.log(`Lease created successfully - ID: ${this.lastID}`);
                // Don't update book available copies until lease is approved
                res.status(201).json({
                  message: 'Lease request submitted successfully. Waiting for approval.',
                  lease: {
                    id: this.lastID,
                    user_id,
                    book_id: bookId,
                    lease_date: new Date().toISOString(),
                    due_date: dueDate.toISOString(),
                    status: 'pending',
                    approval_status: 'pending',
                    custom_message: custom_message || ''
                  }
                });
              }
            );
          }
        );
      });
    });
  } catch (error) {
    console.error('Uncaught exception in creating lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get a single lease by ID
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const leaseId = req.params.id;
    const userId = req.user.id;
    const isUserAdmin = req.user.role === 'admin';

    // Check if lease exists and user has access
    const query = isUserAdmin
      ? 'SELECT l.*, b.title, b.author, b.cover_image FROM leases l JOIN books b ON l.book_id = b.id WHERE l.id = ?'
      : 'SELECT l.*, b.title, b.author, b.cover_image FROM leases l JOIN books b ON l.book_id = b.id WHERE l.id = ? AND l.user_id = ?';

    const params = isUserAdmin ? [leaseId] : [leaseId, userId];

    db.get(query, params, (err, lease) => {
      if (err) {
        console.error('Database error fetching lease:', err.message);
        return res.status(500).json({ message: 'Error fetching lease', error: err.message });
      }

      if (!lease) {
        return res.status(404).json({ message: `Lease with ID ${leaseId} not found` });
      }

      res.json({ lease });
    });
  } catch (error) {
    console.error('Uncaught exception in fetching lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Approve a lease (admin only)
router.put('/:id/approve', authenticateToken, (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.log(`Access denied for user ${req.user.id} to approve lease ${req.params.id}`);
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    const db = req.app.locals.db;
    const leaseId = req.params.id;

    // Get the lease to verify status
    db.get('SELECT * FROM leases WHERE id = ?', [leaseId], (err, lease) => {
      if (err) {
        console.error('Database error fetching lease:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!lease) {
        console.log(`Lease with ID ${leaseId} not found`);
        return res.status(404).json({ message: 'Lease not found' });
      }

      if (lease.approval_status !== 'pending') {
        console.log(`Lease ${leaseId} has already been processed`);
        return res.status(400).json({ message: 'This lease has already been processed' });
      }

      // Check if book is still available
      db.get('SELECT * FROM books WHERE id = ?', [lease.book_id], (err, book) => {
        if (err) {
          console.error('Database error checking book:', err.message);
          return res.status(500).json({ message: 'Database error', error: err.message });
        }

        if (!book) {
          console.log(`Book with ID ${lease.book_id} not found`);
          return res.status(404).json({ message: 'Book not found' });
        }

        if (book.available_copies <= 0) {
          // Update lease status to rejected if no copies available
          db.run(
            'UPDATE leases SET approval_status = "rejected", rejection_reason = "No copies available", approval_date = ? WHERE id = ?',
            [new Date().toISOString(), leaseId],
            (err) => {
              if (err) {
                console.error('Database error updating lease:', err.message);
                return res.status(500).json({ message: 'Error updating lease', error: err.message });
              }

              console.log(`Lease ${leaseId} rejected due to no available copies`);
              return res.status(400).json({ message: 'Cannot approve lease. No copies available.' });
            }
          );
        } else {
          // Update lease status to approved
          db.run(
            'UPDATE leases SET status = "active", approval_status = "approved", approval_date = ? WHERE id = ?',
            [new Date().toISOString(), leaseId],
            (err) => {
              if (err) {
                console.error('Database error updating lease:', err.message);
                return res.status(500).json({ message: 'Error updating lease', error: err.message });
              }

              // Update book available copies
              db.run(
                'UPDATE books SET available_copies = available_copies - 1 WHERE id = ?',
                [lease.book_id],
                (err) => {
                  if (err) {
                    console.error('Database error updating book availability:', err.message);
                    return res.status(500).json({ message: 'Error updating book availability', error: err.message });
                  }

                  console.log(`Lease ${leaseId} approved and book availability updated`);
                  res.json({ message: 'Lease approved successfully' });
                }
              );
            }
          );
        }
      });
    });
  } catch (error) {
    console.error('Uncaught exception in approving lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Reject a lease (admin only)
router.put('/:id/reject', authenticateToken, (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      console.log(`Access denied for user ${req.user.id} to reject lease ${req.params.id}`);
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    const db = req.app.locals.db;
    const leaseId = req.params.id;
    const { reason } = req.body;

    // Get the lease to verify status
    db.get('SELECT * FROM leases WHERE id = ?', [leaseId], (err, lease) => {
      if (err) {
        console.error('Database error fetching lease:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!lease) {
        console.log(`Lease with ID ${leaseId} not found`);
        return res.status(404).json({ message: 'Lease not found' });
      }

      if (lease.approval_status !== 'pending') {
        console.log(`Lease ${leaseId} has already been processed`);
        return res.status(400).json({ message: 'This lease has already been processed' });
      }

      // Update lease status to rejected
      db.run(
        'UPDATE leases SET approval_status = "rejected", rejection_reason = ?, approval_date = ? WHERE id = ?',
        [reason || 'No reason provided', new Date().toISOString(), leaseId],
        (err) => {
          if (err) {
            console.error('Database error updating lease:', err.message);
            return res.status(500).json({ message: 'Error updating lease', error: err.message });
          }

          console.log(`Lease ${leaseId} rejected`);
          res.json({ message: 'Lease rejected successfully' });
        }
      );
    });
  } catch (error) {
    console.error('Uncaught exception in rejecting lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Cancel a lease request (admin or the user who made the request)
router.put('/:id/cancel', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const leaseId = req.params.id;

    // Get the lease to verify ownership and status
    db.get('SELECT * FROM leases WHERE id = ?', [leaseId], (err, lease) => {
      if (err) {
        console.error('Database error fetching lease:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!lease) {
        console.log(`Lease with ID ${leaseId} not found`);
        return res.status(404).json({ message: 'Lease not found' });
      }

      // Only admin or the user who made the request can cancel it
      if (req.user.role !== 'admin' && lease.user_id !== req.user.id) {
        console.log(`Access denied for user ${req.user.id} to cancel lease ${leaseId}`);
        return res.status(403).json({ message: 'Access denied. You can only cancel your own lease requests.' });
      }

      // Can only cancel pending leases
      if (lease.status !== 'pending' || lease.approval_status !== 'pending') {
        console.log(`Cannot cancel lease ${leaseId} as it's not in pending status`);
        return res.status(400).json({ message: 'Only pending lease requests can be cancelled' });
      }

      // Update lease status to cancelled
      db.run(
        'UPDATE leases SET status = "cancelled", approval_status = "cancelled" WHERE id = ?',
        [leaseId],
        function(err) {
          if (err) {
            console.error('Database error cancelling lease:', err.message);
            return res.status(500).json({ message: 'Error cancelling lease', error: err.message });
          }

          console.log(`Lease ${leaseId} cancelled successfully`);
          res.json({ message: 'Lease request cancelled successfully' });
        }
      );
    });
  } catch (error) {
    console.error('Uncaught exception in cancelling lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Cancel a lease within the cancellation window (3 hours after creating)
router.put('/:id/cancel', authenticateToken, (req, res) => {
  try {
    const leaseId = req.params.id;
    const userId = req.user.id;
    const db = req.app.locals.db;

    // First check if the lease exists and belongs to the user
    db.get(
      'SELECT l.*, b.title as book_title FROM leases l JOIN books b ON l.book_id = b.id WHERE l.id = ? AND l.user_id = ?',
      [leaseId, userId],
      (err, lease) => {
        if (err) {
          console.error('Database error checking lease:', err.message);
          return res.status(500).json({ message: 'Database error', error: err.message });
        }

        if (!lease) {
          return res.status(404).json({ message: 'Lease not found or you do not have permission to cancel it' });
        }

        // Check if the lease is in a status that can be cancelled
        if (lease.status !== 'active' && lease.status !== 'pending') {
          return res.status(400).json({
            message: `Cannot cancel a lease with status: ${lease.status}. Only active or pending leases can be cancelled.`
          });
        }

        // Check the cancellation window (3 hours = 10800000 milliseconds)
        const leaseDate = new Date(lease.lease_date);
        const now = new Date();
        const timeDifference = now.getTime() - leaseDate.getTime();
        const CANCELLATION_WINDOW = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

        if (timeDifference > CANCELLATION_WINDOW) {
          return res.status(400).json({
            message: 'Cancellation period has expired. Leases can only be cancelled within 3 hours of creation.'
          });
        }

        // Proceed with cancellation
        db.run(
          'UPDATE leases SET status = "cancelled", return_date = CURRENT_TIMESTAMP WHERE id = ?',
          [leaseId],
          function(err) {
            if (err) {
              console.error('Database error cancelling lease:', err.message);
              return res.status(500).json({ message: 'Error cancelling lease', error: err.message });
            }

            if (this.changes === 0) {
              return res.status(500).json({ message: 'Lease cancellation failed' });
            }

            // If lease was active (not just pending), increment the available copies
            if (lease.status === 'active') {
              db.run(
                'UPDATE books SET available_copies = available_copies + 1 WHERE id = ?',
                [lease.book_id],
                function(err) {
                  if (err) {
                    console.error('Database error updating book copies:', err.message);
                    // We don't want to fail the whole operation if this fails
                    // Just log it and continue
                  }
                }
              );
            }

            res.json({
              message: `Lease for "${lease.book_title}" has been successfully cancelled`,
              lease_id: leaseId
            });
          }
        );
      }
    );
  } catch (error) {
    console.error('Uncaught exception in cancelling lease:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Return a book (update lease status)
router.put('/:id/return', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const leaseId = req.params.id;

    // Get the lease to verify ownership and status
    db.get('SELECT * FROM leases WHERE id = ?', [leaseId], (err, lease) => {
      if (err) {
        console.error('Database error fetching lease:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!lease) {
        console.log(`Lease with ID ${leaseId} not found`);
        return res.status(404).json({ message: 'Lease not found' });
      }

      // Regular users can only return their own leases
      if (req.user.role !== 'admin' && lease.user_id !== req.user.id) {
        console.log(`Access denied for user ${req.user.id} to return lease ${leaseId}`);
        return res.status(403).json({ message: 'Access denied' });
      }

      if (lease.status !== 'active') {
        console.log(`Lease ${leaseId} has already been returned`);
        return res.status(400).json({ message: 'This book has already been returned' });
      }

      // Update lease status and return date
      db.run(
        'UPDATE leases SET status = "returned", return_date = ? WHERE id = ?',
        [new Date().toISOString(), leaseId],
        (err) => {
          if (err) {
            console.error('Database error updating lease:', err.message);
            return res.status(500).json({ message: 'Error updating lease', error: err.message });
          }

          // Update book available copies
          db.run(
            'UPDATE books SET available_copies = available_copies + 1 WHERE id = ?',
            [lease.book_id],
            (err) => {
              if (err) {
                console.error('Database error updating book availability:', err.message);
                return res.status(500).json({ message: 'Error updating book availability', error: err.message });
              }

              console.log(`Book returned and availability updated for lease ${leaseId}`);
              res.json({ message: 'Book returned successfully' });
            }
          );
        }
      );
    });
  } catch (error) {
    console.error('Uncaught exception in returning book:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get lease statistics for a user
router.get('/stats', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const user_id = req.user.id;

    // Query to get lease statistics
    db.get(
      `SELECT
        COUNT(CASE WHEN status = 'active' THEN 1 END) as activeLeases,
        COUNT(*) as totalLeases
       FROM leases
       WHERE user_id = ?`,
      [user_id],
      (err, stats) => {
        if (err) {
          console.error('Database error fetching lease statistics:', err.message);
          return res.status(500).json({ message: 'Error fetching lease statistics', error: err.message });
        }

        res.json(stats || { activeLeases: 0, totalLeases: 0 });
      }
    );
  } catch (error) {
    console.error('Uncaught exception in fetching lease statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;