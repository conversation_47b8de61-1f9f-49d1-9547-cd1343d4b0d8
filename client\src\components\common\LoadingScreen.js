import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const LoadingScreen = ({ message = 'Loading...' }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'fixed', // Or 'absolute' if within a specific container
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.8)', // Dark overlay
        zIndex: (theme) => theme.zIndex.drawer + 1, // Ensure it's on top
        color: 'white',
      }}
    >
      <Box sx={{ position: 'relative', width: 100, height: 100, mb: 3 }}>
        {/* Logo in the center */}
        <img 
          src="/logo512.png" // Assumes logo512.png is in the public folder
          alt="Loading Logo" 
          style={{ 
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '70%', // Adjust size as needed
            height: 'auto',
            zIndex: 1, // Logo above spinner
          }} 
        />
        {/* Spinner around the logo */}
        <CircularProgress
          variant="indeterminate"
          disableShrink // Makes the circle spin continuously
          size={100} // Match the Box size or slightly larger/smaller
          thickness={2} // Thinner spinner ring
          sx={{
            color: 'primary.main', // Use theme's primary color (white in our dark theme)
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 0, // Spinner behind logo
            animationDuration: '800ms', // Faster spin
          }}
        />
      </Box>
      <Typography variant="h6">{message}</Typography>
    </Box>
  );
};

export default LoadingScreen; 