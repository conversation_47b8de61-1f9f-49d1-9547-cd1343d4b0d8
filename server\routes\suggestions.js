const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { authenticateToken } = require('./auth');
const { isAdmin } = require('../middleware/admin');
const { requireCompleteProfile } = require('../middleware/profileCheck');
const moment = require('moment');

// Middleware to ensure user is authenticated
router.use(authenticateToken);

// Middleware to check if user is verified
const isVerified = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (req.user.email_verified === 0) {
    return res.status(403).json({
      message: 'You need to verify your email before suggesting books',
      error: 'email_not_verified'
    });
  }

  next();
};

// @route   GET api/suggestions/remaining
// @desc    Get remaining suggestions count for current user
// @access  Private (verified users)
router.get('/remaining', [isVerified, requireCompleteProfile], async (req, res) => {
  try {
    console.log('Checking remaining suggestions for user:', req.user.id);

    // Do a proper count query with Promise-based approach
    const countResult = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        `SELECT COUNT(*) as count FROM book_suggestions WHERE user_id = ?`,
        [req.user.id],
        function(err, row) {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    // Ensure we have a valid count
    const count = countResult && typeof countResult.count !== 'undefined' ?
      parseInt(countResult.count, 10) : 0;

    // Calculate remaining
    const remaining = Math.max(0, 5 - count);

    console.log(`User ${req.user.id} has made ${count} suggestions, ${remaining} remaining`);
    res.json({
      remaining,
      count,
      timestamp: Date.now()
    });
  } catch (err) {
    console.error('Error checking remaining suggestions:', err);
    // Default to 5 remaining in case of error
    res.json({ remaining: 5, error: true });
  }
});

// @route   POST api/suggestions
// @desc    Submit a book suggestion
// @access  Private (verified users)
router.post(
  '/',
  [
    isVerified,
    requireCompleteProfile,
    check('title', 'Title is required').not().isEmpty(),
    check('author', 'Author is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Log user ID and decoded token info
      console.log('User submitting suggestion - ID:', req.user.id);
      console.log('User token data:', JSON.stringify(req.user));

      // Extract data from request
      let { isbn, title, author, description, cover_image } = req.body;
      console.log('Processing book suggestion:', { title, author, isbn });

      // Always ensure ISBN has a value to satisfy NOT NULL constraint
      if (!isbn || isbn.trim() === '') {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        isbn = `TEMP-${timestamp}-${random}`;
        console.log('Generated temporary ISBN:', isbn);
      }

      // Create new suggestion - use a promise to ensure SQLite operation completes
      await new Promise((resolve, reject) => {
        req.app.locals.db.run(
          `INSERT INTO book_suggestions
           (user_id, isbn, title, author, description, cover_image, status, created_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            req.user.id,
            isbn,
            title,
            author,
            description || null,
            cover_image || null,
            'pending',
            moment().format('YYYY-MM-DD HH:mm:ss')
          ],
          function(err) {
            if (err) return reject(err);
            console.log('Book suggestion successfully added with ID:', this.lastID);
            resolve(this.lastID);
          }
        );
      });

      // Get updated count with a direct query
      const countResult = await new Promise((resolve, reject) => {
        req.app.locals.db.get(
          `SELECT COUNT(*) as count FROM book_suggestions WHERE user_id = ?`,
          [req.user.id],
          function(err, row) {
            if (err) return reject(err);
            resolve(row);
          }
        );
      });

      const count = countResult && countResult.count ? parseInt(countResult.count, 10) : 1;
      const remaining = Math.max(0, 5 - count);

      console.log(`User ${req.user.id} now has made ${count} suggestions, ${remaining} remaining`);

      res.json({
        message: 'Book suggestion submitted successfully',
        remaining: remaining,
        debug: { count, userId: req.user.id }
      });
    } catch (err) {
      console.error('Error processing suggestion:', err);
      // Check for specific SQLite error codes
      if (err.code === 'SQLITE_CONSTRAINT') {
        return res.status(400).json({
          message: 'This book already exists or has already been suggested.',
          error: 'duplicate_entry'
        });
      }
      res.status(500).json({
        message: 'Server error processing your suggestion. Please try again later.',
        error: err.message
      });
    }
  }
);

// Admin routes

// @route   GET api/suggestions
// @desc    Get all suggestions
// @access  Admin only
router.get('/', authenticateToken, isAdmin, async (req, res) => {
  try {
    console.log('Fetching suggestions with query:', req.query);

    // Get the status filter from query params (default to 'pending')
    const { status } = req.query;
    const filterStatus = status || 'all';

    // Build the SQL query based on filter
    let query, params = [];

    if (filterStatus === 'all') {
      // Get all suggestions regardless of status
      query = `
        SELECT s.*,
          u.username as user_name,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'pending') as pending_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'acknowledged') as acknowledged_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'approved') as approved_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'rejected') as rejected_count,
          (SELECT COUNT(*) FROM book_suggestions) as total_count
        FROM book_suggestions s
        JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
      `;
    } else {
      // Filter by specific status
      query = `
        SELECT s.*,
          u.username as user_name,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'pending') as pending_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'acknowledged') as acknowledged_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'approved') as approved_count,
          (SELECT COUNT(*) FROM book_suggestions WHERE status = 'rejected') as rejected_count,
          (SELECT COUNT(*) FROM book_suggestions) as total_count
        FROM book_suggestions s
        JOIN users u ON s.user_id = u.id
        WHERE s.status = ?
        ORDER BY s.created_at DESC
      `;
      params.push(filterStatus);
    }

    const suggestions = await new Promise((resolve, reject) => {
      req.app.locals.db.all(query, params, (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });

    console.log(`Fetched ${suggestions.length} suggestions with filter: ${filterStatus}`);

    // Extract counts from the first row if available
    const counts = suggestions.length > 0 ? {
      pending: suggestions[0].pending_count,
      acknowledged: suggestions[0].acknowledged_count,
      approved: suggestions[0].approved_count,
      rejected: suggestions[0].rejected_count,
      total: suggestions[0].total_count
    } : { pending: 0, acknowledged: 0, approved: 0, rejected: 0, total: 0 };

    console.log('Suggestion counts:', counts);

    // Clean up the result by removing count columns from each suggestion
    const cleanSuggestions = suggestions.map(s => {
      const {
        pending_count, acknowledged_count, approved_count, rejected_count, total_count,
        ...cleanSuggestion
      } = s;
      return cleanSuggestion;
    });

    res.json({
      suggestions: cleanSuggestions,
      counts
    });
  } catch (err) {
    console.error('Error fetching suggestions:', err.message);
    res.status(500).json({ message: 'Server error fetching suggestions', error: err.message });
  }
});

// @route   PUT api/suggestions/:id/approve
// @desc    Approve a book suggestion without adding it to books
// @access  Admin
router.put('/:id/approve', isAdmin, async (req, res) => {
  try {
    console.log(`Approving suggestion ${req.params.id}`);

    // Get the suggestion using Promise
    const suggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [req.params.id],
        function(err, row) {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    console.log('Found suggestion:', suggestion);

    if (!suggestion) {
      return res.status(404).json({ message: 'Suggestion not found' });
    }

    // Simply update the suggestion status to approved
    await new Promise((resolve, reject) => {
      req.app.locals.db.run(
        'UPDATE book_suggestions SET status = ?, processed_at = ? WHERE id = ?',
        ['approved', moment().format('YYYY-MM-DD HH:mm:ss'), req.params.id],
        function(err) {
          if (err) return reject(err);
          resolve(this.changes);
        }
      );
    });

    console.log(`Suggestion ${req.params.id} marked as approved`);

    res.json({
      message: 'Suggestion approved successfully',
      id: req.params.id,
      status: 'approved'
    });
  } catch (err) {
    console.error('Error approving suggestion:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// @route   PUT api/suggestions/:id/reject
// @desc    Reject a book suggestion
// @access  Admin
router.put('/:id/reject', isAdmin, async (req, res) => {
  try {
    console.log(`Rejecting suggestion ${req.params.id}`);
    console.log('Rejection payload:', req.body);

    // Force status to 'rejected'
    const status = 'rejected';
    const processed_at = moment().format('YYYY-MM-DD HH:mm:ss');
    const rejection_reason = req.body && req.body.reason !== undefined ? req.body.reason : null;

    // Verify the suggestion exists first
    const suggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [req.params.id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    if (!suggestion) {
      console.log(`Suggestion ${req.params.id} not found`);
      return res.status(404).json({ message: 'Suggestion not found' });
    }

    console.log(`Found suggestion to reject:`, JSON.stringify(suggestion));

    // Execute the update as a transaction to ensure consistency
    await new Promise((resolve, reject) => {
      req.app.locals.db.run('BEGIN TRANSACTION', (err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    try {
      // Update suggestion status
      const result = await new Promise((resolve, reject) => {
        req.app.locals.db.run(
          'UPDATE book_suggestions SET status = ?, processed_at = ?, rejection_reason = ? WHERE id = ?',
          [status, processed_at, rejection_reason, req.params.id],
          function(err) {
            if (err) return reject(err);
            resolve({changes: this.changes});
          }
        );
      });

      // Verify the update was successful
      if (result.changes === 0) {
        throw new Error(`Failed to update suggestion ${req.params.id} status`);
      }

      // Commit the transaction
      await new Promise((resolve, reject) => {
        req.app.locals.db.run('COMMIT', (err) => {
          if (err) return reject(err);
          resolve();
        });
      });

      // Double check that the status was updated correctly
      const updated = await new Promise((resolve, reject) => {
        req.app.locals.db.get(
          'SELECT status FROM book_suggestions WHERE id = ?',
          [req.params.id],
          (err, row) => {
            if (err) return reject(err);
            resolve(row);
          }
        );
      });

      console.log(`Verified rejection - suggestion status is now: ${updated?.status}`);

      if (!updated || updated.status !== 'rejected') {
        console.error(`Warning: Suggestion ${req.params.id} status was not updated correctly`);
      }

      res.json({
        message: 'Suggestion rejected successfully',
        id: req.params.id,
        status: 'rejected',
        processed_at: processed_at
      });
    } catch (err) {
      // Rollback in case of error
      await new Promise((resolve) => {
        req.app.locals.db.run('ROLLBACK', () => resolve());
      });
      throw err;
    }
  } catch (err) {
    console.error('Error rejecting suggestion:', err.message);
    res.status(500).json({ message: 'Server error processing your request' });
  }
});

// @route   DELETE api/suggestions/:id
// @desc    Delete a book suggestion (completely remove it from the database)
// @access  Admin
router.delete('/:id', isAdmin, async (req, res) => {
  try {
    console.log(`Deleting suggestion ${req.params.id} from database`);

    // Verify the suggestion exists first
    const suggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [req.params.id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    if (!suggestion) {
      console.log(`Suggestion ${req.params.id} not found`);
      return res.status(404).json({ message: 'Suggestion not found' });
    }

    console.log(`Found suggestion to delete:`, JSON.stringify(suggestion));

    // Execute the delete
    const result = await new Promise((resolve, reject) => {
      req.app.locals.db.run(
        'DELETE FROM book_suggestions WHERE id = ?',
        [req.params.id],
        function(err) {
          if (err) return reject(err);
          resolve({changes: this.changes});
        }
      );
    });

    // Verify the delete was successful
    if (result.changes === 0) {
      return res.status(404).json({ message: 'No record was deleted' });
    }

    console.log(`Successfully deleted suggestion ${req.params.id} (${result.changes} row affected)`);

    res.json({
      message: 'Suggestion deleted successfully',
      id: req.params.id
    });
  } catch (err) {
    console.error('Error deleting suggestion:', err.message);
    res.status(500).json({ message: 'Server error processing your request' });
  }
});

// @route   PUT api/suggestions/:id
// @desc    Update a suggestion (category or status)
// @access  Admin
router.put('/:id', authenticateToken, isAdmin, async (req, res) => {
  try {
    console.log('Updating suggestion:', req.params.id, req.body);

    const suggestionId = req.params.id;

    // Validate request
    if (!suggestionId) {
      return res.status(400).json({ message: 'Suggestion ID is required' });
    }

    // Check if updating status or category
    const { status, category } = req.body;

    // Make sure we're updating something
    if (!status && !category) {
      return res.status(400).json({ message: 'No update parameters provided' });
    }

    // Verify suggestion exists
    const suggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [suggestionId],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    if (!suggestion) {
      return res.status(404).json({ message: 'Suggestion not found' });
    }

    // Build the update query based on what's being updated
    let updateFields = [];
    let params = [];

    if (status) {
      // Validate status
      if (status !== 'pending' && status !== 'acknowledged' && status !== 'approved' && status !== 'rejected') {
        return res.status(400).json({
          message: 'Invalid status. Must be: pending, acknowledged, approved, or rejected'
        });
      }

      updateFields.push('status = ?');
      params.push(status);
    }

    if (category) {
      updateFields.push('category = ?');
      params.push(category);
    }

    // Add the suggestion ID to params
    params.push(suggestionId);

    // Execute the update
    const result = await new Promise((resolve, reject) => {
      req.app.locals.db.run(
        `UPDATE book_suggestions SET ${updateFields.join(', ')} WHERE id = ?`,
        params,
        function(err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });

    if (result.changes === 0) {
      return res.status(400).json({ message: 'Suggestion not updated. No changes made.' });
    }

    // Get the updated suggestion
    const updatedSuggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [suggestionId],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    res.json({
      message: 'Suggestion updated successfully',
      suggestion: updatedSuggestion
    });
  } catch (err) {
    console.error('Error updating suggestion:', err.message);
    res.status(500).json({ message: 'Server error updating suggestion', error: err.message });
  }
});

// @route   DELETE api/suggestions/:id
// @desc    Delete a book suggestion
// @access  Admin
router.delete('/:id', isAdmin, async (req, res) => {
  try {
    console.log(`Deleting suggestion ${req.params.id}`);

    // Verify the suggestion exists first
    const suggestion = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM book_suggestions WHERE id = ?',
        [req.params.id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    if (!suggestion) {
      console.log(`Suggestion ${req.params.id} not found`);
      return res.status(404).json({ message: 'Suggestion not found' });
    }

    // Delete the suggestion
    await new Promise((resolve, reject) => {
      req.app.locals.db.run(
        'DELETE FROM book_suggestions WHERE id = ?',
        [req.params.id],
        function(err) {
          if (err) return reject(err);
          resolve(this.changes);
        }
      );
    });

    console.log(`Suggestion ${req.params.id} deleted successfully`);
    res.json({ message: 'Suggestion deleted successfully' });
  } catch (err) {
    console.error('Error deleting suggestion:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Export the router
module.exports = router;
