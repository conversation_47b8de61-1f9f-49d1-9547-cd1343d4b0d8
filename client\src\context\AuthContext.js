import React, { createContext, useState, useEffect } from 'react';
import api from '../utils/api'; // Import api utility
import { firebaseInitPromise, sendEmailVerification, createUserWithEmailAndPassword, signInWithEmailAndPassword } from '../firebase';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(0);
  const REFRESH_THROTTLE = 5000; // Minimum 5 seconds between refreshes

  // Load user from localStorage on initial render
  useEffect(() => {
    const loadUser = async () => {
      try {
        const token = localStorage.getItem('token');

        if (!token) {
          setLoading(false);
          return;
        }

        // Set token in api headers
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        // Try to get user data
        const res = await api.direct.get('/api/auth/me');

        if (res.data && res.data.user) {
          setUser(res.data.user);
          setIsAuthenticated(true);

          // If user is verified, store in localStorage
          if (res.data.user.email_verified === 1) {
            localStorage.setItem('userEmailVerified', '1');
          }
        } else {
          // Invalid response, clear token
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
        }
      } catch (err) {
        console.error('Error loading user:', err);
        // Clear token on error
        localStorage.removeItem('token');
        delete api.defaults.headers.common['Authorization'];
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  // Function to refresh user data from server
  const refreshUserData = async (skipVerificationCheck = false) => {
    try {
      if (!isAuthenticated) {
        console.log('User not authenticated, cannot refresh data');
        return null;
      }

      // Get fresh user data from the server
      const response = await api.direct.get('/api/auth/me');

      if (response.data && response.data.user) {
        // Update user data in state
        setUser(response.data.user);
        console.log('User data refreshed successfully');

        // We'll just return the user data without any Firebase checks
        return response.data.user;
      }

      return null;
    } catch (err) {
      console.error('Error refreshing user data:', err);
      setError('Failed to refresh user data');
      return null;
    }
  };

  // Register user
  const register = async (userData) => {
    let firebaseUser = null; // Declare firebaseUser here
    setLoading(true);
    setError(null);

    try {
      // Try to register with backend first
      const registerData = {
        ...userData
      };
      console.log('Data being sent to /api/auth/register:', registerData); // Debug log

      // Use direct API call to register with backend
      const res = await api.direct.post('/api/auth/register', registerData);

      // Save token to localStorage
      localStorage.setItem('token', res.data.token);

      setUser(res.data.user);
      setIsAuthenticated(true);

      // Now try to create Firebase user for email verification
      try {
        const { auth } = await firebaseInitPromise;
        const firebaseCredential = await createUserWithEmailAndPassword(
          auth,
          userData.email,
          userData.password
        );
        firebaseUser = firebaseCredential.user;
        console.log('Firebase user created successfully:', firebaseUser.email);
      } catch (firebaseError) {
        console.error('Firebase registration error:', firebaseError);

        // Handle specific Firebase errors
        if (firebaseError.code === 'auth/email-already-in-use') {
          console.log('Firebase user already exists, will try to sign in for verification');
          // Try to sign in to existing Firebase user for verification
          try {
            const { auth } = await firebaseInitPromise;
            const firebaseCredential = await signInWithEmailAndPassword(
              auth,
              userData.email,
              userData.password
            );
            firebaseUser = firebaseCredential.user;
            console.log('Signed in to existing Firebase user:', firebaseUser.email);
          } catch (signInError) {
            console.error('Failed to sign in to existing Firebase user:', signInError);
            // Continue without Firebase user - backend registration was successful
          }
        } else if (firebaseError.code === 'auth/invalid-email') {
          console.error('Invalid email format for Firebase');
        } else if (firebaseError.code === 'auth/weak-password') {
          console.error('Password too weak for Firebase');
        }

        // Continue without Firebase user - backend registration was successful
        console.log('Continuing without Firebase user - backend registration successful');
      }

      // Update user with Firebase UID if we have it
      if (firebaseUser?.uid && res.data.user) {
        try {
          // Update the user record with Firebase UID
          await api.direct.patch('/api/users/me', {
            firebase_uid: firebaseUser.uid
          });

          // Update local user state
          setUser({
            ...res.data.user,
            firebase_uid: firebaseUser.uid
          });
        } catch (updateError) {
          console.error('Failed to update user with Firebase UID:', updateError);
          // Continue - this is not critical
        }
      }

      // If we have a Firebase user, send verification email
      if (firebaseUser) {
        try {
          // Define actionCodeSettings with the proper redirect URL
          const actionCodeSettings = {
            url: `${window.location.origin}/verify-email-confirmation`,
            handleCodeInApp: false
          };

          await sendEmailVerification(firebaseUser, actionCodeSettings);
          setEmailVerificationSent(true);
          console.log('Verification email sent during registration');
        } catch (verificationError) {
          console.error('Failed to send verification email during registration:', verificationError);
          // Don't fail registration if verification email fails
        }
      }

      return res.data;
    } catch (err) {
      // Clean up Firebase user if backend registration fails
      if (firebaseUser) {
        try {
          await firebaseUser.delete();
          console.log('Deleted Firebase user after backend registration failure');
        } catch (deleteError) {
          console.error('Failed to delete Firebase user after registration failure:', deleteError);
        }
      }

      setError(
        err.response?.data?.message || err.message || 'An unexpected error occurred'
      );
      throw err; // Re-throw the error so Register.js can catch it
    } finally {
      setLoading(false);
    }
  };

  // Login user
  const login = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      // First try to sign in with Firebase
      let firebaseUser = null;
      try {
        const { auth } = await firebaseInitPromise;
        const userCredential = await signInWithEmailAndPassword(auth, userData.email, userData.password);
        firebaseUser = userCredential.user;
        console.log('Firebase login successful for:', firebaseUser.email);
      } catch (firebaseError) {
        console.error('Firebase login error:', firebaseError);

        // Handle specific Firebase errors
        if (firebaseError.code === 'auth/user-not-found' ||
            firebaseError.code === 'auth/wrong-password' ||
            firebaseError.code === 'auth/invalid-credential') {
          // Don't throw here - try backend login instead
          console.log('Firebase authentication failed, trying backend...');
        } else if (firebaseError.code === 'auth/too-many-requests') {
          throw new Error('Too many failed login attempts. Please try again later or reset your password.');
        } else {
          // For unexpected Firebase errors, log but continue to backend
          console.error('Unexpected Firebase auth error:', firebaseError);
        }
      }

      // Now authenticate with backend
      // Use direct API call to bypass proxy issues
      const res = await api.direct.post('/api/auth/login', userData);

      // Save token to localStorage
      localStorage.setItem('token', res.data.token);

      // api utility will handle setting the Authorization header in its interceptors

      setUser(res.data.user);
      setIsAuthenticated(true);

      // If user is not verified and we have a Firebase user, check verification status
      if (res.data.user?.email_verified !== 1 && firebaseUser) {
        // Force reload the user to get current verification status
        await firebaseUser.reload();

        if (firebaseUser.emailVerified) {
          // Update backend if Firebase says the user is verified
          try {
            console.log('Firebase shows verified, syncing with backend');
            await api.direct.post('/api/auth/sync-verification-status', {
              firebase_verified: true,
              user_id: res.data.user?.id,
              email: res.data.user?.email || firebaseUser.email,
              force_update: true
            });

            // Update user in state with verified status
            setUser({
              ...res.data.user,
              email_verified: 1
            });
          } catch (syncError) {
            console.error('Error syncing verification status after login:', syncError);
          }
        }
      }

      return res.data;
    } catch (err) {
      setError(
        err.response?.data?.message || err.message ||
        'Login failed. Please check your credentials and try again.'
      );
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    localStorage.removeItem('token');

    // Remove default headers
    delete api.defaults.headers.common['Authorization'];

    setUser(null);
    setIsAuthenticated(false);

    // Redirect to login page
    window.location.href = '/login';
  };

  // Send verification email
  const sendVerificationEmail = async () => {
    try {
      setEmailVerificationSent(false);
      setError(null);

      // First, create actionCodeSettings with dynamic redirect URL
      const actionCodeSettings = {
        url: `${window.location.origin}/verify-email-confirmation`,
        handleCodeInApp: false, // Changed to false for standard email verification flow
      };

      // Get Firebase auth instance first
      const { auth } = await firebaseInitPromise;

      // Try Firebase method first if we have a current user
      if (auth.currentUser) {
        try {
          // Force reload Firebase user to ensure we have latest state
          await auth.currentUser.reload();

          // Send verification email with custom action code settings
          await sendEmailVerification(auth.currentUser, actionCodeSettings);

          console.log('Firebase verification email sent successfully to:', auth.currentUser.email);

          // Store the timestamp of the most recent email
          localStorage.setItem('lastVerificationEmailSent', Date.now().toString());

          // Also notify backend about the verification email
          try {
            await api.direct.post('/api/auth/send-verification-email');
            console.log('Backend notified about verification email');
          } catch (backendError) {
            console.error('Failed to notify backend about verification email:', backendError);
            // Continue even if backend notification fails
          }

          setEmailVerificationSent(true);
          return { success: true, message: 'Verification email sent successfully' };
        } catch (firebaseError) {
          console.error('Firebase verification email error:', firebaseError);

          // Check specific Firebase error codes
          if (firebaseError.code === 'auth/too-many-requests') {
            throw new Error('Too many verification emails sent. Please try again later.');
          }

          // Fall back to backend method if Firebase fails
          console.log('Falling back to backend verification method after Firebase error');
        }
      }

      // Fall back to backend method if no Firebase user available or Firebase method failed
      console.log('Using backend verification method');
      try {
        const res = await api.direct.post('/api/auth/send-verification-email');

        console.log('Backend verification email route returned:', res.data);

        // Store the timestamp of the most recent email
        localStorage.setItem('lastVerificationEmailSent', Date.now().toString());

        setEmailVerificationSent(true);
        return res.data;
      } catch (err) {
        console.error('Failed to send verification email via backend:', err);
        const errorMessage = err.response?.data?.message || 'Failed to send verification email. Please try again later.';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      console.error('Failed to send verification email:', err);
      const errorMessage = err.message || 'Failed to send verification email. Please try again later.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Verify email with token
  const verifyEmail = async (token) => {
    try {
      setLoading(true);
      setError(null);

      if (!token) {
        throw new Error('Invalid verification link. No token or code provided.');
      }

      // Check if we need to wait for user data to be loaded
      if (!user && isAuthenticated) {
        console.log('User data not loaded yet, refreshing user data first');
        try {
          await refreshUserData();
        } catch (refreshError) {
          console.error('Error refreshing user data during verification:', refreshError);
        }
      }

      // Check if this is an oobCode from Firebase instead of a backend token
      const isFirebaseCode = token && token.length > 20; // Firebase codes are typically longer

      if (isFirebaseCode) {
        // This is a Firebase action code, use Firebase's applyActionCode function
        try {
          const { applyActionCode } = await import('firebase/auth');
          const { auth } = await firebaseInitPromise;
          await applyActionCode(auth, token);

          console.log('Firebase email verification successful with code');

          // Force reload the user to get the updated verification status
          if (auth.currentUser) {
            await auth.currentUser.reload();

            if (auth.currentUser.emailVerified) {
              // Successfully verified in Firebase, sync with backend
              try {
                // First try refreshing user data if not available
                const userData = user || await refreshUserData();

                if (!userData || !userData.id) {
                  console.warn('User data not available for sync. Auth state:', isAuthenticated);
                  // User might not be logged in or data not loaded yet
                  return { success: true, message: 'Email verified in Firebase but not synced with backend. Please log in.' };
                }

                const syncResponse = await api.direct.post('/api/auth/sync-verification-status', {
                  firebase_verified: true,
                  oob_code: token, // Send the oob code for server-side verification if needed
                  user_id: userData.id,
                  email: auth.currentUser.email || userData.email,
                  firebase_uid: auth.currentUser.uid,
                  force_update: true
                });

                console.log('Sync response:', syncResponse.data);

                if (syncResponse.data && syncResponse.data.user) {
                  // Update user state with verified status
                  setUser({
                    ...userData,
                    email_verified: 1
                  });

                  // Set local storage flag
                  localStorage.setItem('userEmailVerified', '1');

                  return syncResponse.data;
                }

                return { success: true, message: 'Email verified but sync response was incomplete' };
              } catch (syncError) {
                console.error('Error syncing verification status after Firebase verification:', syncError);
                if (syncError.response) {
                  console.error('Server response:', syncError.response.data);
                }
                // We still consider this a success since Firebase verification worked
                return {
                  success: true,
                  partial: true,
                  message: 'Email verified in Firebase but failed to sync with backend.'
                };
              }
            } else {
              throw new Error('Email verification failed. Please try again or request a new verification email.');
            }
          } else {
            // Firebase user not available
            return {
              success: true,
              partial: true,
              message: 'Email verification processed but could not confirm status. Please log in to continue.'
            };
          }
        } catch (firebaseError) {
          console.error('Firebase applyActionCode error:', firebaseError);
          throw new Error(
            firebaseError.code === 'auth/invalid-action-code'
              ? 'Invalid or expired verification link. Please request a new verification email.'
              : 'Failed to verify email. Please try again.'
          );
        }
      } else {
        // This is a backend token, use the backend API
        try {
          const res = await api.direct.get(`/api/auth/verify-email/${token}`);

          // Make sure we have valid response data containing the verified user
          if (!res.data || !res.data.user || !res.data.user.id) {
            console.error('Invalid response from verification endpoint:', res.data);
            throw new Error('Invalid verification response');
          }

          // Only set verified status if the verification response matches our current user or we have no user
          if (!user || (user && res.data.user.id === user.id)) {
            // Set local storage flag to avoid unnecessary API calls
            localStorage.setItem('userEmailVerified', '1');

            // Update user state with verified status
            if (user) {
              setUser({
                ...user,
                email_verified: 1
              });
            } else if (res.data.user) {
              // If we don't have a user loaded yet, set it from the response
              setUser(res.data.user);
              setIsAuthenticated(true);
            }
          } else {
            console.warn('Verification response user does not match current user, not updating local state');
          }

          return res.data;
        } catch (backendError) {
          console.error('Backend verification error:', backendError);
          if (backendError.response) {
            console.error('Server response:', backendError.response.data);
          }
          throw new Error(backendError.response?.data?.message ||
            backendError.message ||
            'Failed to verify email with backend. The link may be invalid or expired.');
        }
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to verify email. The link may be invalid or expired.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        loading,
        error,
        emailVerificationSent,
        isRateLimited,
        register,
        login,
        logout,
        sendVerificationEmail,
        verifyEmail,
        refreshUserData,
        setUser,
        setIsAuthenticated
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};