// Admin middleware to restrict access to admin-only routes
const isAdmin = (req, res, next) => {
  console.log('Checking admin permissions for user:', req.user);
  
  // Check if user is authenticated and has admin role
  if (!req.user) {
    console.log('No user object in request');
    return res.status(401).json({ message: 'User not authenticated' });
  }
  
  // Check if user has admin role
  if (req.user.role !== 'admin') {
    console.log(`User ${req.user.id} is not an admin (role: ${req.user.role})`);
    return res.status(403).json({ message: 'Admin access required' });
  }
  
  console.log(`Admin access granted for user ${req.user.id}`);
  // User is admin, proceed to the next middleware/route handler
  next();
};

module.exports = {
  isAdmin
};
