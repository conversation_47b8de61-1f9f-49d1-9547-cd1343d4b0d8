import React, { useState, useEffect } from 'react';
import { Grid, Paper, Typography, Box, CircularProgress, Alert } from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import axios from 'axios';
import { useLanguage } from '../../context/LanguageContext';

const Dashboard = () => {
  const { translate } = useLanguage();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/admin/stats'); 
        setStats(response.data);
        setError('');
      } catch (err) {
        console.error("Error fetching dashboard stats:", err);
        setError(translate('Failed to load dashboard statistics.'));
      } finally {
        setLoading(false);
      }
    };
    fetchStats();
  }, [translate]);

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}><CircularProgress /></Box>;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!stats) {
    return <Typography>{translate('No statistics data available.')}</Typography>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {translate('Admin Dashboard')}
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6">{translate('Total Books')}</Typography>
            <Typography variant="h4">{stats.totalBooks ?? 'N/A'}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6">{translate('Total Users')}</Typography>
            <Typography variant="h4">{stats.totalUsers ?? 'N/A'}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6">{translate('Active Leases')}</Typography>
            <Typography variant="h4">{stats.activeLeases ?? 'N/A'}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6">{translate('Overdue Leases')}</Typography>
            <Typography variant="h4">{stats.overdueLeases ?? 'N/A'}</Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard; 