import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import {
  TextField, Button, Paper, Grid, Typography, Snackbar, Alert, IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import AccountSummary from '../components/profile/AccountSummary';
import { useLanguage } from '../context/LanguageContext';
import api from '../utils/api';

const ProfilePage = () => {
  const { user, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
  });
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [leaseStats, setLeaseStats] = useState({ activeLeases: 0, totalLeases: 0 });

  useEffect(() => {
    if (user) {
      setFormData({ username: user.username, email: user.email });
      fetchLeaseStats();
    }
  }, [user]);

  const fetchLeaseStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await api.direct.get('/api/leases/stats', {
        headers: { Authorization: `Bearer ${token}` },
      });
      setLeaseStats(response.data || { activeLeases: 0, totalLeases: 0 });
    } catch (err) {
      console.error('Error fetching lease stats:', err);
    }
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      const response = await api.direct.put('/api/users/me/profile', formData);
      setSnackbar({ open: true, message: translate('Profile updated successfully!'), severity: 'success' });
      setIsEditing(false);
      // After successful update, you might want to refresh user context or page data
    } catch (error) {
      console.error('Profile update error:', error);
      setError(error.response?.data?.message || translate('Failed to update profile.'));
      setSnackbar({ open: true, message: error.response?.data?.message || translate('Failed to update profile.'), severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    logout();
    navigate('/login');
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Grid container justifyContent="center">
      <Grid item xs={12} md={8} lg={6}>
        <Paper elevation={3} sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 3 }}>
          <Typography variant="h4" align="center" gutterBottom>
            {translate('My Profile')}
          </Typography>

          {/* Account Summary Component */}
          <AccountSummary user={user} leaseStats={leaseStats} />

          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              {translate('Personal Information')}
            </Typography>
            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                margin="normal"
                label={translate('Username')}
                name="username"
                value={formData.username}
                onChange={handleChange}
                disabled={!isEditing}
              />
              <TextField
                fullWidth
                margin="normal"
                label={translate('Email')}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                disabled={!isEditing}
              />

              {isEditing ? (
                <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                  <Button type="submit" variant="contained" color="primary" disabled={loading}>
                    {loading ? translate('Updating...') : translate('Update Profile')}
                  </Button>
                  <Button onClick={handleEditToggle} variant="outlined">
                    {translate('Cancel')}
                  </Button>
                </Box>
              ) : (
                <Box sx={{ mt: 2 }}>
                  <Button onClick={handleEditToggle} variant="contained" color="secondary">
                    {translate('Edit Profile')}
                  </Button>
                </Box>
              )}
            </form>
          </Paper>

          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              {translate('Account Actions')}
            </Typography>
            <Button
              variant="contained"
              color="error"
              onClick={handleLogout}
            >
              {translate('Logout')}
            </Button>
          </Paper>

          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              <HistoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              {translate('Lease History')}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<HistoryIcon />}
              onClick={() => navigate('/lease-history')}
            >
              {translate('View Lease History')}
            </Button>
          </Paper>


        </Paper>
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          message={snackbar.message}
          severity={snackbar.severity}
          action={
            <React.Fragment>
              <IconButton size="small" aria-label="close" color="inherit" onClick={handleCloseSnackbar}>
                <CloseIcon fontSize="small" />
              </IconButton>
            </React.Fragment>
          }
        />
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Grid>
    </Grid>
  );
};

export default ProfilePage; 