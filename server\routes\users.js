const express = require('express');
const router = express.Router();
const { authenticateToken } = require('./auth');
const { isAdmin } = require('../middleware/admin');
const { downloadProfileImage } = require('../utils/profileImageHandler');
// Import our Firebase Admin utility
const firebaseAdmin = require('../utils/firebaseAdmin');

// Middleware to ensure user is authenticated
// const { authenticateToken } = authRoutes;

// Get all users (admin only)
router.get('/', authenticateToken, isAdmin, (req, res) => {
  try {
    const db = req.app.locals.db;

    db.all('SELECT id, username, email, role, is_banned, email_verified, class_teacher, grade, profile_completed, created_at FROM users ORDER BY created_at DESC', [], (err, users) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      res.json({ users });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get a single user by ID (admin or the user themselves)
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const userId = req.params.id;

    // Check if user is admin or the user themselves
    if (req.user.role !== 'admin' && req.user.id !== parseInt(userId)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const db = req.app.locals.db;

    db.get('SELECT id, username, email, role, is_banned, email_verified, class_teacher, grade, profile_completed, created_at FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json({ user });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Ban/unban a user (admin only)
router.put('/:id/ban', authenticateToken, isAdmin, (req, res) => {
  try {
    const userId = req.params.id;
    const { is_banned, ban_reason } = req.body;

    const db = req.app.locals.db;

    // Check if user exists and is not an admin
    db.get('SELECT * FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      if (user.role === 'admin') {
        return res.status(400).json({ message: 'Cannot ban an admin user' });
      }

      // Update user's ban status
      db.run(
        'UPDATE users SET is_banned = ?, ban_reason = ? WHERE id = ?',
        [is_banned ? 1 : 0, ban_reason || null, userId],
        function(err) {
          if (err) {
            return res.status(500).json({ message: 'Error updating user', error: err.message });
          }

          if (this.changes === 0) {
            return res.status(404).json({ message: 'User not found or no changes made' });
          }

          res.json({
            message: is_banned ? 'User banned successfully' : 'User unbanned successfully',
            user_id: userId,
            is_banned: is_banned ? true : false
          });
        }
      );
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user role (admin only)
router.put('/:id/role', authenticateToken, isAdmin, (req, res) => {
  try {
    const userId = req.params.id;
    const { role } = req.body;

    if (!role || (role !== 'admin' && role !== 'user')) {
      return res.status(400).json({ message: 'Invalid role. Must be "admin" or "user".' });
    }

    const db = req.app.locals.db;

    // Update user's role
    db.run(
      'UPDATE users SET role = ? WHERE id = ?',
      [role, userId],
      function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error updating user role', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'User not found or no changes made' });
        }

        res.json({
          message: 'User role updated successfully',
          user_id: userId,
          role
        });
      }
    );
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete a user (admin only)
router.delete('/:id', authenticateToken, isAdmin, (req, res) => {
  try {
    const userId = req.params.id;
    const db = req.app.locals.db;

    // Check if user exists and is not an admin
    db.get('SELECT * FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      if (user.role === 'admin') {
        return res.status(400).json({ message: 'Cannot delete an admin user' });
      }

      // Delete all user's leases first
      db.run('DELETE FROM leases WHERE user_id = ?', [userId], (err) => {
        if (err) {
          return res.status(500).json({ message: 'Error deleting user leases', error: err.message });
        }

        // Delete the user
        db.run('DELETE FROM users WHERE id = ?', [userId], function(err) {
          if (err) {
            return res.status(500).json({ message: 'Error deleting user', error: err.message });
          }

          if (this.changes === 0) {
            return res.status(404).json({ message: 'User not found or no changes made' });
          }

          res.json({
            message: 'User deleted successfully',
            user_id: userId
          });
        });
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin route to verify a user's email
router.post('/:id/verify-email', authenticateToken, isAdmin, async (req, res) => {
  try {
    const userId = req.params.id;
    const db = req.app.locals.db;

    console.log(`Admin initiating email verification for user ID: ${userId}`);

    db.get('SELECT * FROM users WHERE id = ?', [userId], async (err, user) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        console.warn(`User not found: ${userId}`);
        return res.status(404).json({ message: 'User not found' });
      }

      try {
        // Update user verification status in our database
        db.run(
          'UPDATE users SET email_verified = 1 WHERE id = ?',
          [userId],
          function(err) {
            if (err) {
              console.error('Database update error:', err);
              return res.status(500).json({ message: 'Error updating verification status', error: err.message });
            }

            // If using Firebase Admin SDK, also update Firebase Auth
            if (firebaseAdmin.auth) {
              try {
                // Find the Firebase user by email
                firebaseAdmin.getUserByEmail(user.email)
                  .then((firebaseUser) => {
                    if (firebaseUser) {
                      // Update user's email_verified status in Firebase
                      firebaseAdmin.auth.updateUser(firebaseUser.uid, {
                        emailVerified: true
                      })
                      .then(() => {
                        console.log(`Updated Firebase verification status for ${user.email}`);
                      })
                      .catch((updateError) => {
                        console.error('Error updating Firebase user:', updateError);
                      });
                    }
                  })
                  .catch((firebaseError) => {
                    console.error('Firebase user not found or error:', firebaseError);
                    // Continue anyway as we've updated our database
                  });
              } catch (firebaseError) {
                console.error('Error with Firebase admin:', firebaseError);
                // Don't fail the request if Firebase update fails
              }
            } else {
              console.log('Firebase Auth not available, skipping Firebase auth update');
            }

            console.log(`User ${userId} (${user.email}) verified successfully by admin`);

            res.json({
              message: 'User email verified successfully',
              user_id: userId
            });
          }
        );
      } catch (error) {
        console.error('Server error during admin verification:', error);
        res.status(500).json({ message: 'Error verifying email', error: error.message });
      }
    });
  } catch (error) {
    console.error('Unhandled error in admin verification:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Send verification email (admin or user themselves)
router.post('/:id/send-verification', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.id;

    // Check if user is admin or the user themselves
    if (req.user.role !== 'admin' && req.user.id !== parseInt(userId)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const db = req.app.locals.db;

    // Get the user
    db.get('SELECT * FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Email verification removed - users are automatically verified
      res.json({
        message: 'Email verification is no longer required - users are automatically verified',
        user_id: userId
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user data (PATCH endpoint for partial updates)
router.patch('/me', authenticateToken, (req, res) => {
  try {
    const userId = req.user.id;
    const allowedFields = ['firebase_uid', 'class_teacher', 'grade'];
    const updates = {};

    // Only allow specific fields to be updated
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ message: 'No valid fields to update' });
    }

    const db = req.app.locals.db;

    // Build dynamic SQL query
    const setClause = Object.keys(updates).map(field => `${field} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(userId);

    db.run(
      `UPDATE users SET ${setClause} WHERE id = ?`,
      values,
      function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error updating user', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'User not found or no changes made' });
        }

        res.json({
          message: 'User updated successfully',
          user_id: userId,
          updated_fields: updates
        });
      }
    );
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user profile information (class teacher and grade)
router.put('/me/profile', authenticateToken, (req, res) => {
  try {
    const { class_teacher, grade } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!class_teacher || !grade) {
      return res.status(400).json({ message: 'Class teacher and grade are required' });
    }

    if (typeof class_teacher !== 'string' || typeof grade !== 'string') {
      return res.status(400).json({ message: 'Class teacher and grade must be strings' });
    }

    if (class_teacher.trim().length === 0 || grade.trim().length === 0) {
      return res.status(400).json({ message: 'Class teacher and grade cannot be empty' });
    }

    const db = req.app.locals.db;

    // Update user's profile information
    db.run(
      'UPDATE users SET class_teacher = ?, grade = ?, profile_completed = 1 WHERE id = ?',
      [class_teacher.trim(), grade.trim(), userId],
      function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error updating profile', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'User not found or no changes made' });
        }

        res.json({
          message: 'Profile updated successfully',
          user_id: userId,
          class_teacher: class_teacher.trim(),
          grade: grade.trim(),
          profile_completed: true
        });
      }
    );
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get user profile completion status
router.get('/me/profile-status', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;
    const userId = req.user.id;

    db.get('SELECT profile_completed, class_teacher, grade FROM users WHERE id = ?', [userId], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json({
        profile_completed: user.profile_completed === 1,
        class_teacher: user.class_teacher,
        grade: user.grade
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin endpoint to update user profile information
router.put('/:id/profile', authenticateToken, isAdmin, (req, res) => {
  try {
    const { class_teacher, grade } = req.body;
    const userId = req.params.id;

    // Validate input
    if (!class_teacher || !grade) {
      return res.status(400).json({ message: 'Class teacher and grade are required' });
    }

    if (typeof class_teacher !== 'string' || typeof grade !== 'string') {
      return res.status(400).json({ message: 'Class teacher and grade must be strings' });
    }

    const db = req.app.locals.db;

    // Update user's profile information
    db.run(
      'UPDATE users SET class_teacher = ?, grade = ?, profile_completed = 1 WHERE id = ?',
      [class_teacher.trim(), grade.trim(), userId],
      function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error updating profile', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'User not found or no changes made' });
        }

        res.json({
          message: 'Profile updated successfully',
          user_id: userId,
          class_teacher: class_teacher.trim(),
          grade: grade.trim(),
          profile_completed: true
        });
      }
    );
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;