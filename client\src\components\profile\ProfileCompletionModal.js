import React, { useState, useContext, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { School as SchoolIcon } from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';
import { AuthContext } from '../../context/AuthContext';
import api from '../../utils/api';

const ProfileCompletionModal = ({ open, onClose, onComplete, user }) => {
  const [formData, setFormData] = useState({
    class_teacher: '',
    grade: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { translate } = useLanguage();
  const { refreshUserData } = useContext(AuthContext);

  // Populate form with existing data when modal opens
  useEffect(() => {
    if (open && user) {
      setFormData({
        class_teacher: user.class_teacher || '',
        grade: user.grade || ''
      });
    }
  }, [open, user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.class_teacher.trim() || !formData.grade.trim()) {
      setError(translate('Both class teacher and grade are required.'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await api.direct.put('/api/users/me/profile', {
        class_teacher: formData.class_teacher.trim(),
        grade: formData.grade.trim()
      });

      // Refresh user data to get updated profile information
      if (refreshUserData) {
        await refreshUserData();
      }

      // Call the completion callback with a delay to ensure user data is updated
      setTimeout(() => {
        if (onComplete) {
          onComplete({
            class_teacher: formData.class_teacher.trim(),
            grade: formData.grade.trim(),
            profile_completed: true
          });
        }

        // Close the modal
        onClose();
      }, 500);
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(
        err.response?.data?.message ||
        translate('Failed to update profile. Please try again.')
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // Always allow closing when not loading
    if (!loading) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={true}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SchoolIcon color="primary" />
          <Typography variant="h6">
            {translate('Complete Your Profile')}
          </Typography>
        </Box>
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            {translate('To rent books and make suggestions, please provide your class teacher and grade information.')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            fullWidth
            label={translate('Class Teacher')}
            name="class_teacher"
            value={formData.class_teacher}
            onChange={handleChange}
            margin="normal"
            required
            disabled={loading}
            placeholder={translate('Enter your class teacher\'s name')}
            helperText={translate('Example: Mrs. Smith, Mr. Johnson')}
          />

          <TextField
            fullWidth
            label={translate('Grade/Class')}
            name="grade"
            value={formData.grade}
            onChange={handleChange}
            margin="normal"
            required
            disabled={loading}
            placeholder={translate('Enter your grade or class')}
            helperText={translate('Example: 9A, Grade 10, Class 11B')}
          />
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1 }}>
          {/* Always show cancel button */}
          <Button
            onClick={handleClose}
            disabled={loading}
            color="secondary"
          >
            {translate('Cancel')}
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading || !formData.class_teacher.trim() || !formData.grade.trim()}
            startIcon={loading ? <CircularProgress size={20} /> : null}
            sx={{ flex: 1 }}
          >
            {loading ? translate('Saving...') : translate('Save Profile')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ProfileCompletionModal;
