import React, { useState, useEffect, useContext, useRef } from 'react';
import { Button, useMediaQuery, CircularProgress, Typography, Box } from '@mui/material';
import {
  signInWithPopup,
  sendEmailVerification,
  setPersistence,
  browserLocalPersistence,
  GoogleAuthProvider,
} from 'firebase/auth';
import { firebaseInitPromise } from '../../firebase';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useSecurity } from '../../context/SecurityContext';
import { useLanguage } from '../../context/LanguageContext';
import GoogleIcon from '@mui/icons-material/Google';
import { useTheme } from '@mui/material/styles';
import useRecaptcha from '../../hooks/useRecaptcha';
import api from '../../utils/api';

const GoogleAuth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setUser, setIsAuthenticated } = useContext(AuthContext);
  const { translate } = useLanguage();
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);
  const [error, setError] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const authWindowRef = useRef(null); // Ref to keep track of the popup window
  const { securityStatus } = useSecurity();
  const { executeRecaptcha, isReady: recaptchaReady } = useRecaptcha(securityStatus.requiresRecaptcha);



  // Setup message listener for the popup window (for mobile flow)
  useEffect(() => {
    const handleAuthMessage = async (event) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) {
        console.warn("[GoogleAuth] Received message from untrusted origin:", event.origin);
        return;
      }

      // Check if this is our expected auth credential message
      if (event.data && event.data.type === 'GOOGLE_AUTH_CREDENTIAL' && event.data.payload) {
        console.log('[GoogleAuth] Received auth credential message from popup:', event.data.payload);
        setIsProcessingAuth(true);
        setError(null); // Clear previous errors

        // Close the popup window now that we have the data
        if (authWindowRef.current && !authWindowRef.current.closed) {
            authWindowRef.current.close();
            console.log("[GoogleAuth] Closed auth popup window.");
        }
        authWindowRef.current = null; // Clear the ref

        try {
          const { displayName, email, uid, photoURL, idToken } = event.data.payload;

          // We have the necessary info (including idToken) directly from the popup
          // No need to call Firebase functions here, just process the result
          console.log("[GoogleAuth] Processing credentials received via postMessage.");

          // Call backend with the received data
          await handleAuthBackendProcessing({ displayName, email, uid, photoURL, idToken });

          // Navigate after successful backend processing
          const from = location.state?.from || '/';
          console.log("[GoogleAuth] Mobile auth successful, navigating to:", from);
          navigate(from);

        } catch (err) {
          console.error('[GoogleAuth] Error processing auth message payload:', err);
          setError('Failed to complete authentication after receiving credentials.');
        } finally {
          setIsProcessingAuth(false);
        }
      }
    };

    // Add the message listener
    console.log("[GoogleAuth] Adding message listener for auth popup.");
    window.addEventListener('message', handleAuthMessage);

    // Cleanup listener on component unmount
    return () => {
      console.log("[GoogleAuth] Removing message listener.");
      window.removeEventListener('message', handleAuthMessage);
      // Also ensure the popup is closed if the component unmounts unexpectedly
      if (authWindowRef.current && !authWindowRef.current.closed) {
          authWindowRef.current.close();
          authWindowRef.current = null;
      }
    };
  }, [navigate, location, setUser, setIsAuthenticated]); // Dependencies needed for processing

  const handleGoogleSignIn = async () => {
    try {
      setError(null);
      setIsProcessingAuth(true); // Set loading state immediately

      if (securityStatus.mode === 'high') {
        alert('System is in lockdown mode. Only administrators can sign in.');
        setIsProcessingAuth(false);
        return;
      }

      if (isMobile) {
        console.log("[GoogleAuth] Using mobile authentication flow (window.open + postMessage)");

        // Ensure only one auth window is open at a time
        if (authWindowRef.current && !authWindowRef.current.closed) {
            authWindowRef.current.focus(); // Bring existing window to front
            console.log("[GoogleAuth] Auth popup already open, focusing it.");
            setIsProcessingAuth(false); // Stop loading as window is already open
            return;
        }

        const popupWidth = Math.min(window.outerWidth - 20, 500);
        const popupHeight = Math.min(window.outerHeight - 40, 650);
        const popupLeft = window.screenX + (window.outerWidth - popupWidth) / 2;
        const popupTop = window.screenY + (window.outerHeight - popupHeight) / 2;

        const authUrl = `/auth-popup`; // Path to the dedicated popup page
        const newAuthWindow = window.open(
            authUrl,
            'googleAuthPopup',
            `width=${popupWidth},height=${popupHeight},left=${popupLeft},top=${popupTop},resizable=yes,scrollbars=yes`
        );

        if (newAuthWindow) {
            console.log('[GoogleAuth] Auth popup window opened.');
            authWindowRef.current = newAuthWindow;
            // Monitor the popup window in case the user closes it manually
            const timer = setInterval(() => {
                if (newAuthWindow.closed) {
                    clearInterval(timer);
                    if (isProcessingAuth) { // Only stop loading if we were still waiting
                       console.log("[GoogleAuth] Auth popup closed manually by user before completion.");
                       setIsProcessingAuth(false);
                       setError("Authentication cancelled.");
                    }
                    authWindowRef.current = null; // Clear ref if closed
                }
            }, 500);
        } else {
            console.error('[GoogleAuth] Failed to open auth popup window. Check browser popup blocker.');
            setError('Could not open authentication window. Please disable popup blockers and try again.');
            setIsProcessingAuth(false);
        }
        // Keep isProcessingAuth=true while waiting for the message from the popup

      } else {
        // Desktop flow: Use standard signInWithPopup
        console.log("[GoogleAuth] Using desktop authentication flow (signInWithPopup)");
        try {
          const { auth, googleProvider } = await firebaseInitPromise;
          if (!auth || !googleProvider) {
            console.error('[GoogleAuth] Firebase not initialized: auth or googleProvider is undefined', { auth, googleProvider });
            setError('Google login is temporarily unavailable. Please refresh the page or try again later.');
            setIsProcessingAuth(false);
            return;
          }
          const result = await signInWithPopup(auth, googleProvider);
          console.log("[GoogleAuth] Desktop signInWithPopup successful");

          // Call the main processing function with the Firebase result
          await handleGoogleAuthResult(result);

          // Navigate on success
          const { from } = location.state || { from: '/' };
          navigate(from);

        } catch (error) {
          console.error('[GoogleAuth] Desktop signInWithPopup error:', error);
          if (error.code === 'auth/popup-closed-by-user') {
            setError('Login cancelled. Please try again.');
          } else if (error.code === 'auth/cancelled-popup-request' || error.code === 'auth/popup-blocked') {
            setError('Popup issue detected. Please ensure popups are allowed and try again.');
          } else {
            setError('Failed to sign in with Google. Please try again.');
          }
        } finally {
           setIsProcessingAuth(false); // Ensure loading stops
        }
      }
    } catch (error) { // Catch errors from the initial setup/checks
      console.error('[GoogleAuth] General handleGoogleSignIn error:', error);
      setError('An unexpected error occurred during sign-in setup.');
      setIsProcessingAuth(false);
    }
  };

  // Refactored backend processing logic used by both flows
  const handleAuthBackendProcessing = async ({ displayName, email, uid, photoURL, idToken }) => {
    console.log(`[handleAuthBackendProcessing] Processing user: ${email}`);
    try {
      localStorage.setItem('lastLoginEmail', email);

      // Get reCAPTCHA token if required
      let recaptchaToken = null;
      if (securityStatus.requiresRecaptcha) {
        if (!recaptchaReady) {
          throw new Error('reCAPTCHA is loading. Please wait and try again.');
        }

        recaptchaToken = await executeRecaptcha('google_login');
        if (!recaptchaToken) {
          throw new Error('reCAPTCHA verification failed. Please try again.');
        }
      }

      console.log("[handleAuthBackendProcessing] Sending user data and ID token to backend...");
      const response = await api.direct.post('/api/auth/google', {
        username: displayName || email.split('@')[0],
        email,
        googleId: uid,
        photoURL,
        idToken, // Send the token received from popup or Firebase result
        ...(recaptchaToken && { recaptchaToken })
      });

      console.log('[handleAuthBackendProcessing] Backend response:', response.data ? "Successful" : "Failed or unexpected response");

      if (!response.data || !response.data.token) {
        throw new Error('Invalid response from server during Google Auth backend processing.');
      }

      localStorage.setItem('token', response.data.token);
      console.log("[handleAuthBackendProcessing] Auth token saved to localStorage.");

      api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
      console.log("[handleAuthBackendProcessing] Set authorization header for API requests.");

      // Make sure to store the email_verified status in localStorage
      if (response.data.user && response.data.user.email_verified === 1) {
        localStorage.setItem('userEmailVerified', '1');
      }

      setUser(response.data.user);
      setIsAuthenticated(true);
      console.log("[handleAuthBackendProcessing] Auth context updated successfully.");

      return response.data;
    } catch (error) {
      console.error("[handleAuthBackendProcessing] Error:", error);
      // Rethrow the error to be handled by the calling function (handleGoogleSignIn or message listener)
      throw error;
    }
  };

  // Original function now specifically for handling Firebase Auth results (desktop flow)
  const handleGoogleAuthResult = async (result) => {
    console.log("[handleGoogleAuthResult] Handling Firebase auth result...");
    const { user } = result;
    try {
      // Get a fresh ID token from the Firebase user object
      const idToken = await user.getIdToken(true);
      console.log("[handleGoogleAuthResult] Retrieved ID token from Firebase user.");

      // Send email verification if needed (only possible with Firebase user object)
      if (result.operationType === 'signIn' && user.sendEmailVerification && !user.emailVerified) {
        try {
          await sendEmailVerification(user);
          console.log('[handleGoogleAuthResult] Verification email sent to:', user.email);
        } catch (verificationError) {
          console.error('[handleGoogleAuthResult] Error sending verification email:', verificationError);
        }
      }

      // Call the common backend processing function
      return await handleAuthBackendProcessing({
          displayName: user.displayName,
          email: user.email,
          uid: user.uid,
          photoURL: user.photoURL,
          idToken
      });
    } catch (error) {
      console.error("[handleGoogleAuthResult] Error:", error);
      // We might want specific error handling here or just let it propagate
      setError('Failed to process Firebase authentication result.');
      throw error; // Rethrow to ensure calling function knows about the error
    }
  };

  // --- Render Logic ---
  if (isProcessingAuth) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 2 }}>
        <CircularProgress size={24} sx={{ mb: 1 }} />
        <Typography variant="body2">Authenticating with Google...</Typography>
        {isMobile && <Typography variant="caption" color="text.secondary">(Check popup window)</Typography>}
      </Box>
    );
  }

  return (
    <>
      <Button
        variant="outlined"
        fullWidth
        startIcon={<GoogleIcon />}
        onClick={handleGoogleSignIn}
        disabled={isProcessingAuth} // Disable button while processing
        sx={{ mt: 2, mb: 2 }}
      >
        {translate('Sign in with Google')}
      </Button>

      {error && (
        <Typography color="error" variant="body2" align="center" sx={{ mt: 1 }}>
          {error}
        </Typography>
      )}
    </>
  );
};

export default GoogleAuth;