import React, { useState, useEffect, useContext } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Divider,
  Avatar,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar
} from '@mui/material';
import {
  Person as PersonIcon,
  Book as BookIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Edit as EditIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import api from '../utils/api';
import { AuthContext } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import ProfileCompletionModal from '../components/profile/ProfileCompletionModal';
import useProfileCompletion from '../hooks/useProfileCompletion';

const UserProfile = () => {
  const { user } = useContext(AuthContext);
  const { translate } = useLanguage();
  const { profileStatus, refetchProfileStatus } = useProfileCompletion();
  const [leases, setLeases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [returnSuccess, setReturnSuccess] = useState(false);
  const [calendarButtonClicked, setCalendarButtonClicked] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [leaseToCancelId, setLeaseToCancelId] = useState(null);
  const [leaseToCancelTitle, setLeaseToCancelTitle] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [profileEditModalOpen, setProfileEditModalOpen] = useState(false);
  const navigate = useNavigate();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4
      }
    }
  };

  // Fetch user's leases initially
  useEffect(() => {
    const fetchInitialLeases = async () => {
      setLoading(true);
      setError(''); // Clear previous errors
      try {
        console.log('Fetching initial leases...');
        const res = await api.direct.get('/api/leases');
        console.log('Initial Leases response:', res.data);
        const leasesData = res.data?.leases || [];
        setLeases(leasesData);
      } catch (err) {
        console.error('Error fetching initial leases:', err);
        setError('Failed to load your leases. Please try again later.');
        setLeases([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialLeases();
  }, []); // Empty dependency array: runs only on mount

  // Refetch leases after a return or cancellation action
  useEffect(() => {
    // This flag helps track if the effect is running due to an actual action
    let isMounted = true;
    const refetchLeases = async () => {
      // Optional: You might want to briefly show a loading state during refetch
      // setLoading(true);
      setError(''); // Clear previous errors
      try {
        console.log('Refetching leases due to action...');
        const res = await api.direct.get('/api/leases');
        console.log('Refetched Leases response:', res.data);
        const leasesData = res.data?.leases || [];
        if (isMounted) {
            setLeases(leasesData);
        }
      } catch (err) {
        console.error('Error refetching leases:', err);
         if (isMounted) {
            setError('Failed to update lease list after action. Please refresh the page.');
         }
      } finally {
        // Optional: If you set loading to true above, set it back to false
        // if (isMounted) setLoading(false);
      }
    };

    // Check if this effect run is due to an actual change in returnSuccess
    // (i.e., not the initial mount where returnSuccess might be default)
    // We rely on the fact that returnSuccess is toggled (true/false)
    // This check might need adjustment based on initial state logic
    refetchLeases();

    return () => {
        isMounted = false; // Cleanup function to prevent state updates on unmounted component
    };
  }, [returnSuccess]); // Dependency: runs when returnSuccess changes

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle book return
  const handleReturnBook = async (leaseId) => {
    try {
      await api.direct.put(`/api/leases/${leaseId}/return`);
      setReturnSuccess(prev => !prev); // Toggle to trigger useEffect
      setSuccessMessage('Book returned successfully!');
      setShowSuccessAlert(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to return book. Please try again.');
    }
  };

  // Handle cancel lease dialog open
  const handleCancelDialogOpen = (leaseId, leaseTitle) => {
    setLeaseToCancelId(leaseId);
    setLeaseToCancelTitle(leaseTitle);
    setCancelDialogOpen(true);
  };

  // Handle cancel lease dialog close
  const handleCancelDialogClose = () => {
    setCancelDialogOpen(false);
    setLeaseToCancelId(null);
    setLeaseToCancelTitle('');
  };

  // Handle lease cancellation
  const handleCancelLease = async () => {
    try {
      const response = await api.direct.put(`/api/leases/${leaseToCancelId}/cancel`);
      setReturnSuccess(prev => !prev); // Toggle to trigger useEffect
      handleCancelDialogClose();
      setSuccessMessage(response.data.message || 'Lease cancelled successfully!');
      setShowSuccessAlert(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to cancel lease. Please try again.');
      handleCancelDialogClose();
    }
  };

  // Handle success alert close
  const handleSuccessAlertClose = () => {
    setShowSuccessAlert(false);
  };

  // Handle profile edit modal
  const handleProfileEditOpen = () => {
    setProfileEditModalOpen(true);
  };

  const handleProfileEditClose = () => {
    setProfileEditModalOpen(false);
  };

  const handleProfileEditComplete = (profileData) => {
    setSuccessMessage(translate('Profile updated successfully!'));
    setShowSuccessAlert(true);
    setProfileEditModalOpen(false);
    // Refresh the profile status to get the latest data
    refetchProfileStatus();
  };

  // Check if a lease is cancellable (within 3 hours of creation)
  const isCancellable = (leaseDate) => {
    const leaseTimestamp = new Date(leaseDate).getTime();
    const currentTimestamp = new Date().getTime();
    const timeDifference = currentTimestamp - leaseTimestamp;
    const threeHoursInMs = 3 * 60 * 60 * 1000;
    return timeDifference <= threeHoursInMs;
  };

  // Filter leases by status
  const activeLeases = leases.filter(lease => lease.status === 'active' || lease.status === 'pending');
  const returnedLeases = leases.filter(lease => lease.status === 'returned' || lease.status === 'cancelled');

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div variants={itemVariants}>
          <Typography variant="h3" component="h1" gutterBottom>
            {translate('My Profile')}
          </Typography>
        </motion.div>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Success message */}
        <Snackbar
          open={showSuccessAlert}
          autoHideDuration={6000}
          onClose={handleSuccessAlertClose}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert
            onClose={handleSuccessAlertClose}
            severity="success"
            variant="filled"
            sx={{ width: '100%' }}
          >
            {successMessage}
          </Alert>
        </Snackbar>

        <Grid container spacing={4}>
          {/* User Info */}
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Paper elevation={2} sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{ width: 100, height: 100, mb: 2, bgcolor: 'primary.main' }}
                  >
                    <PersonIcon fontSize="large" />
                  </Avatar>
                  <Typography variant="h5" gutterBottom>
                    {user?.username}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {user?.email}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    {translate('Account Type:')} <Chip label={user?.role === 'admin' ? translate('Administrator') : translate('User')} color="primary" size="small" />
                  </Typography>
                  <Typography variant="subtitle1" gutterBottom>
                    {translate('Active Leases:')} {activeLeases.length}
                  </Typography>
                  <Typography variant="subtitle1" gutterBottom>
                    {translate('Total Leases:')} {leases.length}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Profile Information Section */}
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SchoolIcon color="primary" />
                      {translate('Profile Information')}
                    </Typography>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={handleProfileEditOpen}
                      variant="outlined"
                    >
                      {translate('Edit')}
                    </Button>
                  </Box>

                  {profileStatus.loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                      <CircularProgress size={20} />
                    </Box>
                  ) : profileStatus.profile_completed ? (
                    <Box>
                      <Typography variant="body2" gutterBottom>
                        <strong>{translate('Class Teacher:')}</strong> {profileStatus.class_teacher || user?.class_teacher || translate('Not specified')}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>{translate('Grade/Class:')}</strong> {profileStatus.grade || user?.grade || translate('Not specified')}
                      </Typography>
                    </Box>
                  ) : (
                    <Box>
                      <Typography variant="body2" color="warning.main" gutterBottom>
                        {translate('Profile incomplete. Please complete your profile to rent books and make suggestions.')}
                      </Typography>
                      <Button
                        size="small"
                        variant="contained"
                        color="warning"
                        onClick={handleProfileEditOpen}
                        sx={{ mt: 1 }}
                      >
                        {translate('Complete Profile')}
                      </Button>
                    </Box>
                  )}
                </Box>
              </Paper>
            </motion.div>
          </Grid>

          {/* Leases */}
          <Grid item xs={12} md={8}>
            <motion.div variants={itemVariants}>
              <Paper elevation={2} sx={{ p: 3 }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs value={tabValue} onChange={handleTabChange} aria-label="lease tabs">
                    <Tab label={`${translate('Active Leases')} (${activeLeases.length})`} />
                    <Tab label={`${translate('Past Leases')} (${returnedLeases.length})`} />
                  </Tabs>
                </Box>

                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <div role="tabpanel" hidden={tabValue !== 0}>
                    {tabValue === 0 && (
                      activeLeases.length > 0 ? (
                        <Grid container spacing={3}>
                          {activeLeases.map(lease => (
                            <Grid item xs={12} md={6} lg={4} key={lease.id || `lease-${Math.random()}`}>
                              <Card variant="outlined" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                                <CardContent sx={{ flexGrow: 1 }}>
                                  <Grid container spacing={2}>
                                    <Grid item xs={12} sm={4}>
                                      <Box
                                        component="img"
                                        src={lease.cover_image || '/placeholder-book.png'}
                                        alt={lease.title || 'Book cover'}
                                        sx={{
                                          width: '100%',
                                          height: 'auto',
                                          maxHeight: '150px',
                                          objectFit: 'cover',
                                          borderRadius: 1
                                        }}
                                        onError={(e) => {
                                          e.target.src = '/placeholder-book.png';
                                        }}
                                      />
                                    </Grid>
                                    <Grid item xs={12} sm={8}>
                                      <Typography variant="h6" gutterBottom>
                                        {lease.title || translate('Untitled Book')}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary" gutterBottom>
                                        {translate('by')} {lease.author || translate('Unknown Author')}
                                      </Typography>
                                      <Box sx={{ mt: 2 }}>
                                        <Typography variant="body2">
                                          <strong>{translate('Leased on:')}</strong> {formatDate(lease.lease_date)}
                                        </Typography>
                                        <Typography variant="body2">
                                          <strong>{translate('Due date:')}</strong> {formatDate(lease.due_date)}
                                        </Typography>
                                        <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                                          <Chip
                                            label={lease.status === 'pending' ? translate('Pending') :
                                                  new Date(lease.due_date) < new Date() ? translate('Overdue') : translate('Active')}
                                            color={lease.status === 'pending' ? 'warning' :
                                                  new Date(lease.due_date) < new Date() ? 'error' : 'success'}
                                            size="small"
                                          />
                                          {isCancellable(lease.lease_date) && (
                                            <Chip
                                              label={translate('Cancellable')}
                                              color="default"
                                              size="small"
                                              icon={<WarningIcon fontSize="small" />}
                                            />
                                          )}
                                        </Box>
                                      </Box>
                                    </Grid>
                                  </Grid>
                                </CardContent>
                                <CardActions>
                                  <Button
                                    size="small"
                                    color="primary"
                                    onClick={() => handleReturnBook(lease.id)}
                                  >
                                    {translate('Return Book')}
                                  </Button>
                                  {isCancellable(lease.lease_date) && (
                                    <Button
                                      size="small"
                                      color="error"
                                      startIcon={<CancelIcon />}
                                      onClick={() => handleCancelDialogOpen(lease.id, lease.title)}
                                    >
                                      {translate('Cancel Lease')}
                                    </Button>
                                  )}
                                </CardActions>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                          <Typography variant="body1" align="center" gutterBottom>
                            {translate('You don\'t have any active leases.')}
                          </Typography>
                          <Button
                            variant="contained"
                            color="primary"
                            onClick={() => navigate('/lease-history')}
                            sx={{ mt: 2 }}
                          >
                            {translate('View Lease Calendar')}
                          </Button>
                        </Box>
                      )
                    )}
                  </div>
                )}

                <div role="tabpanel" hidden={tabValue !== 1}>
                  {tabValue === 1 && (
                    returnedLeases.length > 0 ? (
                      <Grid container spacing={3}>
                        {returnedLeases.map(lease => lease && (
                          <Grid item xs={12} md={6} lg={4} key={lease.id || `past-lease-${Math.random()}`}>
                            <Card variant="outlined" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                              <CardContent sx={{ flexGrow: 1 }}>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} sm={4}>
                                    <Box
                                      component="img"
                                      src={lease.cover_image || '/placeholder-book.png'}
                                      alt={lease.title || 'Book cover'}
                                      sx={{
                                        width: '100%',
                                        height: 'auto',
                                        maxHeight: '150px',
                                        objectFit: 'cover',
                                        borderRadius: 1
                                      }}
                                      onError={(e) => {
                                        e.target.src = '/placeholder-book.png';
                                      }}
                                    />
                                  </Grid>
                                  <Grid item xs={12} sm={8}>
                                    <Typography variant="h6" gutterBottom>
                                      {lease.title || translate('Untitled Book')}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary" gutterBottom>
                                      {translate('by')} {lease.author || translate('Unknown Author')}
                                    </Typography>
                                    <Box sx={{ mt: 2 }}>
                                      <Typography variant="body2">
                                        <strong>{translate('Leased on:')}</strong> {formatDate(lease.lease_date)}
                                      </Typography>
                                      {lease.status === 'returned' ? (
                                        <Typography variant="body2">
                                          <strong>{translate('Returned on:')}</strong> {formatDate(lease.return_date)}
                                        </Typography>
                                      ) : (
                                        <Typography variant="body2">
                                          <strong>{translate('Cancelled on:')}</strong> {formatDate(lease.return_date)}
                                        </Typography>
                                      )}
                                      <Chip
                                        label={lease.status.charAt(0).toUpperCase() + lease.status.slice(1)}
                                        color={lease.status === 'returned' ? 'default' : 'default'}
                                        size="small"
                                        sx={{ mt: 1 }}
                                      />
                                    </Box>
                                  </Grid>
                                </Grid>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Typography variant="body1" align="center" sx={{ py: 4 }}>
                        {translate('You don\'t have any past leases yet.')}
                      </Typography>
                    )
                  )}
                </div>
              </Paper>
            </motion.div>
          </Grid>
        </Grid>

        {/* Cancel Lease Confirmation Dialog */}
        <Dialog
          open={cancelDialogOpen}
          onClose={handleCancelDialogClose}
          aria-labelledby="cancel-lease-dialog-title"
          aria-describedby="cancel-lease-dialog-description"
        >
          <DialogTitle id="cancel-lease-dialog-title">
            {translate('Cancel Lease')}
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="cancel-lease-dialog-description">
              {translate('Are you sure you want to cancel your lease for')} <strong>{leaseToCancelTitle}</strong>?
              {translate('This action cannot be undone. You can only cancel leases within 3 hours of creating them.')}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelDialogClose} color="primary">
              {translate('No, Keep Lease')}
            </Button>
            <Button onClick={handleCancelLease} color="error" variant="contained" autoFocus>
              {translate('Yes, Cancel Lease')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Profile Edit Modal */}
        <ProfileCompletionModal
          open={profileEditModalOpen}
          onClose={handleProfileEditClose}
          onComplete={handleProfileEditComplete}
          user={user}
        />
      </Container>
    </motion.div>
  );
};

export default UserProfile;