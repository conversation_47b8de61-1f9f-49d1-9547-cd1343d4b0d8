import React, { useState, useRef, useEffect, useContext } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  CircularProgress,
  Alert,
  IconButton
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  CameraAlt as CameraIcon,
  Close as CloseIcon,
  AddCircleOutline as AddIcon
} from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';
import { AuthContext } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const BookForm = ({ 
  bookData = null, 
  onSave,
  onCancel,
  categories = [],
  isLoading = false,
  isSaving = false
}) => {
  // Get translate function
  const { translate } = useLanguage();
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  
  // Check if user verification is required and redirect if not verified
  useEffect(() => {
    if (user && user.email_verified === 0) {
      navigate('/verify-email');
    }
  }, [user, navigate]);
  
  // State for form data
  const [editedBook, setEditedBook] = useState(null);
  const [newCategory, setNewCategory] = useState('');
  const [imageUploadMode, setImageUploadMode] = useState('url');
  const [coverImageFile, setCoverImageFile] = useState(null);
  const [takingPhoto, setTakingPhoto] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [isValid, setIsValid] = useState(false);
  const [coverPreviewError, setCoverPreviewError] = useState(false);
  
  // Ensure categories is always an array
  const categoryOptions = Array.isArray(categories) ? categories : [];
  
  // Refs
  const fileInputRef = useRef(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  
  // Initialize form with book data when available
  useEffect(() => {
    if (bookData) {
      // Initialize with book data and add default values for library management
      const bookWithImage = {
        ...bookData,
        total_copies: bookData.total_copies || 1,
        available_copies: bookData.available_copies || 1,
        // Ensure cover_image is set from either cover_image or image_url
        cover_image: bookData.cover_image || bookData.image_url || ''
      };
      
      setEditedBook(bookWithImage);
      
      // Set appropriate image upload mode based on cover_image value
      if (bookWithImage.cover_image && bookWithImage.cover_image.startsWith('data:image')) {
        setImageUploadMode('file');
      } else if (bookWithImage.cover_image) {
        setImageUploadMode('url');
      }
    } else {
      // Initialize with empty values
      setEditedBook({
        title: '',
        author: '',
        isbn: '',
        isbn_10: '',
        isbn_13: '',
        published_year: '',
        publisher: '',
        category: '',
        description: '',
        cover_image: '',
        total_copies: 1,
        available_copies: 1
      });
    }
    
    // Reset other state
    setNewCategory('');
    setCoverImageFile(null);
    setTakingPhoto(false);
    setFormErrors({});
    setCoverPreviewError(false);
  }, [bookData]);
  
  // Validate form
  useEffect(() => {
    if (!editedBook) return;
    
    // Validate required fields
    const errors = {};
    if (!editedBook.title || editedBook.title.trim() === '') {
      errors.title = translate('Title is required');
    }
    
    if (!editedBook.author || editedBook.author.trim() === '') {
      errors.author = translate('Author is required');
    }
    
    if (editedBook.category === 'new_category' && (!newCategory || newCategory.trim() === '')) {
      errors.category = translate('Please enter a name for the new category');
    }
    
    if (editedBook.total_copies < 1) {
      errors.total_copies = translate('Total copies must be at least 1');
    }
    
    if (editedBook.available_copies < 0 || editedBook.available_copies > editedBook.total_copies) {
      errors.available_copies = translate('Available copies must be between 0 and total copies');
    }
    
    setFormErrors(errors);
    setIsValid(Object.keys(errors).length === 0);
  }, [editedBook, newCategory, translate]);
  
  // Clean up camera resources
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);
  
  // Handle input changes
  const handleChange = (field, value) => {
    setEditedBook(prevData => ({
      ...prevData,
      [field]: value
    }));
  };
  
  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setCoverImageFile(e.target.files[0]);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (event) => {
        handleChange('cover_image', event.target.result);
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };
  
  // Trigger file input click
  const handleSelectFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  // Handler for URL input
  const handleCoverUrlChange = (e) => {
    handleChange('cover_image', e.target.value);
    setCoverPreviewError(false);
  };
  
  // Handle image preview error
  const handleImageError = () => {
    setCoverPreviewError(true);
  };
  
  // Initialize camera for taking photo
  const startCamera = async () => {
    try {
      setTakingPhoto(true);
      
      // Check if we already have a stream and stop it first
      if (streamRef.current) {
        stopCamera();
      }
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      setTakingPhoto(false);
      alert(translate('Error accessing camera. Please check permissions.'));
    }
  };
  
  // Stop camera and release resources
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    
    setTakingPhoto(false);
  };
  
  // Take a photo using the camera
  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // Set canvas size to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // Draw video frame to canvas
      const context = canvas.getContext('2d');
      context.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // Get data URL
      const dataURL = canvas.toDataURL('image/jpeg');
      handleChange('cover_image', dataURL);
      
      // Convert to file
      fetch(dataURL)
        .then(res => res.blob())
        .then(blob => {
          const file = new File([blob], "cover_photo.jpg", { type: "image/jpeg" });
          setCoverImageFile(file);
        });
      
      // Stop camera
      stopCamera();
    }
  };
  
  // Handler for form submission
  const handleSubmit = (e) => {
    if (e) e.preventDefault();
    
    if (!editedBook || !isValid) {
      console.log('Form validation failed', formErrors);
      return;
    }
    
    // Create the final book data
    let finalBookData = { ...editedBook };
    
    // Handle new category case
    if (editedBook.category === 'new_category' && newCategory.trim()) {
      finalBookData.category = newCategory.trim();
    }
    
    // If there's a cover file, pass it separately
    onSave(finalBookData, coverImageFile);
  };
  
  // Render cover image section with multiple upload options
  const renderCoverSection = () => {
    const hasImage = editedBook && editedBook.cover_image;
    
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          {translate('Book Cover')}
        </Typography>
        
        {/* Image Upload Method Selection */}
        <Box sx={{ mb: 2 }}>
          <FormControl size="small" fullWidth>
            <InputLabel id="image-upload-method-label">{translate('Cover Image Source')}</InputLabel>
            <Select
              labelId="image-upload-method-label"
              id="image-upload-method"
              value={imageUploadMode}
              label={translate('Cover Image Source')}
              onChange={(e) => setImageUploadMode(e.target.value)}
            >
              <MenuItem value="url">{translate('URL Link')}</MenuItem>
              <MenuItem value="file">{translate('Upload File')}</MenuItem>
              <MenuItem value="camera">{translate('Take Photo')}</MenuItem>
            </Select>
          </FormControl>
        </Box>
        
        {/* URL Input with Live Preview */}
        {imageUploadMode === 'url' && (
          <Box sx={{ width: '100%' }}>
            <TextField
              fullWidth
              label={translate('Image URL')}
              value={editedBook?.cover_image || ''}
              onChange={handleCoverUrlChange}
              placeholder="https://example.com/book-cover.jpg"
              variant="outlined"
              size="small"
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: editedBook?.cover_image && (
                  <InputAdornment position="end">
                    <IconButton 
                      onClick={() => handleChange('cover_image', '')}
                      edge="end"
                      size="small"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            {editedBook?.cover_image && !coverPreviewError && (
              <Box sx={{ 
                width: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                mt: 2 
              }}>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1 }}>
                  {translate('Live Preview')}
                </Typography>
                <Paper
                  elevation={2}
                  sx={{
                    width: '100%',
                    maxWidth: '250px',
                    height: '320px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden',
                    position: 'relative',
                    mb: 1
                  }}
                >
                  <img
                    src={editedBook.cover_image}
                    alt={translate('Book cover preview')}
                    onError={handleImageError}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain'
                    }}
                  />
                </Paper>
                
                {/* Switch button to toggle between URL and file upload */}
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => setImageUploadMode('file')}
                    startIcon={<AddIcon />}
                  >
                    {translate('Switch to File Upload')}
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        )
  }
        
        {/* File Upload */}
        {imageUploadMode === 'file' && (
          <Box>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              style={{ display: 'none' }}
              ref={fileInputRef}
            />
            <Button
              variant="outlined"
              onClick={handleSelectFile}
              startIcon={<AddIcon />}
              sx={{ mb: 2 }}
            >
              {translate('Select Image File')}
            </Button>
            
            {/* Show URL input next to total copies */}
            {!hasImage && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setImageUploadMode('url')}
                  startIcon={<EditIcon />}
                >
                  {translate('Switch to URL Input')}
                </Button>
              </Box>
            )}
          </Box>
        )}
        
        {/* Camera Input */}
        {imageUploadMode === 'camera' && (
          <Box sx={{ width: '100%' }}>
            {takingPhoto ? (
              <Box sx={{ position: 'relative', width: '100%', mb: 2 }}>
                <video 
                  ref={videoRef} 
                  style={{ width: '100%', maxHeight: '300px', borderRadius: '4px' }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={capturePhoto}
                  sx={{ 
                    position: 'absolute', 
                    bottom: '10px', 
                    left: '50%', 
                    transform: 'translateX(-50%)',
                    borderRadius: '20px',
                    px: 3
                  }}
                >
                  {translate('Capture')}
                </Button>
                <IconButton
                  sx={{ 
                    position: 'absolute', 
                    top: '5px', 
                    right: '5px',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'rgba(0,0,0,0.7)',
                    }
                  }}
                  onClick={stopCamera}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            ) : (
              <Button
                variant="outlined"
                onClick={startCamera}
                startIcon={<CameraIcon />}
                sx={{ mb: 2 }}
              >
                {translate('Take Photo')}
              </Button>
            )}
            <canvas ref={canvasRef} style={{ display: 'none' }} />
          </Box>
        )}
        
        {/* Preview Image */}
        {hasImage && !coverPreviewError && imageUploadMode !== 'url' && (
          <Box sx={{ 
            width: '100%', 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center' 
          }}>
            <Paper
              elevation={2}
              sx={{
                width: '100%',
                maxWidth: '250px',
                height: '320px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                position: 'relative',
                mb: 1
              }}
            >
              <img
                src={editedBook.cover_image}
                alt={translate('Book cover preview')}
                onError={handleImageError}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
              <IconButton
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.7)',
                  },
                  fontSize: '0.75rem',
                  p: '4px'
                }}
                onClick={() => handleChange('cover_image', '')}
                size="small"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Paper>
            
            {/* Switch button to toggle between URL and file upload */}
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setImageUploadMode('url')}
                startIcon={<EditIcon />}
              >
                {translate('Switch to URL Input')}
              </Button>
            </Box>
          </Box>
        )}
        
        {/* Error Message for Invalid URL */}
        {coverPreviewError && (
          <Alert severity="warning" sx={{ mt: 1 }}>
            {translate('Could not load image from URL. Please check the link.')}
          </Alert>
        )}
        
        {/* No Image Selected Message */}
        {!hasImage && (
          <Alert severity="info" sx={{ mt: 1 }}>
            {translate('No cover image set. Book will display with a placeholder cover.')}
          </Alert>
        )}
      </Box>
    );
  };
  
  if (!editedBook) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mb: 4 }}>
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <CircularProgress />
        </Box>
      )}
      
      <Grid container spacing={2}>
        {/* Book cover and image upload section */}
        <Grid item xs={12} md={4}>
          {renderCoverSection()}
          
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {translate('Library Management')}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="total_copies"
                  label={translate('Total Copies')}
                  type="number"
                  value={editedBook.total_copies || 1}
                  onChange={(e) => handleChange('total_copies', parseInt(e.target.value) || 1)}
                  InputProps={{ inputProps: { min: 1 } }}
                  error={!!formErrors.total_copies}
                  helperText={formErrors.total_copies}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="available_copies"
                  label={translate('Available Copies')}
                  type="number"
                  value={editedBook.available_copies || 0}
                  onChange={(e) => handleChange('available_copies', parseInt(e.target.value) || 0)}
                  InputProps={{ 
                    inputProps: { 
                      min: 0, 
                      max: editedBook.total_copies 
                    } 
                  }}
                  error={!!formErrors.available_copies}
                  helperText={formErrors.available_copies}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
        
        {/* Book details fields */}
        <Grid item xs={12} md={8}>
          <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {translate('Book Details')}
            </Typography>
            
            <Grid container spacing={2}>
              {/* Title */}
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  required
                  name="title"
                  label={translate('Title')}
                  value={editedBook.title || ''}
                  onChange={(e) => handleChange('title', e.target.value)}
                  error={!!formErrors.title}
                  helperText={formErrors.title}
                />
              </Grid>
              
              {/* Author */}
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  required
                  name="author"
                  label={translate('Author')}
                  value={editedBook.author || ''}
                  onChange={(e) => handleChange('author', e.target.value)}
                  error={!!formErrors.author}
                  helperText={formErrors.author}
                />
              </Grid>
              
              {/* ISBN-13 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="isbn_13"
                  label={translate('ISBN-13')}
                  value={editedBook.isbn_13 || editedBook.isbn || ''}
                  onChange={(e) => {
                    handleChange('isbn_13', e.target.value);
                    handleChange('isbn', e.target.value);
                  }}
                  placeholder="9780123456789"
                />
              </Grid>
              
              {/* ISBN-10 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="isbn_10"
                  label={translate('ISBN-10')}
                  value={editedBook.isbn_10 || ''}
                  onChange={(e) => handleChange('isbn_10', e.target.value)}
                  placeholder="0123456789"
                />
              </Grid>
              
              {/* Category */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>{translate('Category')}</InputLabel>
                  <Select
                    value={editedBook.category}
                    onChange={(e) => handleChange('category', e.target.value)}
                    name="category"
                    label={translate('Category')}
                  >
                    <MenuItem value="">{translate('None')}</MenuItem>
                    {categories.map(category => (
                      <MenuItem key={category.id} value={category.name}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              {/* Publisher */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="publisher"
                  label={translate('Publisher')}
                  value={editedBook.publisher || ''}
                  onChange={(e) => handleChange('publisher', e.target.value)}
                />
              </Grid>
              
              {/* Published Year */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="published_year"
                  label={translate('Published Year')}
                  value={editedBook.published_year || ''}
                  onChange={(e) => handleChange('published_year', e.target.value)}
                  placeholder="2023"
                />
              </Grid>
              
              {/* Description */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="description"
                  label={translate('Description')}
                  value={editedBook.description || ''}
                  onChange={(e) => handleChange('description', e.target.value)}
                  multiline
                  minRows={3}
                  maxRows={6}
                  placeholder={translate('Brief description of the book...')}
                />
              </Grid>
            </Grid>
          </Paper>
          
          {/* Submit button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button 
              variant="outlined" 
              onClick={onCancel}
              disabled={isSaving}
            >
              {translate('Cancel')}
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              disabled={!isValid || isSaving}
              startIcon={isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              {isSaving ? translate('Saving...') : translate('Save Book')}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BookForm;