import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Chip,
  Tooltip,
  Grid,
  Card,
  CardContent,
  CardActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Storage as StorageIcon,
  TableChart as TableIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';
import api from '../../utils/api';

const DatabaseManagement = () => {
  const { translate } = useLanguage();
  const [currentTab, setCurrentTab] = useState(0);
  const [tables, setTables] = useState([]);
  const [selectedTable, setSelectedTable] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [tableSchema, setTableSchema] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // SQL Query tab states
  const [sqlQuery, setSqlQuery] = useState('');
  const [queryResult, setQueryResult] = useState(null);
  const [queryLoading, setQueryLoading] = useState(false);

  // Dialog states
  const [addColumnDialog, setAddColumnDialog] = useState(false);
  const [editRowDialog, setEditRowDialog] = useState(false);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState(false);
  const [addRowDialog, setAddRowDialog] = useState(false);

  // Form states
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'TEXT',
    nullable: true,
    defaultValue: ''
  });
  const [editingRow, setEditingRow] = useState(null);
  const [newRow, setNewRow] = useState({});
  const [deleteTarget, setDeleteTarget] = useState(null);

  // Column types available in SQLite
  const columnTypes = ['TEXT', 'INTEGER', 'REAL', 'BLOB', 'NUMERIC', 'BOOLEAN', 'DATE', 'DATETIME'];

  // Protected tables and columns that cannot be modified
  const protectedTables = ['sqlite_sequence', 'sqlite_master'];
  const protectedColumns = {
    // Removed all protections - admin has full control
  };

  useEffect(() => {
    fetchTables();
  }, []);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData();
      fetchTableSchema();
    }
  }, [selectedTable]);

  const fetchTables = async () => {
    try {
      setLoading(true);
      const response = await api.direct.get('/api/admin/database/tables');
      setTables(response.data.tables || []);
    } catch (err) {
      setError('Failed to fetch tables: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const fetchTableData = async () => {
    if (!selectedTable) return;

    try {
      setLoading(true);
      const response = await api.direct.get(`/api/admin/database/tables/${selectedTable}/data`);
      setTableData(response.data.data || []);
    } catch (err) {
      setError('Failed to fetch table data: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const fetchTableSchema = async () => {
    if (!selectedTable) return;

    try {
      const response = await api.direct.get(`/api/admin/database/tables/${selectedTable}/schema`);
      setTableSchema(response.data.schema || []);
    } catch (err) {
      setError('Failed to fetch table schema: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    if (newValue === 0) {
      setSelectedTable(null);
    }
  };

  const executeQuery = async () => {
    if (!sqlQuery.trim()) {
      setError('Please enter a SQL query');
      return;
    }

    try {
      setQueryLoading(true);
      const response = await api.direct.post('/api/admin/database/query', { sql: sqlQuery });
      setQueryResult(response.data);
      setSuccess('Query executed successfully');
    } catch (err) {
      setError('Query failed: ' + (err.response?.data?.message || err.message));
      setQueryResult(null);
    } finally {
      setQueryLoading(false);
    }
  };

  const handleTableSelect = (tableName) => {
    setSelectedTable(tableName);
    setCurrentTab(1);
  };

  const handleAddColumn = async () => {
    try {
      await api.direct.post(`/api/admin/database/tables/${selectedTable}/columns`, newColumn);
      setSuccess('Column added successfully');
      setAddColumnDialog(false);
      setNewColumn({ name: '', type: 'TEXT', nullable: true, defaultValue: '' });
      fetchTableSchema();
      fetchTableData();
    } catch (err) {
      setError('Failed to add column: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleDeleteColumn = async (columnName) => {
    if (protectedColumns[selectedTable]?.includes(columnName)) {
      setError('Cannot delete protected column');
      return;
    }

    try {
      await api.direct.delete(`/api/admin/database/tables/${selectedTable}/columns/${columnName}`);
      setSuccess('Column deleted successfully');
      fetchTableSchema();
      fetchTableData();
    } catch (err) {
      setError('Failed to delete column: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleAddRow = async () => {
    try {
      await api.direct.post(`/api/admin/database/tables/${selectedTable}/rows`, newRow);
      setSuccess('Row added successfully');
      setAddRowDialog(false);
      setNewRow({});
      fetchTableData();
    } catch (err) {
      setError('Failed to add row: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleEditRow = async () => {
    try {
      const primaryKey = tableSchema.find(col => col.pk)?.name || 'id';
      const rowId = editingRow[primaryKey];

      await api.direct.put(`/api/admin/database/tables/${selectedTable}/rows/${rowId}`, editingRow);
      setSuccess('Row updated successfully');
      setEditRowDialog(false);
      setEditingRow(null);
      fetchTableData();
    } catch (err) {
      setError('Failed to update row: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleDeleteRow = async () => {
    try {
      const primaryKey = tableSchema.find(col => col.pk)?.name || 'id';
      const rowId = deleteTarget[primaryKey];

      await api.direct.delete(`/api/admin/database/tables/${selectedTable}/rows/${rowId}`);
      setSuccess('Row deleted successfully');
      setDeleteConfirmDialog(false);
      setDeleteTarget(null);
      fetchTableData();
    } catch (err) {
      setError('Failed to delete row: ' + (err.response?.data?.message || err.message));
    }
  };

  const isProtectedColumn = (columnName) => {
    return protectedColumns[selectedTable]?.includes(columnName);
  };

  const isProtectedTable = (tableName) => {
    return protectedTables.includes(tableName);
  };

  const formatCellValue = (value, columnType) => {
    if (value === null || value === undefined) return 'NULL';
    if (columnType === 'BOOLEAN') return value ? 'TRUE' : 'FALSE';
    if (typeof value === 'string' && value.length > 50) {
      return value.substring(0, 50) + '...';
    }
    return String(value);
  };

  const renderTablesOverview = () => (
    <Grid container spacing={3}>
      {tables.map((table) => (
        <Grid item xs={12} sm={6} md={4} key={table.name}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TableIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">{table.name}</Typography>
                {isProtectedTable(table.name) && (
                  <Chip
                    label="Protected"
                    color="warning"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
              <Typography variant="body2" color="text.secondary">
                Rows: {table.row_count || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Columns: {table.column_count || 0}
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                onClick={() => handleTableSelect(table.name)}
                disabled={isProtectedTable(table.name)}
              >
                Manage
              </Button>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderTableData = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Table: {selectedTable}
        </Typography>
        <Box>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchTableData}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            startIcon={<AddIcon />}
            variant="contained"
            onClick={() => {
              setNewRow({});
              setAddRowDialog(true);
            }}
          >
            Add Row
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Typography>Loading...</Typography>
      ) : (
        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {tableSchema.map((column) => (
                  <TableCell key={column.name}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {column.name}
                      {column.pk && <Chip label="PK" size="small" color="primary" sx={{ ml: 1 }} />}
                      {isProtectedColumn(column.name) && (
                        <Chip label="Protected" size="small" color="warning" sx={{ ml: 1 }} />
                      )}
                    </Box>
                  </TableCell>
                ))}
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tableData.map((row, index) => (
                <TableRow key={index}>
                  {tableSchema.map((column) => (
                    <TableCell key={column.name}>
                      <Tooltip title={String(row[column.name] || 'NULL')}>
                        <span>{formatCellValue(row[column.name], column.type)}</span>
                      </Tooltip>
                    </TableCell>
                  ))}
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setEditingRow({ ...row });
                        setEditRowDialog(true);
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => {
                        setDeleteTarget(row);
                        setDeleteConfirmDialog(true);
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderSchemaManagement = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Schema: {selectedTable}
        </Typography>
        <Button
          startIcon={<AddIcon />}
          variant="contained"
          onClick={() => setAddColumnDialog(true)}
        >
          Add Column
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Column Name</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Nullable</TableCell>
              <TableCell>Default</TableCell>
              <TableCell>Primary Key</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tableSchema.map((column) => (
              <TableRow key={column.name}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {column.name}
                    {isProtectedColumn(column.name) && (
                      <Chip label="Protected" size="small" color="warning" sx={{ ml: 1 }} />
                    )}
                  </Box>
                </TableCell>
                <TableCell>{column.type}</TableCell>
                <TableCell>{column.notnull ? 'No' : 'Yes'}</TableCell>
                <TableCell>{column.dflt_value || 'None'}</TableCell>
                <TableCell>{column.pk ? 'Yes' : 'No'}</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    color="error"
                    disabled={isProtectedColumn(column.name)}
                    onClick={() => handleDeleteColumn(column.name)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderSqlQuery = () => (
    <Box>
      <Typography variant="h5" gutterBottom>
        {translate('SQL Query Console')}
      </Typography>

      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>{translate('Advanced Feature:')}</strong> {translate('Execute custom SQL queries. Only SELECT, INSERT, UPDATE, DELETE operations are allowed.')}
        </Typography>
      </Alert>

      <TextField
        fullWidth
        multiline
        rows={8}
        variant="outlined"
        label={translate('SQL Query')}
        value={sqlQuery}
        onChange={(e) => setSqlQuery(e.target.value)}
        placeholder="SELECT * FROM users WHERE role = 'admin';"
        sx={{ mb: 2 }}
      />

      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          onClick={executeQuery}
          disabled={queryLoading || !sqlQuery.trim()}
          sx={{ mr: 2 }}
        >
          {queryLoading ? <CircularProgress size={20} /> : translate('Execute Query')}
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            setSqlQuery('');
            setQueryResult(null);
          }}
        >
          {translate('Clear')}
        </Button>
      </Box>

      {queryResult && (
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            {translate('Query Result')}
          </Typography>

          {queryResult.type === 'select' ? (
            queryResult.result.length > 0 ? (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {Object.keys(queryResult.result[0]).map((column) => (
                        <TableCell key={column}>{column}</TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {queryResult.result.map((row, index) => (
                      <TableRow key={index}>
                        {Object.values(row).map((value, cellIndex) => (
                          <TableCell key={cellIndex}>
                            {value === null ? 'NULL' : String(value)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography>{translate('No results returned.')}</Typography>
            )
          ) : (
            <Box>
              <Typography>
                <strong>{translate('Changes:')}</strong> {queryResult.result.changes}
              </Typography>
              {queryResult.result.lastID && (
                <Typography>
                  <strong>{translate('Last Insert ID:')}</strong> {queryResult.result.lastID}
                </Typography>
              )}
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <StorageIcon sx={{ mr: 2 }} />
        Database Management
      </Typography>

      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Warning:</strong> This is a powerful tool that directly modifies your database.
          Always backup your data before making changes. Protected tables and columns cannot be modified.
        </Typography>
      </Alert>

      <Paper sx={{ width: '100%' }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab label={translate('Tables Overview')} />
          <Tab label={translate('Table Data')} disabled={!selectedTable} />
          <Tab label={translate('Schema Management')} disabled={!selectedTable} />
          <Tab label={translate('SQL Query')} />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {currentTab === 0 && renderTablesOverview()}
          {currentTab === 1 && renderTableData()}
          {currentTab === 2 && renderSchemaManagement()}
          {currentTab === 3 && renderSqlQuery()}
        </Box>
      </Paper>

      {/* Snackbar for success/error messages */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
      >
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
      >
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      </Snackbar>

      {/* Add Column Dialog */}
      <Dialog open={addColumnDialog} onClose={() => setAddColumnDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Column</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Column Name"
            fullWidth
            variant="outlined"
            value={newColumn.name}
            onChange={(e) => setNewColumn({ ...newColumn, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Data Type</InputLabel>
            <Select
              value={newColumn.type}
              label="Data Type"
              onChange={(e) => setNewColumn({ ...newColumn, type: e.target.value })}
            >
              {columnTypes.map((type) => (
                <MenuItem key={type} value={type}>{type}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Nullable</InputLabel>
            <Select
              value={newColumn.nullable}
              label="Nullable"
              onChange={(e) => setNewColumn({ ...newColumn, nullable: e.target.value })}
            >
              <MenuItem value={true}>Yes</MenuItem>
              <MenuItem value={false}>No</MenuItem>
            </Select>
          </FormControl>
          <TextField
            margin="dense"
            label="Default Value (optional)"
            fullWidth
            variant="outlined"
            value={newColumn.defaultValue}
            onChange={(e) => setNewColumn({ ...newColumn, defaultValue: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddColumnDialog(false)}>Cancel</Button>
          <Button onClick={handleAddColumn} variant="contained">Add Column</Button>
        </DialogActions>
      </Dialog>

      {/* Add Row Dialog */}
      <Dialog open={addRowDialog} onClose={() => setAddRowDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Row</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {tableSchema.filter(col => !col.pk).map((column) => (
              <Grid item xs={12} sm={6} key={column.name}>
                <TextField
                  margin="dense"
                  label={column.name}
                  fullWidth
                  variant="outlined"
                  value={newRow[column.name] || ''}
                  onChange={(e) => setNewRow({ ...newRow, [column.name]: e.target.value })}
                  helperText={`Type: ${column.type}`}
                />
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddRowDialog(false)}>Cancel</Button>
          <Button onClick={handleAddRow} variant="contained">Add Row</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Row Dialog */}
      <Dialog open={editRowDialog} onClose={() => setEditRowDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Row</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {tableSchema.map((column) => (
              <Grid item xs={12} sm={6} key={column.name}>
                <TextField
                  margin="dense"
                  label={column.name}
                  fullWidth
                  variant="outlined"
                  value={editingRow?.[column.name] || ''}
                  onChange={(e) => setEditingRow({ ...editingRow, [column.name]: e.target.value })}
                  disabled={column.pk || isProtectedColumn(column.name)}
                  helperText={`Type: ${column.type}${column.pk ? ' (Primary Key)' : ''}${isProtectedColumn(column.name) ? ' (Protected)' : ''}`}
                />
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditRowDialog(false)}>Cancel</Button>
          <Button onClick={handleEditRow} variant="contained">Update Row</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmDialog} onClose={() => setDeleteConfirmDialog(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this row? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmDialog(false)}>Cancel</Button>
          <Button onClick={handleDeleteRow} color="error" variant="contained">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseManagement;
