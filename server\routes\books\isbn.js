const express = require('express');
const router = express.Router();
const axios = require('axios');
const { authenticateToken } = require('../../middleware/auth');
const { isAdmin } = require('../../middleware/admin');

// Load Google Books API key from environment variables
const GOOGLE_BOOKS_API_KEY = process.env.GOOGLE_BOOKS_API_KEY ? process.env.GOOGLE_BOOKS_API_KEY.trim() : null;

// Log the API key (masked for security) to verify it's loaded
console.log('Google Books API Key loaded:', GOOGLE_BOOKS_API_KEY ? `${GOOGLE_BOOKS_API_KEY.substring(0, 4)}...${GOOGLE_BOOKS_API_KEY.substring(GOOGLE_BOOKS_API_KEY.length - 4)}` : 'Not found');
console.log('Environment variables loaded:', Object.keys(process.env).includes('GOOGLE_BOOKS_API_KEY') ? 'GOOGLE_BOOKS_API_KEY exists' : 'GOOGLE_BOOKS_API_KEY missing');

// Enhanced cover image handling with retries and multiple dimensions
const fetchBookCover = async (isbn) => {
  const sources = [
    // Open Library covers in multiple sizes - try large first
    {
      url: `https://covers.openlibrary.org/b/isbn/${isbn}-L.jpg`,
      quality: 'high'
    },
    // Open Library medium size
    {
      url: `https://covers.openlibrary.org/b/isbn/${isbn}-M.jpg`,
      quality: 'medium'
    },
    // Google Books cover API with direct ISBN
    {
      url: `https://www.googleapis.com/books/v1/volumes?q=isbn:${isbn}&fields=items(volumeInfo(imageLinks))`,
      processor: (data) => {
        if (data.items && data.items[0]?.volumeInfo?.imageLinks) {
          // Get largest available image and remove zoom parameters
          return data.items[0].volumeInfo.imageLinks.extraLarge || 
                 data.items[0].volumeInfo.imageLinks.large ||
                 data.items[0].volumeInfo.imageLinks.medium ||
                 data.items[0].volumeInfo.imageLinks.thumbnail || 
                 data.items[0].volumeInfo.imageLinks.smallThumbnail;
        }
        return null;
      },
      quality: 'medium'
    },
    // Add Amazon book cover API (using book cover widget)
    {
      url: `https://images-na.ssl-images-amazon.com/images/P/${isbn}.01.LZZZZZZZ.jpg`,
      quality: 'medium'
    },
    // Add Google Books cover direct URL format
    {
      url: `https://books.google.com/books/content?id=ISBN:${isbn}&printsec=frontcover&img=1&zoom=1&source=gbs_api`,
      quality: 'medium'
    },
    // Add Bookshop.org cover (may work for newer books)
    {
      url: `https://images.bookshop.org/covers/${isbn}.jpg`,
      quality: 'medium'
    },
    // Add Goodreads cover API
    {
      url: `https://www.goodreads.com/book/isbn/${isbn}`,
      processor: async (data) => {
        // Extract cover image from Goodreads page
        const match = data.match(/<img id=\"coverImage\" src=\"(.*?)\"/);
        return match ? match[1] : null;
      },
      quality: 'medium'
    },
    // Add LibraryThing cover API
    {
      url: `https://www.librarything.com/work/isbn/${isbn}`,
      processor: async (data) => {
        // Extract cover image from LibraryThing page
        const match = data.match(/<img class=\"cover\" src=\"(.*?)\"/);
        return match ? match[1] : null;
      },
      quality: 'medium'
    },
    // Add WorldCat cover API
    {
      url: `https://www.worldcat.org/isbn/${isbn}`,
      processor: async (data) => {
        // Extract cover image from WorldCat page
        const match = data.match(/<img class=\"cover\" src=\"(.*?)\"/);
        return match ? match[1] : null;
      },
      quality: 'medium'
    },
    // Fallback to Open Library small size
    {
      url: `https://covers.openlibrary.org/b/isbn/${isbn}-S.jpg`,
      quality: 'low'
    }
  ];

  let bestImage = null;
  let bestQuality = 'none';
  
  const qualityRank = {
    'high': 3,
    'medium': 2,
    'low': 1,
    'none': 0
  };
  
  for (const source of sources) {
    try {
      // Skip if we already have a better quality image
      if (bestImage && qualityRank[bestQuality] > qualityRank[source.quality]) {
        continue;
      }
      
      if (source.processor) {
        // This is an API that returns JSON
        const response = await axios.get(source.url);
        const imageUrl = source.processor(response.data);
        if (imageUrl) {
          // Remove zoom parameters and ensure https
          const cleanUrl = imageUrl
            .replace(/&zoom=\d/, '')
            .replace(/^http:\/\//i, 'https://');
          
          // Verify image exists and is not a placeholder
          try {
            const imgResponse = await axios.head(cleanUrl);
            if (imgResponse.status === 200 && 
                imgResponse.headers['content-length'] && 
                parseInt(imgResponse.headers['content-length']) > 1000) {
              bestImage = cleanUrl;
              bestQuality = source.quality;
            }
          } catch (err) {
            // Skip this image if head request fails
            console.log(`Image verification failed for ${cleanUrl}: ${err.message}`);
          }
        }
      } else {
        // This is a direct image URL - try get first to check if image exists
        try {
          const response = await axios.head(source.url);
          if (response.status === 200 && 
              response.headers['content-length'] && 
              parseInt(response.headers['content-length']) > 1000) {
            bestImage = source.url;
            bestQuality = source.quality;
          }
        } catch (err) {
          // Try a normal GET request as fallback (some servers don't allow HEAD)
          try {
            const getResponse = await axios.get(source.url, { responseType: 'arraybuffer' });
            if (getResponse.status === 200 && 
                getResponse.data && 
                getResponse.data.length > 1000) {
              bestImage = source.url;
              bestQuality = source.quality;
            }
          } catch (getErr) {
            // Skip this image if both requests fail
            console.log(`Failed to fetch cover from ${source.url}: ${getErr.message}`);
          }
        }
      }
    } catch (err) {
      // Continue to next source on error
      console.log(`Failed to fetch cover from ${source.url}:`, err.message);
    }
  }
  
  console.log(`Found best cover image for ISBN ${isbn}: ${bestImage} (quality: ${bestQuality})`);
  return bestImage;
};

// Enhanced helper function to merge book data from multiple sources
const mergeBookData = async (googleData, openLibraryData, isbn) => {
  // Start with base book structure
  const mergedBook = {
    title: '',
    author: '',
    description: '',
    isbn: isbn,
    isbn_10: '',
    isbn_13: '',
    published_year: '',
    publisher: '',
    category: '',
    cover_image: '',
    page_count: null,
    subjects: [],
    exists_in_db: false,
    source: 'Combined APIs',
    preview_link: '',
    info_link: '',
    language: '',
    sources: {
      google: null,
      openLibrary: null
    }
  };

  // Process Google Books data if available
  if (googleData) {
    // Store the original Google data for reference
    mergedBook.sources.google = googleData;
    
    // Extract core metadata
    mergedBook.title = googleData.title || mergedBook.title;
    mergedBook.author = googleData.authors ? googleData.authors.join(', ') : mergedBook.author;
    mergedBook.description = googleData.description || mergedBook.description;
    
    // Extract and normalize publication data
    if (googleData.publishedDate) {
      // Handle various date formats (YYYY, YYYY-MM, YYYY-MM-DD)
      const match = googleData.publishedDate.match(/(\d{4})/);
      mergedBook.published_year = match ? match[1] : '';
    }
    
    mergedBook.publisher = googleData.publisher || mergedBook.publisher;
    mergedBook.category = googleData.categories ? googleData.categories[0] : mergedBook.category;
    mergedBook.language = googleData.language || mergedBook.language;
    
    // Extract cover image from imageLinks with improved URL handling
    if (googleData.imageLinks) {
      // Try to get the largest available image
      let imageUrl = googleData.imageLinks.extraLarge || 
                     googleData.imageLinks.large || 
                     googleData.imageLinks.medium || 
                     googleData.imageLinks.thumbnail || 
                     googleData.imageLinks.smallThumbnail || '';
      
      if (imageUrl) {
        // Convert http to https
        imageUrl = imageUrl.replace(/^http:\/\//i, 'https://');
        // Remove zoom parameters for higher quality
        imageUrl = imageUrl.replace(/&zoom=\d/, '');
        // Update zoom level to get higher resolution (if using new URL pattern)
        imageUrl = imageUrl.replace(/(&zoom=)\d/, '$15'); // Set zoom to highest level
        
        mergedBook.cover_image = imageUrl;
      }
    }
    
    mergedBook.page_count = googleData.pageCount || mergedBook.page_count;
    
    // Add links
    mergedBook.preview_link = googleData.previewLink || '';
    mergedBook.info_link = googleData.infoLink || '';
    
    // Extract ISBNs
    if (googleData.industryIdentifiers) {
      googleData.industryIdentifiers.forEach(identifier => {
        if (identifier.type === 'ISBN_13') {
          mergedBook.isbn_13 = identifier.identifier;
        } else if (identifier.type === 'ISBN_10') {
          mergedBook.isbn_10 = identifier.identifier;
        }
      });
    }
    
    // Extract subjects/categories with more detail
    if (googleData.categories) {
      mergedBook.subjects = [...mergedBook.subjects, ...googleData.categories];
    }
  }

  // Process Open Library data if available
  if (openLibraryData) {
    // Store the original Open Library data for reference
    mergedBook.sources.openLibrary = openLibraryData;
    
    // Fill in missing data or override with potentially better data
    if (!mergedBook.title) mergedBook.title = openLibraryData.title || '';
    
    // Handle authors with better formatting
    if ((!mergedBook.author || mergedBook.author === '') && openLibraryData.authors) {
      mergedBook.author = openLibraryData.authors.map(a => a.name).join(', ');
    }
    
    // Handle description from different Open Library fields
    if (!mergedBook.description || mergedBook.description === '') {
      if (openLibraryData.excerpts && openLibraryData.excerpts.length > 0) {
      mergedBook.description = openLibraryData.excerpts[0].text || '';
      } else if (openLibraryData.description) {
        if (typeof openLibraryData.description === 'object') {
          mergedBook.description = openLibraryData.description.value || '';
        } else {
          mergedBook.description = openLibraryData.description;
        }
      }
    }
    
    // Extract publication year more reliably
    if ((!mergedBook.published_year || mergedBook.published_year === '') && openLibraryData.publish_date) {
      // Extract the year using regex to handle different date formats
      const yearMatch = openLibraryData.publish_date.match(/\b(\d{4})\b/);
      if (yearMatch) {
        mergedBook.published_year = yearMatch[1];
      }
    }
    
    // Handle publisher
    if ((!mergedBook.publisher || mergedBook.publisher === '') && openLibraryData.publishers && openLibraryData.publishers.length > 0) {
      mergedBook.publisher = openLibraryData.publishers[0].name;
    }
    
    // Handle subjects/categories
    if ((!mergedBook.category || mergedBook.category === '') && openLibraryData.subjects && openLibraryData.subjects.length > 0) {
      mergedBook.category = openLibraryData.subjects[0].name;
    }
    
    // Add Open Library cover image as a potential source
    if (openLibraryData.cover) {
      const olCoverImage = openLibraryData.cover.large || 
                            openLibraryData.cover.medium || 
                            openLibraryData.cover.small;
      
      if (olCoverImage) {
        // Only replace if we don't have an image or this one is likely better
        if (!mergedBook.cover_image || (olCoverImage.includes('large') && !mergedBook.cover_image.includes('extraLarge'))) {
        // Replace HTTP with HTTPS
        const coverUrl = olCoverImage.replace(/^http:\/\//i, 'https://');
        mergedBook.cover_image = coverUrl;
        }
      }
    }
    
    // Handle page count
    if ((!mergedBook.page_count || mergedBook.page_count === 0) && openLibraryData.number_of_pages) {
      mergedBook.page_count = openLibraryData.number_of_pages;
    }
    
    // Merge subjects from Open Library
    if (openLibraryData.subjects) {
      const subjects = openLibraryData.subjects.map(s => s.name || s);
      mergedBook.subjects = [...new Set([...mergedBook.subjects, ...subjects])];
    }
    
    // Fill in missing ISBNs
    if ((!mergedBook.isbn_10 || mergedBook.isbn_10 === '') && openLibraryData.identifiers && openLibraryData.identifiers.isbn_10) {
          mergedBook.isbn_10 = openLibraryData.identifiers.isbn_10[0];
        }
    
    if ((!mergedBook.isbn_13 || mergedBook.isbn_13 === '') && openLibraryData.identifiers && openLibraryData.identifiers.isbn_13) {
          mergedBook.isbn_13 = openLibraryData.identifiers.isbn_13[0];
        }
      }

  // If no cover image was found, try advanced cover image search
  if (!mergedBook.cover_image || mergedBook.cover_image === '') {
    const coverImage = await fetchBookCover(isbn);
    if (coverImage) {
      mergedBook.cover_image = coverImage;
    }
  }
  
  // Clean up data
  // Ensure subjects are unique
  mergedBook.subjects = [...new Set(mergedBook.subjects.filter(Boolean))];
  
  // Find the most appropriate ISBN to use
  if (!mergedBook.isbn_13 && !mergedBook.isbn_10) {
    if (isbn.length === 13) {
      mergedBook.isbn_13 = isbn;
    } else if (isbn.length === 10) {
      mergedBook.isbn_10 = isbn;
    }
  }
  
  // Determine the data source label
  if (mergedBook.sources.google && mergedBook.sources.openLibrary) {
    mergedBook.source = 'Google Books + Open Library';
  } else if (mergedBook.sources.google) {
    mergedBook.source = 'Google Books';
  } else if (mergedBook.sources.openLibrary) {
    mergedBook.source = 'Open Library';
  }
  
  return mergedBook;
};

// New function to try additional book APIs when primary ones fail
const searchAdditionalApis = async (isbn) => {
  let bookData = null;
  
  // Try New York Times Books API
  try {
    // This is an example - would need an API key in practice
    console.log('Trying additional book API: NYT Books API');
    // const nytResponse = await axios.get(`https://api.nytimes.com/svc/books/v3/reviews.json?isbn=${isbn}&api-key=${NYT_API_KEY}`);
    // ...processing would go here
  } catch (error) {
    console.log('NYT Books API failed:', error.message);
  }
  
  // Try WorldCat Search API
  try {
    console.log('Trying additional book API: WorldCat Search');
    // WorldCat requires authentication - this is a simplified example
    // const worldcatResponse = await axios.get(`http://www.worldcat.org/webservices/catalog/content/isbn/${isbn}?wskey=${WORLDCAT_API_KEY}`);
    // ...processing would go here
  } catch (error) {
    console.log('WorldCat API failed:', error.message);
  }
  
  // Add more API attempts here as needed
  
  return bookData;
};

// Special route handlers need to be defined BEFORE the generic /:isbn handler

// Also add a dedicated look-up route
router.get('/lookup/book', async (req, res) => {
  try {
    const isbn = req.query.isbn;
    
    if (!isbn) {
      return res.status(400).json({ message: 'ISBN query parameter is required' });
    }
    
    console.log(`Looking up book with ISBN query param: ${isbn}`);
    
    // Forward to the ISBN parameter route
    return router.handle(
      { ...req, path: `/${isbn}`, url: `/${isbn}`, params: { isbn } }, 
      res
    );
  } catch (err) {
    console.error('Error in lookup route:', err);
    res.status(500).json({ message: 'Server error in lookup route', error: err.message, success: false });
  }
});

// Route for external API lookups (needs to be before the generic /:isbn route)
router.get('/external/:isbn', async (req, res) => {
  try {
    const isbn = req.params.isbn;
    
    if (!isbn) {
      return res.status(400).json({ message: 'ISBN is required' });
    }
    
    console.log(`Looking up book with ISBN: ${isbn} from external APIs`);
    
    // Variables to hold API data
    let googleData = null;
    let openLibraryData = null;
    
    // Try Google Books API
    try {
      if (!GOOGLE_BOOKS_API_KEY) {
        console.error('Google Books API key not found in environment variables');
        console.log('Attempting to use Google Books API without key...');
      }

      console.log('Using Google Books API with key:', GOOGLE_BOOKS_API_KEY ? `${GOOGLE_BOOKS_API_KEY.substring(0, 4)}...` : 'None');
      
      // Set up URL with or without API key
      let googleUrl = `https://www.googleapis.com/books/v1/volumes?q=isbn:${isbn}`;
      if (GOOGLE_BOOKS_API_KEY) {
        googleUrl += `&key=${GOOGLE_BOOKS_API_KEY}`;
      }
      
      // Make the request
      const googleResponse = await axios.get(googleUrl);
      
      if (googleResponse.data.items && googleResponse.data.items.length > 0) {
        googleData = googleResponse.data.items[0].volumeInfo;
        console.log(`Book found in Google Books API: ${googleData.title}`);
      } else {
        console.log('No results found in Google Books API');
      }
    } catch (error) {
      console.error('Error with Google Books API:', error.message);
    }
    
    // Try Open Library API
    try {
      console.log(`Fetching data from Open Library API for ISBN: ${isbn}`);
      const openLibraryResponse = await axios.get(`https://openlibrary.org/api/books?bibkeys=ISBN:${isbn}&format=json&jscmd=data`);
      
      if (openLibraryResponse.data[`ISBN:${isbn}`]) {
        openLibraryData = openLibraryResponse.data[`ISBN:${isbn}`];
        console.log(`Book found in Open Library API: ${openLibraryData.title}`);
      } else {
        console.log('No results found in Open Library API');
      }
    } catch (error) {
      console.error('Error with Open Library API:', error.message);
    }
    
    // If we have data from either API, merge them
    if (googleData || openLibraryData) {
      // Use the comprehensive mergeBookData function to combine data from both APIs
      console.log('Merging book data from multiple sources');
      const mergedBook = await mergeBookData(googleData, openLibraryData, isbn);
      
      console.log(`Final merged book data: ${JSON.stringify({
        title: mergedBook.title,
        author: mergedBook.author,
        isbn: mergedBook.isbn,
        hasDescription: !!mergedBook.description,
        descriptionLength: mergedBook.description ? mergedBook.description.length : 0,
        hasCover: !!mergedBook.cover_image,
        source: mergedBook.source
      })}`);
      
      return res.json({ book: mergedBook, success: true });
    }
    
    // Try additional APIs as a last resort
    const additionalData = await searchAdditionalApis(isbn);
    if (additionalData) {
      console.log('Found book data in additional APIs');
      return res.json({ book: additionalData, success: true });
    }
    
    // Book not found in any external API
    console.log(`Book with ISBN ${isbn} not found in any external API`);
    return res.status(404).json({ book: null, success: false, message: 'Book not found in any external API' });
  } catch (err) {
    console.error('Error looking up ISBN from external APIs:', err);
    res.status(500).json({ message: 'Server error looking up ISBN from external APIs', error: err.message, success: false });
  }
});

// Generic ISBN lookup (database first, then external)
router.get('/:isbn', async (req, res) => {
  try {
    const isbn = req.params.isbn;
    
    if (!isbn) {
      return res.status(400).json({ message: 'ISBN is required' });
    }
    
    console.log(`Looking up book with ISBN: ${isbn}`);
    
    // Check if book exists in database (you can keep your existing database lookup logic here)
    const book = await new Promise((resolve, reject) => {
      req.app.locals.db.get(
        'SELECT * FROM books WHERE isbn = ?',
        [isbn],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
    
    if (book) {
      console.log(`Book found in database: ${book.title}`);
      return res.json({ book, success: true });
    }
    
    // If not in database, forward to external API lookup
    console.log(`Book with ISBN ${isbn} not found in database, searching external APIs`);
    return router.handle(
      { ...req, path: `/external/${isbn}`, url: `/external/${isbn}` }, 
      res
    );
  } catch (err) {
    console.error('Error looking up ISBN:', err);
    res.status(500).json({ message: 'Server error looking up ISBN', error: err.message, success: false });
  }
});

module.exports = router;