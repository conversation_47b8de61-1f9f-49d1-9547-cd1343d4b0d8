import React, { useState } from 'react';
import { Box } from '@mui/material';
import { useLanguage } from '../../context/LanguageContext';

/**
 * BookCover component that displays either the provided image URL or a placeholder SVG
 * Handles both external URLs and local server image paths
 *
 * @param {string} imageUrl - URL of the book cover image
 * @param {string} title - Book title for alt text
 * @param {object} sx - Additional styling props
 */
const BookCover = ({ imageUrl, title, sx = {} }) => {
  const [imageError, setImageError] = useState(false);
  const { translate } = useLanguage();

  // Check if we have a valid image URL
  const hasValidImage = imageUrl && imageUrl.trim() !== '' && !imageError;

  // Default styling
  const defaultSx = {
    width: '100%',
    height: 'auto',
    maxHeight: 100,
    objectFit: 'cover',
    borderRadius: 2,
    boxShadow: 3,
    bgcolor: 'grey.100',
    ...sx
  };

  // Process image URL - if it's a relative path like /uploads/book_covers/...,
  // prefix it with the API_BASE_URL
  const getImageSrc = (url) => {
    if (!url) return '';

    // If the URL is already absolute (starts with http or https), use it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If URL starts with /uploads/, it's a local image from the server
    if (url.startsWith('/uploads/')) {
      // Get the API base URL from environment or fallback to current domain
      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL ||
                           window.location.origin;
      return `${API_BASE_URL}${url}`;
    }

    // Otherwise return the URL as is
    return url;
  };

  // If we have a valid image URL, display the image
  if (hasValidImage) {
    return (
      <Box
        component="img"
        src={getImageSrc(imageUrl)}
        alt={`${title} cover`}
        sx={defaultSx}
        onError={(e) => {
          // If image fails to load, set error state to show placeholder
          console.error(`Failed to load image: ${imageUrl}`);
          e.target.onerror = null;
          setImageError(true);
        }}
      />
    );
  }

  // Otherwise, display a placeholder SVG
  return (
    <Box
      sx={{
        ...defaultSx,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2
      }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 240 360"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="240" height="360" fill="#f5f5f5" />
        <path
          d="M120 80 L180 120 L120 160 L60 120 Z"
          fill="#e0e0e0"
          stroke="#9e9e9e"
          strokeWidth="2"
        />
        <rect x="60" y="180" width="120" height="10" rx="5" fill="#e0e0e0" />
        <rect x="60" y="200" width="120" height="10" rx="5" fill="#e0e0e0" />
        <rect x="60" y="220" width="80" height="10" rx="5" fill="#e0e0e0" />
        <text
          x="120"
          y="280"
          fontFamily="Arial, sans-serif"
          fontSize="14"
          textAnchor="middle"
          dominantBaseline="middle"
          fill="#9e9e9e"
        >
          {title || translate('No Cover Available')}
        </text>
      </svg>
    </Box>
  );
};

export default BookCover;