const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to the database
const dbPath = path.resolve(__dirname, 'server/database/bookleasing.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('Connected to the SQLite database');
});

// Get the schema of the books table
db.all('PRAGMA table_info(books)', (err, rows) => {
  if (err) {
    console.error('Error getting table info:', err.message);
    db.close();
    process.exit(1);
  }
  
  console.log('Books table schema:');
  console.log(rows);
  
  // Check if published_year column exists
  const publishedYearColumn = rows.find(row => row.name === 'published_year');
  if (publishedYearColumn) {
    console.log('\npublished_year column exists in the books table');
  } else {
    console.log('\npublished_year column does NOT exist in the books table');
    console.log('\nAdding published_year column to books table...');
    
    // Add the missing column
    db.run('ALTER TABLE books ADD COLUMN published_year TEXT', (err) => {
      if (err) {
        console.error('Error adding published_year column:', err.message);
      } else {
        console.log('published_year column added successfully');
      }
      
      // Add publisher column if it doesn't exist
      db.all('PRAGMA table_info(books)', (err, updatedRows) => {
        const publisherColumn = updatedRows.find(row => row.name === 'publisher');
        if (!publisherColumn) {
          db.run('ALTER TABLE books ADD COLUMN publisher TEXT', (err) => {
            if (err) {
              console.error('Error adding publisher column:', err.message);
            } else {
              console.log('publisher column added successfully');
            }
            db.close();
          });
        } else {
          console.log('publisher column already exists');
          db.close();
        }
      });
    });
  }
});