# Educanet Books

A comprehensive book management system designed for educational institutions. This application allows administrators to manage a library of books, while users can browse, borrow, and return books.

## Features

- **Book Management**: Add, edit, delete, and search books
- **Barcode Scanning**: Quickly add books by scanning ISBN barcodes
- **User Authentication**: Email/password and Google authentication with auto-verification
- **Book Borrowing**: Track book loans and returns
- **Admin Dashboard**: Manage users, books, and loans. Includes a security panel with real-time analytics.
- **Responsive Design**: Works on desktop and mobile devices
- **Security Modes**: Normal, Mild, and High (lockdown) modes to control application access. Admin access to the security panel is maintained during lockdown.
- **Security Analytics**: Real-time tracking of suspicious IPs, rate limit exceeds, invalid token attempts, spam attempts, login attempts (hourly/daily), failed logins (hourly/daily), and API requests (hourly/daily).
- **Internationalization (i18n)**: Supports English and Czech (cs). Key components like the authentication popup are translated, with ongoing work to cover the entire application.

## Quick Setup

For the fastest setup, run:

```bash
npm install
cd client
npm install
cd ..
npm start
```

This will:
1. Install dependencies for both server and client
2. Start the server on http://localhost:8080
3. Start the client on http://localhost:80

## Manual Setup

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Server Configuration
PORT=8080
JWT_SECRET=your_jwt_secret_key

# Google Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Google Books API (for book information lookup)
GOOGLE_BOOKS_API_KEY=your_google_books_api_key

# Firebase Configuration (for authentication)
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_firebase_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_firebase_app_id
```

### Server Setup

```bash
cd server
npm install
npm run dev
```

The server will start on http://localhost:8080

### Client Setup

```bash
cd client
npm install
npm start
```

The client will start on http://localhost:80

## Security

The application includes several security features and considerations:

- **Security Modes**: As mentioned in features, the application can be set to 'normal', 'mild', or 'high' security modes. The 'high' mode acts as a lockdown, restricting access to most functionalities, but critical admin functions like managing security settings remain accessible.
- **Real-time Security Analytics**: The admin security panel provides insights into various security-related events, using real data (not mock data). This includes tracking:
    - Suspicious IP addresses accessing the system.
    - Instances where rate limiting has been triggered.
    - Attempts to use invalid or expired authentication tokens.
    - Potential spam or bot-like activities.
    - Hourly and daily counts of login attempts.
    - Hourly and daily counts of failed login attempts.
    - Hourly and daily counts of overall API requests.
- **Input Sanitization & SQL Injection Prevention**: The backend aims to use parameterized queries for database interactions to prevent SQL injection vulnerabilities. Code reviews and security testing are ongoing.
- **File Uploads**: For file uploads (e.g., book covers), MIME type validation is a planned enhancement to ensure only allowed file types are processed. A `TODO` for this is in place in `server/routes/books.js`.
- **Dependency Management**: Regularly review and update dependencies to mitigate known vulnerabilities. (User should run `npm audit` or similar tools).

## Project Structure

```
educanet-books/
├── client/                 # React frontend application
│   ├── public/             # Static assets
│   ├── src/                # Source code
│   │   ├── components/     # React components
│   │   ├── context/        # React context providers
│   │   ├── utils/          # Utility functions
│   │   ├── App.js          # Main application component
│   │   └── index.js        # Entry point
│   └── package.json        # Frontend dependencies
├── server/                 # Node.js backend application
│   ├── database/           # SQLite database
│   ├── middleware/         # Express middleware
│   ├── routes/             # API routes
│   ├── uploads/            # Uploaded files (book covers)
│   ├── utils/              # Utility functions
│   ├── config.js           # Server configuration
│   └── index.js            # Server entry point
└── package.json            # Root dependencies and scripts
```

## Key Features

### Authentication

The system supports two authentication methods:

1. **Email/Password Authentication**
   - Users register with email and password
   - Email verification required (unless disabled in settings)
   - Password reset functionality

2. **Google Authentication**
   - One-click sign-in with Google
   - Automatic account creation for new users
   - Automatic email verification
   - The authentication popup flow is now translated.

### Book Management

#### Adding Books

Books can be added in several ways:

1. **Manual Entry**
   - Fill in book details manually
   - Upload cover image or provide image URL

2. **ISBN Lookup**
   - Enter ISBN to fetch book details from Google Books API
   - Review and edit details before saving

3. **Barcode Scanning**
   - Use device camera to scan book barcode
   - Automatically fetch book details
   - Review and edit before saving

4. **Bulk Import**
   - Import books from CSV file
   - Template available for download

### User Roles

The system has two main user roles:

1. **Admin**
   - Full access to all features
   - Can manage books, users, and leases
   - Access to system settings and the security dashboard.

2. **User**
   - Can browse and search books
   - Can borrow and return books
   - Can suggest new books

## Troubleshooting

### Common Issues

1. **Database Initialization**
   - If you encounter database errors, try deleting the `server/database/bookleasing.db` file and restart the server to recreate it.

2. **Image Upload Issues**
   - If image uploads fail, check that the `server/uploads` directory exists and has write permissions.
   - The system uses base64 encoding as a fallback when server uploads fail.
   - *Note: MIME type validation for uploaded files is a pending enhancement.*

3. **Authentication Problems**
   - Ensure Firebase configuration is correct in the `.env` file.
   - Check that Google authentication is properly configured in Firebase console.

4. **API Connection Errors**
   - The client uses both proxied and direct API calls. If you encounter connection issues, check the `client/src/utils/api.js` file.

## License

### Educational Use License

**EduCanet Books - Educational Use License**

Copyright (c) 2023-2024 EduCanet

Permission is hereby granted, free of charge, to educational institutions, students, teachers, and academic researchers to use, copy, modify, and distribute this software and its documentation for educational and research purposes only, subject to the following conditions:

1. **Non-Commercial Use**: This software may not be used for commercial purposes. Commercial purposes include, but are not limited to, selling the software, incorporating the software into a commercial product, or using the software in any way that generates revenue.

2. **Attribution**: The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

3. **Educational Context**: Use of this software must be within an educational or academic research context.

4. **No Warranty**: THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

5. **Derivative Works**: Any derivative works based on this software must also be used exclusively for educational purposes and must be distributed under the same license terms.

6. **Termination**: This license automatically terminates if you violate any of these terms. Upon termination, you must destroy all copies of the software in your possession.

For commercial licensing inquiries, we don't care and don't ask us

## Deployment

### Vercel Deployment

This project is configured for easy deployment to Vercel. To deploy:

1. Push your code to a GitHub repository
2. Sign up for a Vercel account at https://vercel.com
3. Create a new project and link your GitHub repository
4. Configure the environment variables in Vercel:
   - JWT_SECRET
   - GOOGLE_BOOKS_API_KEY
   - FIREBASE_ADMIN_CREDENTIALS
5. Deploy with the `Production` environment

The application is configured to run the frontend on port 80 and has the necessary setup for Vercel's serverless functions.