import React, { useState } from 'react';
import axios from 'axios';

const SuggestionForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    isbn: '',
    description: '',
    cover_image: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [remainingCount, setRemainingCount] = useState(0);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleSubmitSuggestion = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      console.log('Submitting suggestion:', formData);
      const response = await axios.post('/api/suggestions', formData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Suggestion response:', response.data);
      setRemainingCount(response.data.remaining);
      setSubmitSuccess(true);
      setFormData({
        title: '',
        author: '',
        isbn: '',
        description: '',
        cover_image: ''
      });
      
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 5000);
      
    } catch (err) {
      console.error('Suggestion submission error:', err);
      if (err.response) {
        setError(err.response.data.message || 'Error submitting suggestion. Please try again.');
        console.log('Error response:', err.response.data);
      } else {
        setError('Network error. Please check your connection and try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      {/* Render your form here */}
    </div>
  );
};

export default SuggestionForm; 