import axios from 'axios';

/**
 * API Configuration
 * Configures Axios for API requests with interceptors for auth tokens
 */

// Environment variables
const isDevelopment = process.env.NODE_ENV === 'development';
const productionApiUrl = process.env.REACT_APP_API_URL;

// Determine base URL based on environment
let baseURL;

if (isDevelopment) {
  // In development:
  // 1. If using direct API calls (no proxy) - use full URL: http://localhost:8080/api
  // 2. If using proxy - use relative path: /api
  // Default to option 2 (using proxy) in development
  baseURL = '/api';
  console.log('[API] Using development proxy: requests to /api/* will be proxied to http://localhost:8080/api/*');
} else if (productionApiUrl) {
  // In production with custom API URL
  baseURL = `${productionApiUrl}/api`;
  console.log(`[API] Using production API URL: ${productionApiUrl}/api`);
} else {
  // In production without custom API URL (relative path)
  baseURL = '/api';
  console.log('[API] Using production relative API path: /api');
}

// Create API instance with appropriate base URL
const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 30000 // 30 second timeout
});

// Log API configuration
console.log(`[API] Initializing API client in ${isDevelopment ? 'development' : 'production'} mode`);
console.log(`[API] Base URL: ${api.defaults.baseURL}`);

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // In development, log requests for debugging
    if (isDevelopment) {
      console.log(`[API Request] ${config.method.toUpperCase()} ${config.url}`);
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling common errors
api.interceptors.response.use(
  (response) => {
    // In development, log successful responses
    if (isDevelopment) {
      console.log(`[API Response] ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`);
    }
    return response;
  },
  (error) => {
    // Log errors in both development and production
    console.error(
      `[API Error] ${error.response?.status || 'Network Error'} ${error.config?.method?.toUpperCase() || 'N/A'} ${error.config?.url || 'N/A'}`,
      error.response?.data
    );
    
    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      // Optional: Redirect to login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// Direct (non-proxied) API call helper for development testing
// This bypasses the proxy, useful for testing when proxy isn't working
api.direct = {
  get: (endpoint, config = {}) => {
    let url;
    if (isDevelopment) {
      url = `http://localhost:8080${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else if (productionApiUrl) {
      url = `${productionApiUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else {
      // Fallback to relative URL in production
      url = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    }
    console.log(`[API Direct] GET ${url}`);
    
    // Add authentication token to direct requests if available
    const headers = { ...config.headers };
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    try {
      return axios.get(url, { ...config, headers });
    } catch (error) {
      console.error('Direct API call failed:', error);
      throw error;
    }
  },
  post: (endpoint, data, config = {}) => {
    let url;
    if (isDevelopment) {
      url = `http://localhost:8080${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else if (productionApiUrl) {
      url = `${productionApiUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else {
      // Fallback to relative URL in production
      url = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    }
    console.log(`[API Direct] POST ${url}`);
    
    // Add authentication token to direct requests if available
    const headers = { ...config.headers };
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    try {
      return axios.post(url, data, { ...config, headers });
    } catch (error) {
      console.error('Direct API call failed:', error);
      throw error;
    }
  },
  put: (endpoint, data, config = {}) => {
    let url;
    if (isDevelopment) {
      url = `http://localhost:8080${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else if (productionApiUrl) {
      url = `${productionApiUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else {
      // Fallback to relative URL in production
      url = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    }
    console.log(`[API Direct] PUT ${url}`);
    
    // Add authentication token to direct requests if available
    const headers = { ...config.headers };
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    try {
      return axios.put(url, data, { ...config, headers });
    } catch (error) {
      console.error('Direct API call failed:', error);
      throw error;
    }
  },
  patch: (endpoint, data, config = {}) => {
    let url;
    if (isDevelopment) {
      url = `http://localhost:8080${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else if (productionApiUrl) {
      url = `${productionApiUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else {
      // Fallback to relative URL in production
      url = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    }
    console.log(`[API Direct] PATCH ${url}`);
    
    // Add authentication token to direct requests if available
    const headers = { ...config.headers };
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    try {
      return axios.patch(url, data, { ...config, headers });
    } catch (error) {
      console.error('Direct API call failed:', error);
      throw error;
    }
  },
  delete: (endpoint, config = {}) => {
    let url;
    if (isDevelopment) {
      url = `http://localhost:8080${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else if (productionApiUrl) {
      url = `${productionApiUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    } else {
      // Fallback to relative URL in production
      url = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    }
    console.log(`[API Direct] DELETE ${url}`);
    
    // Add authentication token to direct requests if available
    const headers = { ...config.headers };
    const token = localStorage.getItem('token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    try {
      return axios.delete(url, { ...config, headers });
    } catch (error) {
      console.error('Direct API call failed:', error);
      throw error;
    }
  }
};

export default api; 