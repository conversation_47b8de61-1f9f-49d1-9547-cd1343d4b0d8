// Configuration settings for the application

module.exports = {
  // JWT secret key for authentication
  secretKey: process.env.JWT_SECRET || 'your_jwt_secret',

  // Database configuration
  database: {
    path: 'database/bookleasing.db'
  },

  // Server configuration
  server: {
    port: process.env.PORT || 5000
  },

  // Security settings
  security: {
    rateLimitWindow: 60 * 1000, // 1 minute (in milliseconds)
    rateLimitMax: 100, // Maximum number of requests per window
    modes: {
      normal: {
        rateLimitMax: 100
      },
      mild: {
        rateLimitMax: 50
      },
      high: {
        rateLimitMax: 20
      }
    },
    recaptcha: {
      siteKey: process.env.RECAPTCHA_SITE_KEY || '6LewzkkrAAAAAE5Aq9a7Q5X1ceU1ouQ1SK3Rbt3C', // Production key
      secretKey: process.env.RECAPTCHA_SECRET_KEY || '6LewzkkrAAAAAAhCwmMvMWDUXWHhwYrn5m7jGik2' // Production key
    }
  }
};
