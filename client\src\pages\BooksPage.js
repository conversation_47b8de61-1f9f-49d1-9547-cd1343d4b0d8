import React, { useState, useEffect, useCallback } from 'react';
import { Container, Grid, Pagination, Typography, Box, TextField, Select, MenuItem, InputLabel, FormControl, CircularProgress, Alert, Paper, Button } from '@mui/material';
import axios from 'axios';
import BookCard from '../components/books/BookCard';
import { useDebounce } from '../hooks/useDebounce';
import { useLanguage } from '../context/LanguageContext';

const BooksPage = () => {
  const { translate } = useLanguage();
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalBooks, setTotalBooks] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);

  const debouncedSearch = useDebounce(searchQuery, 500);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    // ... fetch logic ...
  }, []);

  // Fetch books
  const fetchBooks = useCallback(async () => {
    setLoading(true);
    try {
      // ... fetch logic ...
      setBooks(response.data.books || response.data); // Handle potential API structure differences
      setTotalBooks(response.data.totalBooks || 0);
      setTotalPages(response.data.totalPages || 0);
      setError('');
    } catch (error) {
      console.error('Error fetching books:', error);
      setError(translate('Failed to fetch books. Please try again later.'));
      setBooks([]);
      setTotalBooks(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, debouncedSearch, selectedCategory, translate]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
    setCurrentPage(1); // Reset to first page when category changes
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {translate('Our Collection')}
      </Typography>

      {/* Filters */}
      <Paper elevation={1} sx={{ p: 2, mb: 4, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <TextField
          label={translate('Search by title or author')}
          variant="outlined"
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ flexGrow: 1, minWidth: '200px' }}
        />
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel id="category-select-label">{translate('Category')}</InputLabel>
          <Select
            labelId="category-select-label"
            value={selectedCategory}
            label={translate('Category')}
            onChange={handleCategoryChange}
          >
            <MenuItem value=""><em>{translate('All')}</em></MenuItem>
            {categories.map((category) => (
              <MenuItem key={category} value={category}>
                {translate(category)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Paper>

      {/* Books Grid or Status Messages */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : books.length > 0 ? (
        <>
          <Grid container spacing={3}>
            {books.map((book) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={book.id}>
                <BookCard book={book} />
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </>
      ) : (
        <Typography sx={{ textAlign: 'center', my: 5 }}>
          {translate('No books found matching your criteria.')}
        </Typography>
      )}
    </Container>
  );
};

export default BooksPage; 