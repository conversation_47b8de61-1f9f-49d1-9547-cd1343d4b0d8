import React from 'react';
import { Box, Typography, Paper, Chip, Divider } from '@mui/material';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import PersonIcon from '@mui/icons-material/Person';
import { useLanguage } from '../../context/LanguageContext';

const AccountSummary = ({ user, leaseStats }) => {
  const { translate } = useLanguage();
  
  // Default lease stats if not provided
  const stats = leaseStats || { activeLeases: 0, totalLeases: 0 };
  
  return (
    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        {translate('Account Information')}
      </Typography>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body1" sx={{ minWidth: 150, fontWeight: 'medium' }}>
            {translate('Account Type:')}
          </Typography>
          <Chip
            icon={user?.role === 'admin' ? <AdminPanelSettingsIcon fontSize="small" /> : <PersonIcon fontSize="small" />}
            label={translate(user?.role === 'admin' ? 'Administrator' : 'User')}
            color={user?.role === 'admin' ? 'secondary' : 'default'}
            variant="outlined"
          />
        </Box>
        
        <Divider />
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body1" sx={{ minWidth: 150, fontWeight: 'medium' }}>
            {translate('Active Leases:')}
          </Typography>
          <Typography variant="body1">
            {stats.activeLeases}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body1" sx={{ minWidth: 150, fontWeight: 'medium' }}>
            {translate('Total Leases:')}
          </Typography>
          <Typography variant="body1">
            {stats.totalLeases}
          </Typography>
        </Box>
        
        {stats.activeLeases === 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
            {translate('You don\'t have any active leases.')}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

export default AccountSummary; 