import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Html5Qrcode, Html5QrcodeSupportedFormats } from 'html5-qrcode';
import { Box, Button, Typography, IconButton as MaterialIconButton, CircularProgress, Card, CardContent, Select, MenuItem, FormControl, InputLabel, Paper, Chip, LinearProgress, Fab, TextField, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { CameraAlt as CameraIcon, FlipCameraAndroid as FlipIcon, Refresh as RefreshIcon, Close as CloseIcon, CenterFocusStrong as FocusIcon, CheckCircle as CheckIcon, Search as SearchIcon, FlashlightOn as FlashlightOnIcon, FlashlightOff as FlashlightOffIcon, Edit as EditIcon } from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';
import { isMobile } from 'react-device-detect'; // Used for mobile detection and optimizations
import { useTheme } from '@mui/material/styles';

// Constants
const SCANNER_ID = 'barcode-scanner-element';
const SCANNER_REGION_ID = 'barcode-scanner-region';

// Define Html5QrcodeScannerState for state checking
const Html5QrcodeScannerState = {
  SCANNING: 2
};

// Universal barcode format support for maximum compatibility
const BARCODE_FORMATS = [
  // Primary ISBN/book formats
  Html5QrcodeSupportedFormats.EAN_13,     // Most common ISBN format
  Html5QrcodeSupportedFormats.EAN_8,      // Short ISBN format
  Html5QrcodeSupportedFormats.UPC_A,      // US book barcodes
  Html5QrcodeSupportedFormats.UPC_E,      // Compact US barcodes

  // Linear barcode formats (often contain ISBN)
  Html5QrcodeSupportedFormats.CODE_128,   // High-density linear barcode
  Html5QrcodeSupportedFormats.CODE_39,    // Alphanumeric barcode
  Html5QrcodeSupportedFormats.CODE_93,    // Compact alphanumeric
  Html5QrcodeSupportedFormats.CODABAR,    // Numeric barcode
  Html5QrcodeSupportedFormats.ITF,        // Interleaved 2 of 5

  // 2D formats (may contain ISBN data)
  Html5QrcodeSupportedFormats.QR_CODE,    // QR codes with ISBN
  Html5QrcodeSupportedFormats.DATA_MATRIX, // Data Matrix codes
  Html5QrcodeSupportedFormats.PDF_417,    // PDF417 codes
  Html5QrcodeSupportedFormats.AZTEC,      // Aztec codes

  // Additional formats for international compatibility
  Html5QrcodeSupportedFormats.RSS_14,     // GS1 DataBar
  Html5QrcodeSupportedFormats.RSS_EXPANDED, // GS1 DataBar Expanded
  Html5QrcodeSupportedFormats.MAXICODE    // MaxiCode
];

// Enhanced scanner configuration with better adaptive sizing and cross-platform optimization
const getOptimizedScannerConfig = () => {
  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  const isAndroid = /Android/i.test(navigator.userAgent);
  const isWindows = /Windows/i.test(navigator.userAgent);
  const isMobileDevice = isIOS || isAndroid || /webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  return {
    fps: isMobileDevice ? 25 : 30, // Optimized FPS for better detection and performance
    qrbox: (viewfinderWidth, viewfinderHeight) => {
      // Safety check: ensure minimum dimensions
      if (!viewfinderWidth || !viewfinderHeight || viewfinderWidth < 100 || viewfinderHeight < 100) {
        return { width: 250, height: 120 };
      }

      // Adaptive sizing based on platform and orientation
      const isPortrait = viewfinderHeight > viewfinderWidth;

      if (isPortrait) {
        // Portrait mode - wider scanning area for mobile
        const width = Math.max(200, Math.min(viewfinderWidth * 0.85, 600));
        const height = Math.max(80, Math.min(width * 0.25, 150));
        return { width, height };
      } else {
        // Landscape mode - optimized for each platform
        let widthRatio = 0.7;
        let heightRatio = 0.25;

        if (isWindows) {
          widthRatio = 0.8; // Larger area for Windows webcams
          heightRatio = 0.3;
        } else if (isIOS) {
          widthRatio = 0.75; // Medium area for iOS
          heightRatio = 0.28;
        } else if (isAndroid) {
          widthRatio = 0.8; // Larger area for Android
          heightRatio = 0.3;
        }

        const width = Math.max(200, Math.min(viewfinderWidth * widthRatio, 800));
        const height = Math.max(80, Math.min(width * heightRatio, 200));
        return { width, height };
      }
    },
    experimentalFeatures: {
      useBarCodeDetectorIfSupported: true,
    },
    formatsToSupport: BARCODE_FORMATS,
    disableFlip: false, // Enable flip for better camera selection
    videoConstraints: getOptimizedVideoConstraints(),
    // Enhanced detection settings
    rememberLastUsedCamera: true,
    showTorchButtonIfSupported: false, // We handle torch manually
    showZoomSliderIfSupported: false,
    defaultZoomValueIfSupported: 1,
    useBarCodeDetectorIfSupported: true,
    // Improved scanning performance
    aspectRatio: isMobileDevice ? 1.0 : 1.333333,
    supportedScanTypes: BARCODE_FORMATS
  };
};

// Platform-specific video constraints with mobile-friendly settings
const getOptimizedVideoConstraints = () => {
  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  const isAndroid = /Android/i.test(navigator.userAgent);
  const isWindows = /Windows/i.test(navigator.userAgent);
  const isMobileDevice = isIOS || isAndroid || /webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  const baseConstraints = {
    width: { min: 640, ideal: isMobileDevice ? 1280 : 1920, max: 1920 },
    height: { min: 480, ideal: isMobileDevice ? 720 : 1080, max: 1080 },
    facingMode: 'environment'
  };

  // Simplified constraints for better mobile compatibility
  if (isMobileDevice) {
    // Mobile devices - use minimal constraints to avoid compatibility issues
    baseConstraints.advanced = [{
      autoFocus: true,
      torch: false
    }];
  } else if (isWindows) {
    // Windows webcam constraints - keep minimal for compatibility
    baseConstraints.advanced = [{
      autoFocus: true,
      focusMode: 'continuous'
    }];
  } else {
    // Desktop/other platforms - basic constraints
    baseConstraints.advanced = [{
      autoFocus: true,
      focusMode: 'continuous'
    }];
  }

  return baseConstraints;
};

// --- ISBN Validation Functions ---

// Validates ISBN-13 / EAN-13 checksum (Modulo 10)
const isValidISBN13 = (code) => {
  // Accept even shorter codes (5-13 digits) for partial scans of thin barcodes
  if (!code || code.length < 5 || !/^[0-9]{5,13}$/.test(code)) {
    return false;
  }

  // If we have a partial code (less than 13 digits), we can't validate the checksum
  // So we'll just check if it could be the beginning of a valid ISBN-13
  if (code.length < 13) {
    return code.startsWith('978') || code.startsWith('979');
  }

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(code.charAt(i)) * (i % 2 === 0 ? 1 : 3);
  }
  const checksum = (10 - (sum % 10)) % 10;
  return checksum === parseInt(code.charAt(12));
};

// Validates ISBN-10 checksum (Modulo 11)
const isValidISBN10 = (code) => {
  // Accept even shorter codes (4-9 digits) for partial scans of thin barcodes
  if (!code || code.length < 4 || !/^[0-9]{4,9}[0-9X]$/i.test(code)) {
    return false;
  }

  // If we have a partial code (less than 10 digits), we can't validate the checksum
  if (code.length < 10) {
    return true; // Accept any potential partial ISBN-10
  }

  code = code.toUpperCase(); // Ensure X is uppercase for comparison
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(code.charAt(i)) * (10 - i);
  }
  const calculatedChecksum = sum % 11;
  const checkDigit = (calculatedChecksum === 0) ? '0' : (calculatedChecksum === 1) ? 'X' : (11 - calculatedChecksum).toString();
  return code.charAt(9) === checkDigit;
};

// Validates EAN-8 checksum (Modulo 10)
const isValidEAN8 = (code) => {
  if (!code || code.length !== 8 || !/^[0-9]{8}$/.test(code)) {
    return false;
  }

  let sum = 0;
  for (let i = 0; i < 7; i++) {
    const digit = parseInt(code.charAt(i));
    sum += (i % 2 === 0) ? digit * 3 : digit;
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(code.charAt(7));
};

// Universal ISBN extraction from any barcode format
const extractISBNFromBarcode = (code) => {
  if (!code || typeof code !== "string") return null;

  // Clean the code
  const cleanCode = code.replace(/[^\dX]/gi, '');

  console.log('Extracting ISBN from barcode:', cleanCode);

  // Direct ISBN-13 (978/979 prefix)
  if (cleanCode.length === 13 && (cleanCode.startsWith('978') || cleanCode.startsWith('979'))) {
    if (isValidISBN13(cleanCode)) {
      return { isbn: cleanCode, format: 'ISBN-13', source: 'direct' };
    }
  }

  // Direct ISBN-10
  if (cleanCode.length === 10 && isValidISBN10(cleanCode)) {
    return { isbn: cleanCode, format: 'ISBN-10', source: 'direct' };
  }

  // UPC-A to ISBN-13 conversion (common in US)
  if (cleanCode.length === 12) {
    // Convert UPC-A to EAN-13 by adding leading zero
    const ean13 = '0' + cleanCode;
    if (ean13.startsWith('0978') || ean13.startsWith('0979')) {
      const isbn13 = ean13.substring(1); // Remove the leading 0
      if (isValidISBN13(isbn13)) {
        return { isbn: isbn13, format: 'ISBN-13', source: 'UPC-A conversion' };
      }
    }

    // Some UPC codes directly encode ISBN without the leading 0
    if (cleanCode.startsWith('978') || cleanCode.startsWith('979')) {
      // This is likely a truncated ISBN-13, try to validate
      return { isbn: cleanCode, format: 'ISBN-12 (truncated)', source: 'UPC-A direct' };
    }
  }

  // EAN-13 format
  if (cleanCode.length === 13) {
    // Check if it's a book-related EAN
    if (cleanCode.startsWith('978') || cleanCode.startsWith('979')) {
      return { isbn: cleanCode, format: 'ISBN-13', source: 'EAN-13' };
    }

    // Some international books use other prefixes
    const bookPrefixes = ['977', '978', '979']; // Include ISSN prefix 977 for periodicals
    if (bookPrefixes.some(prefix => cleanCode.startsWith(prefix))) {
      return { isbn: cleanCode, format: 'EAN-13 (book-related)', source: 'EAN-13 book' };
    }
  }

  // EAN-8 format (sometimes used for books)
  if (cleanCode.length === 8 && isValidEAN8(cleanCode)) {
    return { isbn: cleanCode, format: 'EAN-8', source: 'EAN-8' };
  }

  // Look for ISBN patterns within longer codes
  const isbnPatterns = [
    /978\d{10}/, // ISBN-13 pattern
    /979\d{10}/, // ISBN-13 pattern
    /\d{10}/, // ISBN-10 pattern
  ];

  for (const pattern of isbnPatterns) {
    const match = cleanCode.match(pattern);
    if (match) {
      const potentialISBN = match[0];
      if (potentialISBN.length === 13 && (potentialISBN.startsWith('978') || potentialISBN.startsWith('979'))) {
        if (isValidISBN13(potentialISBN)) {
          return { isbn: potentialISBN, format: 'ISBN-13', source: 'pattern extraction' };
        }
      } else if (potentialISBN.length === 10 && isValidISBN10(potentialISBN)) {
        return { isbn: potentialISBN, format: 'ISBN-10', source: 'pattern extraction' };
      }
    }
  }

  // If all else fails, but it's a reasonable length numeric code, return it
  if (cleanCode.length >= 8 && cleanCode.length <= 13 && /^\d+$/.test(cleanCode)) {
    return { isbn: cleanCode, format: 'Unknown numeric', source: 'fallback' };
  }

  return null;
};

// Enhanced validation function
const couldBeISBN = (code) => {
  const result = extractISBNFromBarcode(code);
  return result !== null;
};

// Enhanced barcode format detection
const detectBarcodeFormat = (code) => {
  if (!code) return 'unknown';

  const cleanCode = code.replace(/[^\dX]/gi, '');

  if (cleanCode.length === 13 && (cleanCode.startsWith('978') || cleanCode.startsWith('979'))) {
    return 'ISBN-13';
  } else if (cleanCode.length === 10) {
    return 'ISBN-10';
  } else if (cleanCode.length === 12) {
    return 'UPC-A';
  } else if (cleanCode.length === 8) {
    return 'EAN-8';
  } else if (cleanCode.length === 13) {
    return 'EAN-13';
  }

  return 'unknown';
};

// Convert ISBN-13 to ISBN-10 (only for 978 prefix)
const convertISBN13to10 = (isbn13) => {
  if (!isbn13 || isbn13.length !== 13 || !isbn13.startsWith('978')) {
    return null;
  }

  // Get 9 middle digits (remove 978 prefix and check digit)
  const middle = isbn13.substring(3, 12);

  // Calculate ISBN-10 check digit
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(middle.charAt(i)) * (10 - i);
  }

  const remainder = sum % 11;
  const checkDigit = remainder === 0 ? '0' :
                    remainder === 1 ? 'X' :
                    (11 - remainder).toString();

  return middle + checkDigit;
};

// Convert ISBN-10 to ISBN-13
const convertISBN10to13 = (isbn10) => {
  if (!isbn10 || isbn10.length !== 10) {
    return null;
  }

  // Add 978 prefix and remove original check digit
  const prefix = '978' + isbn10.substring(0, 9);

  // Calculate ISBN-13 check digit
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(prefix.charAt(i)) * (i % 2 === 0 ? 1 : 3);
  }

  const checkDigit = (10 - (sum % 10)) % 10;

  return prefix + checkDigit.toString();
};

const BarcodeScanner = ({ onDetected, onClose, embedded = false }) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // References and state
  const rootRef = useRef(null);              // Root container reference
  const scannerContainerRef = useRef(null);   // Ref for the direct container of the scanner element
  const scannerInstanceRef = useRef(null);  // Scanner instance reference
  const detectionsRef = useRef({});         // Track detections without state updates
  const lastProcessedRef = useRef(null);    // Last processed barcode
  const resizeObserverRef = useRef(null);   // Ref for the ResizeObserver
  const successSoundRef = useRef(null);     // Ref for the success sound
  const scannerStateRef = useRef({
    detectionInterval: null
  });

  // State
  const [status, setStatus] = useState('initializing');
  const [cameras, setCameras] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [detectionCount, setDetectionCount] = useState({});
  const [lastScan, setLastScan] = useState(null);
  const [torchEnabled, setTorchEnabled] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasScanned, setHasScanned] = useState(false);
  const [showManualInput, setShowManualInput] = useState(false);
  const [manualISBN, setManualISBN] = useState('');

  // Add state for tracking focus
  const [focusState, setFocusState] = useState('searching');
  const lastFocusTimeRef = useRef(0);
  const autoFocusIntervalRef = useRef(null);



  // Universal barcode scan handler with advanced ISBN extraction
  const handleScanSuccess = (decodedText, decodedResult) => {
    // Fast path: Skip processing if component not mounted or not in scanning state
    const scannerActive = scannerInstanceRef.current &&
                         scannerInstanceRef.current.getState &&
                         scannerInstanceRef.current.getState() === Html5QrcodeScannerState.SCANNING;

    if (!scannerActive || hasScanned || isProcessing) {
      console.log('Skipping barcode processing - scanner not active or already processed');
      return;
    }

    console.log('Barcode detected:', decodedText, 'Format:', decodedResult?.decodedResult?.format);

    // Clean the code
    const cleanCode = decodedText.trim().replace(/\s/g, '');

    if (!cleanCode || cleanCode.length < 6) {
      console.log('Skipping too short barcode:', cleanCode);
      return;
    }

    // Check if this is a duplicate scan
    if (lastProcessedRef.current === cleanCode) {
      console.log('Skipping duplicate scan:', cleanCode);
      return;
    }

    // Try to extract ISBN from any barcode format
    const isbnResult = extractISBNFromBarcode(cleanCode);

    console.log(`Processing barcode: ${cleanCode}`);
    console.log('ISBN extraction result:', isbnResult);

    // Accept any barcode that could contain an ISBN or is numeric
    const isValidBarcode = isbnResult !== null ||
                          (cleanCode.length >= 6 && cleanCode.length <= 20 && /^\d+$/.test(cleanCode));

    if (!isValidBarcode) {
      console.log('Barcode does not appear to contain ISBN:', cleanCode);
      return;
    }

    // Track detection count for this code
    const currentCount = detectionsRef.current[cleanCode] || 0;
    detectionsRef.current[cleanCode] = currentCount + 1;

    // Require fewer detections for confirmed ISBN formats
    let requiredDetections = 1;
    if (isbnResult && (isbnResult.format === 'ISBN-13' || isbnResult.format === 'ISBN-10')) {
      requiredDetections = 1; // Immediate processing for direct ISBN
    } else if (isbnResult && isbnResult.source === 'UPC-A conversion') {
      requiredDetections = 1; // Quick processing for UPC conversions
    } else {
      requiredDetections = 2; // Require confirmation for unknown formats
    }

    console.log(`Detection count for ${cleanCode}: ${detectionsRef.current[cleanCode]}/${requiredDetections} required`);

    // Process the barcode if it meets requirements
    if (detectionsRef.current[cleanCode] >= requiredDetections) {
      // Set processing state immediately to prevent multiple scans
      setIsProcessing(true);
      setHasScanned(true);

      // Stop further processing
      if (scannerStateRef.current?.detectionInterval) {
        clearInterval(scannerStateRef.current.detectionInterval);
        scannerStateRef.current.detectionInterval = null;
      }

      // Stop the scanner to prevent further scanning
      if (scannerInstanceRef.current) {
        try {
          scannerInstanceRef.current.pause();
        } catch (err) {
          console.log('Error pausing scanner:', err);
        }
      }

      // Provide immediate feedback
      if (successSoundRef.current) {
        successSoundRef.current.play().catch(() => {});
      }

      if (navigator.vibrate) {
        navigator.vibrate([100, 50, 100]); // Success vibration pattern
      }

      // Update state
      setStatus('success');
      setLastScan(cleanCode);
      lastProcessedRef.current = cleanCode;
      setFocusState('success');

      // Create comprehensive result object
      const result = {
        rawResult: cleanCode,
        isbn: isbnResult ? isbnResult.isbn : cleanCode,
        format: isbnResult ? isbnResult.format : 'Unknown',
        source: isbnResult ? isbnResult.source : 'raw',
        timestamp: Date.now(),
        confidence: isbnResult ? 'high' : 'medium'
      };

      // Add format-specific information
      if (isbnResult) {
        if (isbnResult.format === 'ISBN-13') {
          result.isbn_13 = isbnResult.isbn;
          const isbn10 = convertISBN13to10(isbnResult.isbn);
          if (isbn10) result.isbn_10 = isbn10;
        } else if (isbnResult.format === 'ISBN-10') {
          result.isbn_10 = isbnResult.isbn;
          const isbn13 = convertISBN10to13(isbnResult.isbn);
          if (isbn13) result.isbn_13 = isbn13;
        }

        // Add extraction details
        result.extractionDetails = {
          originalFormat: isbnResult.format,
          extractionMethod: isbnResult.source,
          validated: true
        };
      } else {
        // For non-ISBN barcodes, still try to find patterns
        result.extractionDetails = {
          originalFormat: 'Unknown',
          extractionMethod: 'raw scan',
          validated: false
        };
      }

      // Call the callback
      if (onDetected) {
        console.log('Calling onDetected with universal result:', result);
        onDetected(result);
      } else {
        console.error('onDetected callback is not defined');
      }

      // Reset detection counts
      detectionsRef.current = {};
    }
  };

  // Handle retry button click
  const handleRetry = () => {
    setErrorMessage('');
    setStatus('initializing');
    initScanner();
  };

  // Handle manual ISBN input
  const handleManualSubmit = () => {
    console.log('Manual submit called with ISBN:', manualISBN); // Debug log
    if (!manualISBN.trim()) return;

    const cleanISBN = manualISBN.trim().replace(/[^\dX]/gi, '');
    const isbnResult = extractISBNFromBarcode(cleanISBN);

    console.log('Clean ISBN:', cleanISBN, 'ISBN Result:', isbnResult); // Debug log

    if (isbnResult || (cleanISBN.length >= 8 && cleanISBN.length <= 13)) {
      setIsProcessing(true);
      setHasScanned(true);
      setShowManualInput(false);

      const result = {
        rawResult: cleanISBN,
        isbn: isbnResult ? isbnResult.isbn : cleanISBN,
        format: isbnResult ? isbnResult.format : 'Manual Input',
        source: 'manual',
        timestamp: Date.now(),
        confidence: 'manual'
      };

      console.log('Calling onDetected with result:', result); // Debug log

      if (onDetected) {
        onDetected(result);
      }
    }
  };

  // Enhanced tap-to-focus functionality with better cross-platform support and more aggressive focusing
  const handleTapToFocus = async (event) => {
    if (!scannerContainerRef.current || status !== 'scanning') return;

    try {
      const videoElement = document.querySelector(`#${SCANNER_ID} video`);
      if (!videoElement || !videoElement.srcObject) return;

      const videoTrack = videoElement.srcObject.getVideoTracks()[0];
      if (!videoTrack) return;

      // Provide haptic feedback immediately for better user experience
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }

      // Get tap coordinates relative to video element
      const rect = videoElement.getBoundingClientRect();
      const x = (event.clientX - rect.left) / rect.width;
      const y = (event.clientY - rect.top) / rect.height;

      // Show visual feedback at tap location
      showFocusIndicator(event.clientX - rect.left, event.clientY - rect.top);

      // Set focus state to indicate we're trying to focus
      setFocusState('focusing');

      // Detect device type for platform-specific optimizations
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
      const isAndroid = /Android/i.test(navigator.userAgent);
      const isWindows = /Windows/i.test(navigator.userAgent);

      // Try multiple focus approaches in sequence, starting with the most reliable for each platform
      let focusSuccess = false;

      // 1. First try: Use native camera focus API if available (most reliable)
      if (videoTrack.getCapabilities) {
        try {
          const capabilities = videoTrack.getCapabilities();
          console.log('Camera capabilities:', capabilities);

          // Create platform-optimized constraints with more aggressive focusing
          const constraints = { advanced: [{}] };

          // Platform-specific focus strategies
          if (isWindows) {
            // Windows-specific focus handling - avoid using focusMode which causes issues
            console.log('Applying Windows-specific camera handling');

            try {
              // Windows webcams typically work better with simple autoFocus
              // without any other constraints that might be unsupported
              await videoTrack.applyConstraints({
                advanced: [{ autoFocus: true }]
              });
              console.log('Applied Windows basic autoFocus');
              focusSuccess = true;
            } catch (err) {
              console.log('Windows basic autoFocus failed:', err.message);

              // Try an even more basic approach with minimal constraints
              try {
                // Some Windows webcams only support very basic constraints
                // Try with empty advanced array to reset any previous constraints
                await videoTrack.applyConstraints({
                  advanced: [{}]
                });
                console.log('Applied Windows minimal constraints');
                focusSuccess = true;
              } catch (innerErr) {
                console.log('Windows minimal constraints failed:', innerErr.message);
              }
            }

            // If the basic approaches worked, try to enhance with exposure settings
            // but in a separate try/catch to avoid failing the whole operation
            if (focusSuccess) {
              try {
                const enhancedConstraints = { advanced: [{}] };

                // If available, try to adjust exposure for better barcode contrast
                if (capabilities.exposureMode) {
                  enhancedConstraints.advanced[0].exposureMode = 'manual';
                }

                if (capabilities.exposureCompensation) {
                  const min = capabilities.exposureCompensation.min || -2;
                  const max = capabilities.exposureCompensation.max || 2;
                  // Slightly underexpose for better barcode contrast
                  enhancedConstraints.advanced[0].exposureCompensation = min + (max - min) * 0.4;
                }

                // Apply enhanced constraints separately
                await videoTrack.applyConstraints(enhancedConstraints);
                console.log('Applied Windows enhanced exposure settings');
              } catch (expErr) {
                // Just log the error but don't change focusSuccess status
                console.log('Windows enhanced exposure settings failed:', expErr.message);
              }
            }
          } else if (isIOS) {
            // iOS works best with simple constraints but needs more aggressive settings
            if (capabilities.focusMode) {
              // Force manual focus first to reset the camera's focus system
              if (capabilities.focusMode.includes('manual')) {
                await videoTrack.applyConstraints({
                  advanced: [{ focusMode: 'manual' }]
                });

                // Short delay to let the camera system reset
                await new Promise(resolve => setTimeout(resolve, 50));
              }

              // Then apply the most appropriate focus mode
              if (capabilities.focusMode.includes('single-shot')) {
                constraints.advanced[0].focusMode = 'single-shot';
              } else if (capabilities.focusMode.includes('continuous')) {
                constraints.advanced[0].focusMode = 'continuous';
              }
            }

            // iOS needs more precise focus distance control
            if (capabilities.focusDistance) {
              const min = capabilities.focusDistance.min || 0;
              const max = capabilities.focusDistance.max || 1;
              // Try multiple focus distances in sequence for better results
              const distances = [
                min + (max - min) * 0.2,  // Close focus
                min + (max - min) * 0.3,  // Medium-close focus (good for barcodes)
                min + (max - min) * 0.5   // Medium focus
              ];

              // Try each distance with a small delay between attempts
              for (const distance of distances) {
                try {
                  constraints.advanced[0].focusDistance = distance;
                  await videoTrack.applyConstraints(constraints);
                  await new Promise(resolve => setTimeout(resolve, 100));
                } catch (e) {
                  // Continue to next distance if this one fails
                }
              }
            }

            // iOS often needs exposure adjustments for barcode scanning
            if (capabilities.exposureMode) {
              constraints.advanced[0].exposureMode = 'continuous';
            }

            if (capabilities.exposureCompensation) {
              const min = capabilities.exposureCompensation.min || -2;
              const max = capabilities.exposureCompensation.max || 2;
              // Slightly underexpose for better barcode contrast
              constraints.advanced[0].exposureCompensation = min + (max - min) * 0.3;
            }
          } else if (isAndroid) {
            // Android often supports more advanced controls
            if (capabilities.focusMode) {
              // Try multiple focus modes in sequence for better results
              const focusModes = [];

              // Prioritize focus modes based on what's available
              if (capabilities.focusMode.includes('manual')) {
                focusModes.push('manual');
              }
              if (capabilities.focusMode.includes('single-shot')) {
                focusModes.push('single-shot');
              }
              if (capabilities.focusMode.includes('continuous')) {
                focusModes.push('continuous');
              }

              // Try each focus mode with a small delay between attempts
              for (const mode of focusModes) {
                try {
                  await videoTrack.applyConstraints({
                    advanced: [{ focusMode: mode }]
                  });
                  await new Promise(resolve => setTimeout(resolve, 100));

                  // If we've reached this point, the focus mode was applied successfully
                  constraints.advanced[0].focusMode = mode;
                  break;
                } catch (e) {
                  // Continue to next mode if this one fails
                }
              }
            }

            // Android often supports focus points - critical for tap-to-focus
            if (capabilities.pointsOfInterest) {
              constraints.advanced[0].pointsOfInterest = [{ x, y }];
            }

            // Android benefits from exposure and contrast adjustments
            if (capabilities.exposureMode) {
              constraints.advanced[0].exposureMode = 'continuous';
            }

            if (capabilities.exposureCompensation) {
              const min = capabilities.exposureCompensation.min || -2;
              const max = capabilities.exposureCompensation.max || 2;
              // Slightly underexpose for better barcode contrast
              constraints.advanced[0].exposureCompensation = min + (max - min) * 0.4;
            }

            // Slight zoom can help with focus on Android
            if (capabilities.zoom) {
              const zoomRange = capabilities.zoom.max - capabilities.zoom.min;
              constraints.advanced[0].zoom = capabilities.zoom.min + (zoomRange * 0.15);
            }
          } else {
            // Desktop/other platforms - use standard approach
            if (capabilities.focusMode) {
              if (capabilities.focusMode.includes('single-shot')) {
                constraints.advanced[0].focusMode = 'single-shot';
              } else if (capabilities.focusMode.includes('continuous')) {
                constraints.advanced[0].focusMode = 'continuous';
              }
            }

            // Add focus point if supported
            if (capabilities.pointsOfInterest) {
              constraints.advanced[0].pointsOfInterest = [{ x, y }];
            }
          }

          // Apply constraints
          await videoTrack.applyConstraints(constraints);
          console.log(`Applied platform-optimized focus for ${isIOS ? 'iOS' : isAndroid ? 'Android' : 'desktop'}`);
          focusSuccess = true;

          // Return to continuous focus after a delay if supported
          if (capabilities.focusMode && capabilities.focusMode.includes('continuous')) {
            setTimeout(async () => {
              try {
                if (videoTrack && videoTrack.readyState === 'live') {
                  await videoTrack.applyConstraints({
                    advanced: [{ focusMode: 'continuous' }]
                  });
                }
              } catch (err) {
                // Ignore errors when returning to continuous focus
              }
            }, 2500);
          }
        } catch (err) {
          console.log('Standard focus API failed:', err.message);
        }
      }

      // 2. Second try: Use ImageCapture API (good for some browsers, especially Chrome on Android)
      if (!focusSuccess && window.ImageCapture) {
        try {
          const imageCapture = new ImageCapture(videoTrack);

          // Different approach based on platform
          if (isIOS) {
            // iOS-specific ImageCapture approach
            if (imageCapture.setOptions) {
              // Try multiple focus modes in sequence
              const focusModes = ['single-shot', 'manual', 'continuous'];

              for (const mode of focusModes) {
                try {
                  await imageCapture.setOptions({
                    focusMode: mode,
                    pointsOfInterest: [{ x, y }]
                  });
                  await new Promise(resolve => setTimeout(resolve, 100));
                  console.log(`Applied ImageCapture API focus for iOS with mode: ${mode}`);
                  focusSuccess = true;
                  break;
                } catch (e) {
                  // Continue to next mode if this one fails
                }
              }
            }
          } else {
            // Android and other platforms
            if (imageCapture.setOptions) {
              const options = {
                focusMode: 'single-shot',
                pointsOfInterest: [{ x, y }]
              };

              // Add exposure compensation for Android
              if (isAndroid) {
                options.exposureCompensation = -0.7; // Slightly underexpose for better contrast
              }

              await imageCapture.setOptions(options);
              console.log('Applied ImageCapture API focus');
              focusSuccess = true;
            }
          }
        } catch (err) {
          console.log('ImageCapture API failed:', err.message);
        }
      }

      // 3. Third try: Platform-specific fallbacks with more aggressive approaches
      if (!focusSuccess) {
        if (isIOS) {
          // iOS-specific fallback: toggle focus modes rapidly with multiple attempts
          try {
            // For iOS, rapidly toggling between modes can sometimes trigger the native camera focus
            // Try multiple sequences with different timings
            const sequences = [
              // Sequence 1: manual -> continuous -> single-shot
              async () => {
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'manual' }] });
                await new Promise(resolve => setTimeout(resolve, 100));
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'continuous' }] });
                await new Promise(resolve => setTimeout(resolve, 100));
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'single-shot' }] });
              },
              // Sequence 2: continuous -> manual -> continuous
              async () => {
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'continuous' }] });
                await new Promise(resolve => setTimeout(resolve, 150));
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'manual' }] });
                await new Promise(resolve => setTimeout(resolve, 150));
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'continuous' }] });
              },
              // Sequence 3: single-shot only
              async () => {
                await videoTrack.applyConstraints({ advanced: [{ focusMode: 'single-shot' }] });
              }
            ];

            // Try each sequence until one works
            for (const sequence of sequences) {
              try {
                await sequence();
                await new Promise(resolve => setTimeout(resolve, 100));
                focusSuccess = true;
                break;
              } catch (e) {
                // Continue to next sequence if this one fails
              }
            }

            console.log('Applied iOS-specific focus workaround');
          } catch (err) {
            console.log('iOS focus workaround failed:', err.message);
          }
        } else if (isAndroid) {
          // Android-specific fallback: try multiple autofocus approaches
          try {
            // Try multiple approaches with different settings
            const approaches = [
              // Approach 1: Basic autofocus
              async () => {
                await videoTrack.applyConstraints({
                  advanced: [{ autoFocus: true }]
                });
              },
              // Approach 2: Autofocus with exposure compensation
              async () => {
                await videoTrack.applyConstraints({
                  advanced: [{
                    autoFocus: true,
                    exposureMode: 'continuous',
                    exposureCompensation: -1
                  }]
                });
              },
              // Approach 3: Manual focus with specific distance
              async () => {
                await videoTrack.applyConstraints({
                  advanced: [{
                    focusMode: 'manual',
                    focusDistance: 0.3
                  }]
                });
              }
            ];

            // Try each approach until one works
            for (const approach of approaches) {
              try {
                await approach();
                await new Promise(resolve => setTimeout(resolve, 100));
                focusSuccess = true;
                break;
              } catch (e) {
                // Continue to next approach if this one fails
              }
            }

            console.log('Applied Android-specific focus workaround');
          } catch (err) {
            console.log('Android focus workaround failed:', err.message);
          }
        }
      }

      // 4. Last resort: Basic autofocus for any platform with multiple attempts
      if (!focusSuccess) {
        try {
          // Try multiple basic approaches
          const basicApproaches = [
            // Approach 1: Simple autoFocus
            async () => {
              await videoTrack.applyConstraints({
                advanced: [{ autoFocus: true }]
              });
            },
            // Approach 2: Focus mode continuous
            async () => {
              await videoTrack.applyConstraints({
                advanced: [{ focusMode: 'continuous' }]
              });
            },
            // Approach 3: Focus mode auto
            async () => {
              await videoTrack.applyConstraints({
                advanced: [{ focusMode: 'auto' }]
              });
            }
          ];

          // Try each approach until one works
          for (const approach of basicApproaches) {
            try {
              await approach();
              await new Promise(resolve => setTimeout(resolve, 100));
              focusSuccess = true;
              break;
            } catch (e) {
              // Continue to next approach if this one fails
            }
          }

          console.log('Applied basic autofocus as last resort');
        } catch (err) {
          console.log('Basic autofocus failed:', err.message);
        }
      }

      // Update focus state based on success
      setFocusState(focusSuccess ? 'focused' : 'searching');

    } catch (err) {
      console.error('Error in tap-to-focus:', err);
      setFocusState('searching');
    }
  };

  // Enhanced auto-focus function that works at various distances
  const performContinuousAutoFocus = useCallback(async () => {
    if (!scannerInstanceRef.current || status !== 'scanning') return;

    try {
      const videoElement = document.querySelector(`#${SCANNER_ID} video`);
      if (!videoElement || !videoElement.srcObject) return;

      const stream = videoElement.srcObject;
      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) return;

      const capabilities = videoTrack.getCapabilities();
      if (!capabilities) return;

      // Cycle through different focus distances for better detection at various ranges
      const focusDistances = [0.1, 0.2, 0.3, 0.4, 0.5]; // Close to medium distances
      const currentTime = Date.now();
      const cycleIndex = Math.floor((currentTime / 2000) % focusDistances.length); // Change every 2 seconds
      const targetDistance = focusDistances[cycleIndex];

      // Apply focus with current distance
      if (capabilities.focusDistance) {
        await videoTrack.applyConstraints({
          advanced: [{
            focusMode: 'manual',
            focusDistance: targetDistance
          }]
        });
      } else if (capabilities.focusMode) {
        // Fallback to continuous focus mode
        await videoTrack.applyConstraints({
          advanced: [{
            focusMode: 'continuous'
          }]
        });
      }

      // Update focus state
      setFocusState('focused');
    } catch (error) {
      console.log('Continuous auto-focus error:', error.message);
      setFocusState('searching');
    }
  }, [status]);

  // Add visual focus indicator
  const showFocusIndicator = (x, y) => {
    // Remove any existing indicators
    const existingIndicator = document.getElementById('focus-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // Create new indicator
    const indicator = document.createElement('div');
    indicator.id = 'focus-indicator';
    indicator.style.position = 'absolute';
    indicator.style.left = `${x - 40}px`;
    indicator.style.top = `${y - 40}px`;
    indicator.style.width = '80px';
    indicator.style.height = '80px';
    indicator.style.border = '2px solid rgba(255,255,255,0.8)';
    indicator.style.borderRadius = '50%';
    indicator.style.pointerEvents = 'none';
    indicator.style.zIndex = '10';
    indicator.style.animation = 'focus-pulse 1.5s ease-out';

    // Add to DOM
    scannerContainerRef.current.appendChild(indicator);

    // Remove after animation completes
    setTimeout(() => {
      if (indicator && indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 1500);
  };

  // Use refs for reliable detection counting without state update delays
  const isMounted = useRef(true);

  // Scanner styling - minimal implementation
  const scanBoxStyle = `
    #${SCANNER_REGION_ID} {
      width: 100%;
      height: 100%;
    }

    /* Hide duplicates */
    #html5-qrcode-canvas-container,
    #html5qr-code-full-region {
      display: none !important;
    }

    /* Book cover placeholder styling */
    .book-placeholder {
      max-width: 120px !important;
      max-height: 180px !important;
      object-fit: contain !important;
      margin: 0 auto;
      display: block;
    }
  `;

  // Inject global styles for scanner element and animations
  if (typeof document !== 'undefined') {
    const styleTag = document.createElement('style');
    styleTag.id = 'barcode-scanner-global-style';
    styleTag.textContent = scanBoxStyle + `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }

      @keyframes focus-pulse {
        0% {
          opacity: 1;
          transform: scale(1);
          border-color: rgba(255,255,255,0.8);
        }
        50% {
          opacity: 0.6;
          transform: scale(1.2);
          border-color: rgba(255,255,255,0.4);
        }
        100% {
          opacity: 0;
          transform: scale(1.5);
          border-color: rgba(255,255,255,0.1);
        }
      }

      @keyframes scanLine {
        0% { transform: translateY(-100%); }
        100% { transform: translateY(100%); }
      }
    `;
    document.head.appendChild(styleTag);
  }

  // Html5QrcodeScannerState is now defined at the top of the file

  // Enhanced cleanup function to properly stop camera streams
  const cleanupScanner = () => {
    console.log('Starting scanner cleanup...');

    // Clear the barcode detection interval
    if (scannerStateRef.current?.detectionInterval) {
      clearInterval(scannerStateRef.current.detectionInterval);
      scannerStateRef.current.detectionInterval = null;
    }

    // Clear the auto-focus interval
    if (autoFocusIntervalRef.current) {
      clearInterval(autoFocusIntervalRef.current);
      autoFocusIntervalRef.current = null;
    }

    // Stop all camera streams FIRST before stopping scanner
    try {
      const videoElement = document.querySelector(`#${SCANNER_ID} video`);
      if (videoElement && videoElement.srcObject) {
        console.log('Stopping camera stream...');
        const stream = videoElement.srcObject;

        // Add event listeners to handle cleanup gracefully
        videoElement.onabort = null;
        videoElement.onerror = null;
        videoElement.onended = null;

        // Stop all tracks in the stream
        stream.getTracks().forEach(track => {
          console.log('Stopping track:', track.kind, track.label);
          try {
            track.stop();
          } catch (trackError) {
            console.log('Error stopping track:', trackError);
          }
        });

        // Clear the video source gracefully
        try {
          videoElement.pause();
          videoElement.srcObject = null;
          videoElement.load(); // Reset the video element
        } catch (videoError) {
          console.log('Error clearing video source:', videoError);
        }

        console.log('Camera stream stopped successfully');
      }
    } catch (err) {
      console.log('Error stopping camera stream:', err);
    }

    // Stop scanner with improved error handling
    if (scannerInstanceRef.current) {
      try {
        // Check if scanner is in a state where it can be stopped
        const canStop = scannerInstanceRef.current.getState &&
                       scannerInstanceRef.current.getState() === Html5QrcodeScannerState.SCANNING;

        if (canStop) {
          // Normal stop with promise handling
          scannerInstanceRef.current.stop()
            .then(() => {
              console.log('Scanner stopped successfully');
            })
            .catch(err => {
              // Just log the error but don't throw - we're in cleanup
              console.log('Non-critical error stopping scanner:', err);
            });
        } else {
          console.log('Scanner not in scanning state, skipping stop call');
        }
      } catch (err) {
        // Just log the error but don't throw - we're in cleanup
        console.log('Non-critical error in cleanupScanner:', err);
      } finally {
        // Always clear the reference to prevent memory leaks
        scannerInstanceRef.current = null;
      }
    }

    // Additional cleanup: find and stop any remaining video elements
    try {
      const allVideos = document.querySelectorAll('video');
      allVideos.forEach(video => {
        if (video.srcObject) {
          console.log('Found additional video element, stopping stream...');

          // Clear event handlers
          video.onabort = null;
          video.onerror = null;
          video.onended = null;

          const stream = video.srcObject;
          stream.getTracks().forEach(track => {
            try {
              track.stop();
            } catch (trackError) {
              console.log('Error stopping additional track:', trackError);
            }
          });

          try {
            video.pause();
            video.srcObject = null;
            video.load(); // Reset the video element
          } catch (videoError) {
            console.log('Error clearing additional video source:', videoError);
          }
        }
      });
    } catch (err) {
      console.log('Error in additional video cleanup:', err);
    }

    console.log('Scanner cleanup completed');
  };

  // Initialize scanner with aggressive cleanup
  useEffect(() => {
    console.log('BarcodeScanner: Initializing component, embedded:', embedded);
    isMounted.current = true;

    // Nuclear cleanup - remove ALL video elements in the container
    if (scannerContainerRef.current) {
      // Ensure container has minimum dimensions to prevent qrbox errors
      scannerContainerRef.current.style.minWidth = '320px';
      scannerContainerRef.current.style.minHeight = '320px';

      // Clear existing content
      scannerContainerRef.current.querySelectorAll('video').forEach(v => v.remove());
      scannerContainerRef.current.innerHTML = '';

      const scannerElement = document.createElement('div');
      scannerElement.id = SCANNER_ID;
      scannerContainerRef.current.appendChild(scannerElement);
    }

    // Small delay to ensure clean initialization
    const timer = setTimeout(() => {
      if (isMounted.current && scannerContainerRef.current) {
        initScanner();
      }
    }, 500); // Increased delay for more reliable initialization

    // Clean up on unmount with improved error handling
    return () => {
      // Set mounted flag to false first to prevent any new operations
      isMounted.current = false;

      // Clear any pending timers
      clearTimeout(timer);

      // Run cleanup with a small delay to ensure any in-progress operations complete
      // This helps prevent errors when component is unmounted quickly
      setTimeout(() => {
        try {
          // Run cleanup and don't wait for it to complete
          cleanupScanner();

          // Remove global styles
          const styleTag = document.getElementById('barcode-scanner-global-style');
          if (styleTag) {
            document.head.removeChild(styleTag);
          }

          // Final cleanup of any remaining video elements
          if (scannerContainerRef.current) {
            scannerContainerRef.current.querySelectorAll('video').forEach(v => {
              try {
                // Stop all tracks before removing
                if (v.srcObject) {
                  v.srcObject.getTracks().forEach(track => track.stop());
                }
                v.remove();
              } catch (err) {
                // Ignore errors during cleanup
              }
            });
          }
        } catch (err) {
          // Just log errors during cleanup, don't throw
          console.log('Non-critical error during component cleanup:', err);
        }
      }, 50); // Small delay to ensure clean unmounting
    };
  }, []);

  // Get available cameras method
  const getCameras = async () => {
    try {
      const devices = await Html5Qrcode.getCameras();
      if (devices && devices.length) {
        setCameras(devices);
        console.log('Cameras found:', devices);
      } else {
        setErrorMessage('No cameras found on your device');
      }
    } catch (error) {
      console.error('Error getting cameras:', error);
      setErrorMessage('Failed to access cameras: ' + error.message);
    }
  };

  // Initialize on component mount
  useEffect(() => {
    // Ensure the container has proper dimensions when component mounts
    if (rootRef.current) {
      // Force explicit size to prevent zero-width issues
      rootRef.current.style.minWidth = '320px';
      rootRef.current.style.display = 'block';
    }

    if (scannerContainerRef.current) {
      scannerContainerRef.current.style.minWidth = '320px';
      scannerContainerRef.current.style.minHeight = '320px';
      scannerContainerRef.current.style.display = 'block';
    }

    // Auto-start camera detection
    getCameras();
  }, []);

  // Expose cleanup function for external use
  useEffect(() => {
    // Return cleanup function that can be called externally
    if (onClose && typeof onClose === 'function') {
      // Store the cleanup function reference so parent can call it
      window.barcodeScannerCleanup = cleanupScanner;
    }

    return () => {
      // Clean up the global reference
      if (window.barcodeScannerCleanup) {
        delete window.barcodeScannerCleanup;
      }
    };
  }, [onClose]);

  // Initialize scanner with enhanced cross-platform optimization
  const initScanner = async () => {
    console.log('initScanner called, embedded:', embedded, 'container exists:', !!scannerContainerRef.current);
    if (!scannerContainerRef.current) return;

    setStatus('initializing');
    setErrorMessage('');
    setFocusState('initializing');

    // Get the optimized scanner configuration for current platform
    const config = getOptimizedScannerConfig();

    console.log('Initializing scanner with optimized config:', config);

    // Create the target div for the scanner dynamically if needed
    let scannerElement = document.getElementById(SCANNER_ID);
    if (!scannerElement && scannerContainerRef.current) {
      scannerContainerRef.current.innerHTML = ''; // Clear any existing elements
      scannerElement = document.createElement('div');
      scannerElement.id = SCANNER_ID;
      scannerContainerRef.current.appendChild(scannerElement);
    } else if (!scannerElement) {
      console.error("Cannot create scanner element, container doesn't exist.");
      setStatus('error');
      setErrorMessage('Scanner container element disappeared.');
      return;
    }

    // Get camera (moved inside setup to potentially re-fetch if needed)
    let cameraId = selectedCamera;
    if (!cameraId) {
      try {
        const devices = await Html5Qrcode.getCameras();
        console.log('Available cameras:', devices);
        if (devices && devices.length > 0) {
          // Prefer back camera ('environment')
          const backCamera = devices.find(d => d.label.toLowerCase().includes('back') || d.label.toLowerCase().includes('environment'));
          cameraId = backCamera ? backCamera.id : devices[0].id; // Fallback to the first camera
          console.log(`Auto-selected camera: ${devices.find(d => d.id === cameraId)?.label} (ID: ${cameraId})`);
          setSelectedCamera(cameraId); // Update state
        } else {
          throw new Error('No cameras found.');
        }
      } catch (err) {
        console.error('Failed to get cameras:', err);
        setErrorMessage(`Camera access error: ${err.message}`);
        setStatus('error');
        return; // Stop initialization if no camera
      }
    }

    // --- Create and Start Scanner ---
    try {
      // Ensure scanner instance uses the dynamic element ID
      const newScannerInstance = new Html5Qrcode(SCANNER_ID, { verbose: false });
      scannerInstanceRef.current = newScannerInstance;

      console.log(`Starting scanner with ID: ${cameraId ? cameraId : 'default'}, Config:`, config);

      await newScannerInstance.start(
          cameraId ? { deviceId: { exact: cameraId } } : { facingMode: 'environment' },
          config,
          (decodedText, decodedResult) => {
            // FIXED: Set status to scanning immediately when first barcode is detected
            // This ensures the state is correct when processing barcodes
            if (status !== 'scanning') {
              setStatus('scanning');
              // Add a small delay to ensure state is updated before processing the barcode
              setTimeout(() => {
                handleScanSuccess(decodedText, decodedResult);
              }, 50);
            } else {
              handleScanSuccess(decodedText, decodedResult);
            }
          },
          (errorMessage) => {
            // Ignore common 'QR code not found' errors, log others
            if (!errorMessage.includes('NotFoundException') && !errorMessage.includes('No MultiFormat Readers found')) {
               console.debug('Scan error ignored:', errorMessage);
            }
          }
      );

      console.log('Scanner started successfully.');
      // Set status to scanning here as well to ensure it's set as soon as possible
      setStatus('scanning');

      // Get video element after scanner is started
      setTimeout(() => {
        if (!isMounted.current) return;

        // Find the video element created by the scanner
        const videoElement = document.querySelector(`#${SCANNER_ID} video`);
        if (videoElement) {
          console.log('Starting intelligent barcode detection');

          // Start intelligent barcode detection
          const detectionInterval = intelligentBarcodeDetection(videoElement);

          // Store interval for cleanup
          scannerStateRef.current = {
            ...scannerStateRef.current,
            detectionInterval
          };

          // Start continuous auto-focus for better detection at various distances
          autoFocusIntervalRef.current = setInterval(() => {
            performContinuousAutoFocus();
          }, 3000); // Auto-focus every 3 seconds
        }
      }, 1000);

    } catch (error) {
      console.error('Failed to start scanner:', error);

      // Provide more helpful error messages for mobile users
      let userFriendlyMessage = error.message;

      if (error.message.includes('getUserMedia')) {
        userFriendlyMessage = translate('Camera access denied. Please allow camera permissions and try again.');
      } else if (error.message.includes('colorTemperature') || error.message.includes('non-finite')) {
        userFriendlyMessage = translate('Camera initialization failed. Please try refreshing the page.');
      } else if (error.message.includes('NotFoundError') || error.message.includes('No cameras found')) {
        userFriendlyMessage = translate('No camera found. Please ensure your device has a camera.');
      } else if (error.message.includes('NotAllowedError')) {
        userFriendlyMessage = translate('Camera permission denied. Please allow camera access in your browser settings.');
      } else if (error.message.includes('NotReadableError')) {
        userFriendlyMessage = translate('Camera is being used by another application. Please close other camera apps and try again.');
      } else if (error.message.includes('OverconstrainedError')) {
        userFriendlyMessage = translate('Camera settings not supported. Trying with basic settings...');

        // Try to restart with minimal constraints
        setTimeout(() => {
          initScanner();
        }, 1000);
        return;
      }

      setErrorMessage(userFriendlyMessage);
      setStatus('error');
    }
  };

  // Enhanced intelligent barcode detection with pattern recognition
  const intelligentBarcodeDetection = (videoElement) => {
    if (!videoElement || !videoElement.videoWidth) return null;

    console.log("Starting enhanced intelligent barcode detection");

    // Create a canvas for image analysis
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d', { willReadFrequently: true });

    // Apply initial focus
    setTimeout(() => {
      focusOnBarcodeArea(videoElement);
    }, 500);

    // Apply focus again after a short delay
    setTimeout(() => {
      focusOnBarcodeArea(videoElement);
    }, 1500);

    // Set up enhanced detection with pattern analysis
    const detectionInterval = setInterval(() => {
      if (!isMounted.current) return;

      // Analyze video frame for potential barcodes
      if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
        try {
          // Resize canvas to match video dimensions
          canvas.width = videoElement.videoWidth;
          canvas.height = videoElement.videoHeight;

          // Draw current video frame to canvas
          ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

          // Get image data from center strip where barcodes are likely to be
          const centerY = Math.floor(canvas.height / 2);
          const stripHeight = Math.floor(canvas.height * 0.3); // 30% of height
          const stripTop = centerY - Math.floor(stripHeight / 2);

          const centerStripData = ctx.getImageData(0, stripTop, canvas.width, stripHeight);

          // Simple barcode pattern detection
          const patternDetected = detectSimpleBarcodePattern(centerStripData);

          // Trigger focus if pattern detected and enough time has passed
          const canTriggerFocus = Date.now() - lastFocusTimeRef.current > 2000;

          if (patternDetected && canTriggerFocus) {
            console.log("Potential barcode pattern detected, focusing camera");
            focusOnBarcodeArea(videoElement);
            setFocusState('focused');
            lastFocusTimeRef.current = Date.now();
          } else if (!patternDetected && Date.now() - lastFocusTimeRef.current > 4000) {
            // Regular periodic focus
            focusOnBarcodeArea(videoElement);
            lastFocusTimeRef.current = Date.now();
          }
        } catch (err) {
          console.log("Error in frame analysis:", err.message);
        }
      }
    }, 800); // Check every 800ms

    return detectionInterval;
  };

  // Simple barcode pattern detection for better small barcode scanning
  const detectSimpleBarcodePattern = (imageData) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    // Fast sampling for performance
    const sampleStep = 6;
    let totalBrightness = 0;
    let sampledPixels = 0;

    // Sample pixels to determine average brightness
    for (let y = 0; y < height; y += sampleStep) {
      for (let x = 0; x < width; x += sampleStep) {
        const idx = (y * width + x) * 4;
        const gray = Math.round((data[idx] + data[idx + 1] + data[idx + 2]) / 3);
        totalBrightness += gray;
        sampledPixels++;
      }
    }

    const avgBrightness = totalBrightness / sampledPixels;
    const threshold = Math.max(80, Math.min(160, avgBrightness));

    // Check horizontal lines for barcode patterns
    const sampleLines = 6;
    const lineStep = Math.floor(height / sampleLines);

    for (let line = 0; line < sampleLines; line++) {
      const y = line * lineStep;
      let transitions = 0;
      let lastPixel = null;

      // Sample every 3rd pixel for speed
      for (let x = 0; x < width; x += 3) {
        const idx = (y * width + x) * 4;
        const gray = Math.round((data[idx] + data[idx + 1] + data[idx + 2]) / 3);
        const isBlack = gray < threshold;

        if (lastPixel !== null && lastPixel !== isBlack) {
          transitions++;
        }
        lastPixel = isBlack;
      }

      // Look for barcode-like transition patterns
      if (transitions > 12) {
        return true;
      }
    }

    return false;
  };



  // Enhanced focus helper function for better small barcode detection
  const focusOnBarcodeArea = async (videoElement) => {
    if (!videoElement || !isMounted.current) return;

    try {
      const stream = videoElement.srcObject;
      if (!stream) return;

      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) return;

      // Get capabilities to check what's supported
      const capabilities = videoTrack.getCapabilities ? videoTrack.getCapabilities() : {};

      // Set focus state to indicate we're trying to focus
      setFocusState('focusing');

      let focusSuccess = false;

      // Try advanced focus modes for better small barcode detection
      if (capabilities.focusMode) {
        try {
          // Try single-shot focus first (best for small barcodes)
          if (capabilities.focusMode.includes('single-shot')) {
            await videoTrack.applyConstraints({
              advanced: [{ focusMode: 'single-shot' }]
            });
            console.log('Applied single-shot focus for small barcode detection');
            focusSuccess = true;
          } else if (capabilities.focusMode.includes('manual')) {
            // Try manual focus with optimal distance for barcodes
            const constraints = { advanced: [{ focusMode: 'manual' }] };

            if (capabilities.focusDistance) {
              const min = capabilities.focusDistance.min || 0;
              const max = capabilities.focusDistance.max || 1;
              // Set focus distance optimal for barcode scanning (close to medium range)
              constraints.advanced[0].focusDistance = min + (max - min) * 0.3;
            }

            await videoTrack.applyConstraints(constraints);
            console.log('Applied manual focus for small barcode detection');
            focusSuccess = true;
          }
        } catch (err) {
          console.log('Advanced focus failed:', err.message);
        }
      }

      // Fallback to basic autofocus if advanced modes failed
      if (!focusSuccess) {
        try {
          await videoTrack.applyConstraints({
            advanced: [{ focusMode: 'continuous' }]
          });
          console.log('Applied continuous autofocus');
          focusSuccess = true;
        } catch (err) {
          // Final fallback
          try {
            await videoTrack.applyConstraints({
              advanced: [{ autoFocus: true }]
            });
            console.log('Applied basic autofocus');
            focusSuccess = true;
          } catch (fallbackErr) {
            console.log('All focus methods failed:', fallbackErr.message);
          }
        }
      }

      // Apply exposure compensation for better barcode contrast
      if (focusSuccess && capabilities.exposureCompensation) {
        try {
          const min = capabilities.exposureCompensation.min || -2;
          const max = capabilities.exposureCompensation.max || 2;
          // Slightly underexpose for better barcode contrast
          await videoTrack.applyConstraints({
            advanced: [{ exposureCompensation: min + (max - min) * 0.3 }]
          });
          console.log('Applied exposure compensation for better barcode contrast');
        } catch (expErr) {
          console.log('Exposure compensation failed:', expErr.message);
        }
      }

      setFocusState(focusSuccess ? 'focused' : 'searching');
      lastFocusTimeRef.current = Date.now();
    } catch (err) {
      console.log('Focus error:', err.message);
      setFocusState('searching');
    }
  };



  // If embedded, render just the scanner content without the overlay
  if (embedded) {
    return renderScannerContent();
  }

  // Otherwise render the full-screen overlay version
  return (
    <Box
      ref={rootRef}
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#000',
        overflow: 'hidden',
        zIndex: 9999,
        // Mobile: full screen
        // Desktop: popup style
        ...(window.innerWidth >= 768 && {
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'rgba(0, 0, 0, 0.8)',
        }),
      }}
    >
      {/* Desktop popup container */}
      {window.innerWidth >= 768 ? (
        <Box
          sx={{
            width: '90%',
            maxWidth: '900px',
            height: '85%',
            maxHeight: '700px',
            bgcolor: '#000',
            borderRadius: '16px',
            overflow: 'hidden',
            position: 'relative',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {renderScannerContent()}
        </Box>
      ) : (
        renderScannerContent()
      )}
    </Box>
  );

  function renderScannerContent() {
    return (
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: embedded ? '100%' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          bgcolor: '#000',
        }}
      >
      {/* Top control bar - only show if not embedded */}
      {!embedded && (
        <Paper
          elevation={0}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 10000, // Higher z-index to ensure buttons are clickable
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            px: { xs: 1, sm: 2 },
            py: { xs: 0.5, sm: 1 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            backdropFilter: 'blur(10px)',
            minHeight: { xs: '48px', sm: '56px' },
          }}
        >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
          <MaterialIconButton
            onClick={() => {
              console.log('Close button clicked'); // Debug log
              cleanupScanner();
              if (onClose) {
                onClose();
              }
            }}
            sx={{
              color: 'white',
              p: { xs: 1, sm: 1.5 },
              '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
              zIndex: 10000, // Ensure it's above everything
            }}
          >
            <CloseIcon fontSize={window.innerWidth < 600 ? 'medium' : 'large'} />
          </MaterialIconButton>
          <Typography
            variant={window.innerWidth < 600 ? "subtitle1" : "h6"}
            sx={{
              fontWeight: 500,
              fontSize: { xs: '1rem', sm: '1.25rem' },
              display: { xs: 'none', sm: 'block' }, // Hide title on very small screens
            }}
          >
            {translate('Barcode Scanner')} {showManualInput && '(Manual Input Open)'}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
          {/* Focus indicator */}
          <Chip
            icon={focusState === 'focused' ? <CheckIcon /> : <FocusIcon />}
            label={focusState === 'focused' ? translate('Focused') : translate('Focusing')}
            size="small"
            color={focusState === 'focused' ? 'success' : 'default'}
            variant={focusState === 'focused' ? 'filled' : 'outlined'}
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.3)',
              fontSize: { xs: '0.7rem', sm: '0.8rem' },
              height: { xs: '24px', sm: '32px' },
              display: { xs: 'none', sm: 'flex' }, // Hide on very small screens
              '& .MuiChip-icon': { color: 'inherit' }
            }}
          />

          {/* Manual input button */}
          <MaterialIconButton
            onClick={() => {
              console.log('Manual input button clicked'); // Debug log
              setShowManualInput(true);
            }}
            sx={{
              color: 'white',
              p: { xs: 1, sm: 1.5 },
              '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
              zIndex: 10000, // Ensure it's above everything
            }}
          >
            <EditIcon fontSize={window.innerWidth < 600 ? 'medium' : 'large'} />
          </MaterialIconButton>

          {/* Retry button */}
          <MaterialIconButton
            onClick={handleRetry}
            sx={{
              color: 'white',
              p: { xs: 1, sm: 1.5 },
              '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
            }}
          >
            <RefreshIcon fontSize={window.innerWidth < 600 ? 'medium' : 'large'} />
          </MaterialIconButton>
        </Box>
      </Paper>
      )}

      {/* Scanner container */}
      <Box
        ref={scannerContainerRef}
        id={SCANNER_REGION_ID}
        onClick={handleTapToFocus}
        sx={{
          position: embedded ? 'relative' : 'absolute',
          top: embedded ? 'auto' : 0,
          left: embedded ? 'auto' : 0,
          right: embedded ? 'auto' : 0,
          bottom: embedded ? 'auto' : 0,
          width: '100%',
          height: embedded ? '400px' : '100%',
          minHeight: embedded ? '400px' : 'auto',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: '#000',
          flexGrow: embedded ? 1 : 0,
          '& video': {
            width: '100% !important',
            height: '100% !important',
            objectFit: 'cover',
          },
          '& #barcode-scanner-element': {
            width: '100% !important',
            height: '100% !important',
            border: 'none !important',
            outline: 'none !important',
          },
          '& #barcode-scanner-element > div': {
            width: '100% !important',
            height: '100% !important',
            border: 'none !important',
            outline: 'none !important',
          },
          '& #barcode-scanner-element canvas': {
            border: 'none !important',
            outline: 'none !important',
          },
          '& #barcode-scanner-element video': {
            border: 'none !important',
            outline: 'none !important',
          }
        }}
      >
        {/* Corner brackets scanning overlay */}
        {status === 'scanning' && (
          <>
            {/* Corner brackets frame */}
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: { xs: '85%', sm: '75%', md: '65%' },
                maxWidth: { xs: '280px', sm: '400px' },
                height: { xs: '160px', sm: '120px', md: '100px' },
                zIndex: 999,
                pointerEvents: 'none',
              }}
            >
              {/* Corner brackets */}
              {[
                { top: 0, left: 0, borderTop: true, borderLeft: true },
                { top: 0, right: 0, borderTop: true, borderRight: true },
                { bottom: 0, left: 0, borderBottom: true, borderLeft: true },
                { bottom: 0, right: 0, borderBottom: true, borderRight: true }
              ].map((corner, index) => (
                <Box
                  key={index}
                  sx={{
                    position: 'absolute',
                    ...corner,
                    width: { xs: '30px', sm: '35px', md: '40px' },
                    height: { xs: '30px', sm: '35px', md: '40px' },
                    borderTop: corner.borderTop ? `${window.innerWidth < 600 ? '3px' : '4px'} solid ${focusState === 'success' ? theme.palette.success.main : 'rgba(255, 255, 255, 0.9)'}` : 'none',
                    borderBottom: corner.borderBottom ? `${window.innerWidth < 600 ? '3px' : '4px'} solid ${focusState === 'success' ? theme.palette.success.main : 'rgba(255, 255, 255, 0.9)'}` : 'none',
                    borderLeft: corner.borderLeft ? `${window.innerWidth < 600 ? '3px' : '4px'} solid ${focusState === 'success' ? theme.palette.success.main : 'rgba(255, 255, 255, 0.9)'}` : 'none',
                    borderRight: corner.borderRight ? `${window.innerWidth < 600 ? '3px' : '4px'} solid ${focusState === 'success' ? theme.palette.success.main : 'rgba(255, 255, 255, 0.9)'}` : 'none',
                    transition: 'border-color 0.3s ease',
                  }}
                />
              ))}
            </Box>


          </>
        )}

        {/* Loading overlay */}
        {isProcessing && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(128, 128, 128, 0.7)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1003,
              backdropFilter: 'blur(4px)',
            }}
          >
            <CircularProgress
              size={60}
              sx={{
                color: 'white',
                mb: 2,
                filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.3))'
              }}
            />
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                fontWeight: 500,
                textAlign: 'center',
                textShadow: '0 0 8px rgba(0, 0, 0, 0.5)'
              }}
            >
              {translate('Processing...')}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                mt: 1,
                textAlign: 'center',
                textShadow: '0 0 8px rgba(0, 0, 0, 0.5)'
              }}
            >
              {translate('Please wait while we process your barcode')}
            </Typography>
          </Box>
        )}
      </Box>
      {/* Enhanced bottom status panel - only show if not embedded */}
      {!embedded && (
        <Paper
          elevation={0}
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1001,
            bgcolor: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            px: { xs: 1.5, sm: 2 },
            py: { xs: 1.5, sm: 2 },
            backdropFilter: 'blur(10px)',
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            minHeight: { xs: '60px', sm: '80px' },
          }}
        >
        {status === 'error' ? (
          // Error state with retry button
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body1" gutterBottom sx={{ color: '#ff6b6b', mb: 2 }}>
              {errorMessage || translate('Failed to access camera')}
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={handleRetry}
              sx={{
                minHeight: { xs: '48px', sm: '40px' },
                borderRadius: '24px',
                px: 3,
                fontWeight: 600
              }}
            >
              {translate('Try Again')}
            </Button>
          </Box>
        ) : status === 'initializing' ? (
          // Loading state
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2, py: 1 }}>
            <CircularProgress size={24} sx={{ color: theme.palette.primary.main }} />
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {translate('Starting camera...')}
            </Typography>
          </Box>
        ) : errorMessage ? (
          // Error message
          <Box sx={{ textAlign: 'center', py: 1 }}>
            <Typography sx={{ color: '#ff6b6b' }}>
              {errorMessage}
            </Typography>
          </Box>
        ) : status === 'scanning' ? (
          // Enhanced scanning instructions
          <Box sx={{ textAlign: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
              {focusState === 'focusing' && (
                <CircularProgress size={18} sx={{ color: theme.palette.primary.main }} />
              )}
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {focusState === 'focusing' ? translate('Auto-focusing...') :
                 focusState === 'focused' ? translate('Ready to scan!') :
                 translate('Position barcode in frame')}
              </Typography>
              {focusState === 'focused' && (
                <Box sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.success.main,
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%, 100%': { opacity: 1, transform: 'scale(1)' },
                    '50%': { opacity: 0.5, transform: 'scale(1.2)' },
                  }
                }} />
              )}
            </Box>

            <Typography variant="caption" sx={{ opacity: 0.8, display: 'block' }}>
              {focusState === 'focused'
                ? translate('Tap anywhere to refocus • Supports all barcode formats')
                : translate('Tap screen to focus • Works with ISBN, UPC, EAN codes')
              }
            </Typography>
          </Box>
        ) : status === 'success' ? (
          // Success message with details
          <Box sx={{ textAlign: 'center', py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
              <CheckIcon sx={{ color: theme.palette.success.main, fontSize: '20px' }} />
              <Typography variant="body1" sx={{ color: theme.palette.success.main, fontWeight: 600 }}>
                {translate('Barcode detected!')}
              </Typography>
            </Box>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              {translate('Processing book information...')}
            </Typography>
          </Box>
        ) : (
          // Default ready state
          <Box sx={{ textAlign: 'center', py: 1 }}>
            <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5 }}>
              {translate('Universal Barcode Scanner')}
            </Typography>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              {translate('Ready to scan any barcode containing ISBN')}
            </Typography>
          </Box>
        )}
      </Paper>
      )}
      <audio ref={successSoundRef} src="data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjI5LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAAFowCtra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2tra2t//////////////////////////////////////////////////////////////////8AAAAATGF2YzU4LjU0AAAAAAAAAAAAAAAAJAAAAAAAAAAFo3yofEgAAAAAAAAAAAAAAAAA//tUZAAP8vIxWhMPSjAAAA0gAAABGQjHWGwxKOhaACwAAAABPG5LDsIBgEAQBA5a+CYP1/BA+D9T4Pg+CAIAgCB8Hwf/2T1Mz5PUTPk/l///1AgXCAYDAQE3LVmv5iY0FmIjAI0jiORyORyONICAYDIZDIZDIZDIZDIZDJXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/+xRk4Y/xZQ5YGe9rOAAADSAAAAEF7DVaZ7EM4AAANIAAAAEXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/+5Rk1Q/0JDlR1n6bwAAANIAAAARPQNUxnvQzgAAA0gAAABFXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/+1JE0Y/zYjJUGflrOAAADSAAAAEQyLlNZ+UKwAAANIAAAARXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/+xJk7Y/wxgxXGw84SAAANIAAAAEQ8DVeZ+Ds4AAANIAAAAEXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/+xRlBQ/wAABpAAAACAAADSAAAAEAAAGkAAAAIAAANIAAAARXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"></audio>

      {/* Manual Input Dialog */}
      <Dialog
        open={showManualInput}
        onClose={() => setShowManualInput(false)}
        maxWidth="sm"
        fullWidth
        sx={{
          zIndex: 10001, // Higher than the scanner overlay
          '& .MuiDialog-paper': {
            borderRadius: 2,
            bgcolor: 'background.paper',
            zIndex: 10001,
          },
          '& .MuiBackdrop-root': {
            zIndex: 10000,
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          {translate('Manual Input')}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
            {translate('Enter ISBN manually')}
          </Typography>
          <Typography variant="caption" sx={{ mb: 1, display: 'block', color: 'text.secondary' }}>
            Debug: Dialog is open = {showManualInput.toString()}
          </Typography>
          <TextField
            autoFocus
            fullWidth
            label={translate('Enter ISBN')}
            value={manualISBN}
            onChange={(e) => {
              console.log('TextField onChange:', e.target.value); // Debug log
              setManualISBN(e.target.value);
            }}
            placeholder="978-0-123456-78-9"
            variant="outlined"
            sx={{ mt: 1 }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleManualSubmit();
              }
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setShowManualInput(false)}>
            {translate('Close')}
          </Button>
          <Button
            onClick={handleManualSubmit}
            variant="contained"
            disabled={!manualISBN.trim()}
          >
            {translate('Submit')}
          </Button>
        </DialogActions>
      </Dialog>
      </Box>
    );
  }
};

export default BarcodeScanner;
