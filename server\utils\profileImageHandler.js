const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * Downloads a profile image from a URL and saves it to the local filesystem
 * @param {string} imageUrl - The URL of the image to download
 * @param {string} userId - The user ID to associate with the image
 * @returns {Promise<string>} - The path to the saved image (relative to server root)
 */
async function downloadProfileImage(imageUrl, userId) {
  if (!imageUrl) {
    console.log('No profile image URL provided');
    return null;
  }

  try {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '../uploads/profile_pictures');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Generate a unique filename
    const fileExtension = path.extname(imageUrl.split('?')[0]) || '.jpg';
    const filename = `${userId}-${Date.now()}${fileExtension}`;
    const filePath = path.join(uploadsDir, filename);
    const relativePath = `/uploads/profile_pictures/${filename}`;

    console.log(`Downloading profile image from ${imageUrl}`);
    
    // Download the image
    const response = await axios({
      method: 'get',
      url: imageUrl,
      responseType: 'stream'
    });

    // Save the image to the filesystem
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`Profile image saved to ${filePath}`);
        resolve(relativePath);
      });
      writer.on('error', (err) => {
        console.error('Error saving profile image:', err.message);
        reject(err);
      });
    });
  } catch (error) {
    console.error('Error downloading profile image:', error.message);
    return null;
  }
}

module.exports = { downloadProfileImage };
