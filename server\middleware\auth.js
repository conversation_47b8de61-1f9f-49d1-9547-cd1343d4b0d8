const jwt = require('jsonwebtoken');
// const { secretKey } = require('../config'); // secretKey is not used, JWT_SECRET is used directly
const { getSecurityStats } = require('../routes/security'); // Import getSecurityStats function

// Authentication middleware to verify JWT tokens
const authenticateToken = (req, res, next) => {
  // Get the token from the request headers
  // Check for both formats: Authorization: Bearer token and x-auth-token: token
  const authHeader = req.headers['authorization'];
  const xAuthToken = req.headers['x-auth-token'];

  console.log('Auth Headers:', {
    authorization: authHeader ? 'present' : 'missing',
    xAuthToken: xAuthToken ? 'present' : 'missing'
  });

  let token;

  if (authHeader) {
    // Extract token from "Bearer TOKEN" format
    token = authHeader.split(' ')[1];
    console.log('Using Authorization header token');
  } else if (xAuthToken) {
    // Use the x-auth-token directly
    token = xAuthToken;
    console.log('Using x-auth-token header token');
  }

  if (!token) {
    console.log('No token provided in request headers');
    return res.status(401).json({ message: 'Authentication required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret', (err, user) => {
    if (err) {
      console.error('Token verification error:', err);
      // Track invalid token attempt
      try {
        const securityStats = getSecurityStats();
        // Note: This is just for tracking, the actual increment is handled in security.js
      } catch (securityError) {
        console.error('Error tracking invalid token:', securityError);
      }
      return res.status(403).json({ message: 'Invalid or expired token' });
    }

    // Log the decoded user info
    console.log('Token verified successfully for user:', {
      id: user.id,
      username: user.username,
      role: user.role || 'no role'
    });

    // Attach user data to the request object
    req.user = user;
    next();
  });
};

// Admin authorization middleware
const requireAdmin = (req, res, next) => {
  // Check if user is authenticated first
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  // Check if user has admin role
  if (req.user.role !== 'admin') {
    console.log(`Access denied for user ${req.user.username} (role: ${req.user.role})`);
    return res.status(403).json({ message: 'Admin access required' });
  }

  console.log(`Admin access granted for user ${req.user.username}`);
  next();
};

module.exports = {
  authenticateToken,
  requireAdmin
};
