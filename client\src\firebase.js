import { initializeApp } from 'firebase/app';
import { 
  get<PERSON><PERSON>, 
  GoogleAuthProvider, 
  sendEmailVerification, 
  sendPasswordResetEmail, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signInWithRedirect, 
  getRedirectResult, 
  signInWithPopup,
  applyActionCode,
  verifyPasswordResetCode,
  confirmPasswordReset
} from 'firebase/auth';

// Fetch Firebase config from the server
const fetchFirebaseConfig = async () => {
  try {
    const response = await fetch('/api/firebase-config');
    if (!response.ok) {
      const text = await response.text();
      console.error('Failed to fetch Firebase config from server. Status:', response.status, 'Response:', text);
      throw new Error('Failed to fetch Firebase config from server');
    }
    const json = await response.json();
    console.log('Fetched Firebase config:', json);
    return json;
  } catch (err) {
    console.error('Error fetching Firebase config:', err);
    throw err;
  }
};

// Initialize Firebase asynchronously
const firebaseInitPromise = fetchFirebaseConfig().then(firebaseConfig => {
  // Log Firebase config for debugging (without sensitive values)
  console.log('Firebase config:', {
    ...firebaseConfig,
    apiKey: firebaseConfig.apiKey ? '****' : undefined
  });
  // Check for required fields
  if (!firebaseConfig || !firebaseConfig.apiKey || !firebaseConfig.authDomain || !firebaseConfig.projectId) {
    throw new Error('Invalid Firebase config received from server: ' + JSON.stringify(firebaseConfig));
  }
  const app = initializeApp(firebaseConfig);
  const auth = getAuth(app);
  const googleProvider = new GoogleAuthProvider();
  return { auth, googleProvider };
}).catch(error => {
  console.error('Error initializing Firebase:', error);
  // Create fallback objects to prevent crashes
  return {
    auth: undefined,
    googleProvider: undefined
  };
});

export { 
  firebaseInitPromise, 
  sendEmailVerification, 
  sendPasswordResetEmail, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signInWithRedirect, 
  getRedirectResult, 
  signInWithPopup,
  applyActionCode,
  verifyPasswordResetCode,
  confirmPasswordReset
};