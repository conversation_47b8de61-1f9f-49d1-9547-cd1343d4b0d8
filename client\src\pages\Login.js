import React, { useState, useContext, useEffect } from 'react';
import { useNavigate, Link as RouterLink, useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  TextField,
  Button,
  Box,
  Paper,
  Link,
  Alert,
  CircularProgress,
  Divider,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { AuthContext } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { useSecurity } from '../context/SecurityContext';
import GoogleAuth from '../components/auth/GoogleAuth';
import useRecaptcha from '../hooks/useRecaptcha';
import api from '../utils/api';

const Login = () => {
  const { translate } = useLanguage();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [verificationMessage, setVerificationMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const { login, error, isAuthenticated, setUser, setIsAuthenticated, setError } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const { securityStatus } = useSecurity();
  const { executeRecaptcha, isReady: recaptchaReady, error: recaptchaError } = useRecaptcha(securityStatus.requiresRecaptcha);

  // Check if the user was redirected from verification
  useEffect(() => {
    if (location.state?.message) {
      setVerificationMessage(location.state.message);
    }
  }, [location.state]);



  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };



  const validateForm = () => {
    const errors = {};

    if (!formData.email) {
      errors.email = translate('Email is required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = translate('Email is invalid');
    }

    if (!formData.password) {
      errors.password = translate('Password is required');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.email || !formData.password) {
      setLoginError(translate('Email and password are required'));
      return;
    }

    try {
      setLoading(true);
      setLoginError(null);

      // Get reCAPTCHA token if required
      let recaptchaToken = null;
      console.log(`[LOGIN CLIENT] Security status:`, securityStatus);
      console.log(`[LOGIN CLIENT] reCAPTCHA required:`, securityStatus.requiresRecaptcha);
      console.log(`[LOGIN CLIENT] reCAPTCHA ready:`, recaptchaReady);

      if (securityStatus.requiresRecaptcha) {
        if (!recaptchaReady) {
          setLoginError(translate('reCAPTCHA is loading. Please wait and try again.'));
          setLoading(false);
          return;
        }

        recaptchaToken = await executeRecaptcha('login');
        console.log(`[LOGIN CLIENT] reCAPTCHA token generated:`, !!recaptchaToken);
        if (!recaptchaToken) {
          setLoginError(translate('reCAPTCHA verification failed. Please try again.'));
          setLoading(false);
          return;
        }
      }

      // Prepare login data with reCAPTCHA token if needed
      const loginDataWithRecaptcha = {
        ...formData,
        ...(recaptchaToken && { recaptchaToken })
      };

      console.log(`[LOGIN CLIENT] Sending login data:`, {
        email: loginDataWithRecaptcha.email,
        hasPassword: !!loginDataWithRecaptcha.password,
        hasRecaptcha: !!loginDataWithRecaptcha.recaptchaToken
      });

      let loginData;
      try {
        // Try regular context login first
        loginData = await login(loginDataWithRecaptcha);
      } catch (loginError) {
        console.error('Regular login failed, trying direct API call:', loginError);

        // Fallback to direct API call if context login fails
        loginData = await directLoginFallback(loginDataWithRecaptcha);

        // Need to manually update auth context since we bypassed it
        if (loginData?.user) {
          setUser(loginData.user);
          setIsAuthenticated(true);
        }
      }

      console.log('Login successful:', loginData);
      navigate('/'); // Redirect to home page on success
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = translate('Login failed. Please check your credentials and try again.');

      if (error.response?.data?.message) {
        const serverMessage = error.response.data.message;

        // Handle specific error messages
        if (serverMessage.includes('Invalid credentials')) {
          setFormErrors({
            email: translate('Invalid email or password'),
            password: translate('Invalid email or password')
          });
          return; // Don't show general error, show field-specific errors
        } else if (serverMessage.includes('reCAPTCHA')) {
          errorMessage = translate('Security verification failed. Please try again.');
        } else if (serverMessage.includes('banned')) {
          errorMessage = translate('Your account has been banned. Please contact support.');
        } else if (serverMessage.includes('lockdown')) {
          errorMessage = translate('System is in lockdown mode. Only administrators can access.');
        } else if (serverMessage.includes('All fields are required')) {
          errorMessage = translate('Email and password are required');
        } else {
          errorMessage = translate(serverMessage);
        }
      } else if (error.response?.status === 400) {
        setFormErrors({
          email: translate('Invalid email or password'),
          password: translate('Invalid email or password')
        });
        return;
      } else if (error.response?.status === 500) {
        errorMessage = translate('Server error. Please try again later.');
      }

      setLoginError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Add direct login fallback function
  const directLoginFallback = async (userData) => {
    try {
      console.log('Attempting direct API login...');
      const response = await api.direct.post('/api/auth/login', userData);

      // Save token to localStorage
      localStorage.setItem('token', response.data.token);

      // Return the data
      return response.data;
    } catch (error) {
      console.error('Direct login failed:', error);
      throw error;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 4 }}>
          <Typography variant="h4" component="h1" align="center" gutterBottom>
            {translate('Login')}
          </Typography>

          {loginError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {loginError}
            </Alert>
          )}

          {verificationMessage && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {verificationMessage}
            </Alert>
          )}

          {securityStatus.requiresRecaptcha && (
            <Alert severity="info" sx={{ mb: 3 }}>
              {translate('Enhanced security is enabled. Additional verification required.')}
            </Alert>
          )}





          {recaptchaError && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              {translate('Security verification unavailable. Please try again later.')}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} noValidate>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label={translate('Email Address')}
              name="email"
              autoComplete="email"
              autoFocus
              value={formData.email}
              onChange={handleChange}
              error={!!formErrors.email}
              helperText={formErrors.email}
              disabled={loading}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label={translate('Password')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password}
              disabled={loading}
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.primary',
                },
                '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.6) !important',
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: 'rgba(0, 0, 0, 0.8) !important',
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : translate('Sign In')}
            </Button>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2">
                {translate('Don\'t have an account?')}{' '}
                <Link component={RouterLink} to="/register" variant="body2">
                  {translate('Sign Up')}
                </Link>
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {translate('OR')}
              </Typography>
            </Divider>

            <GoogleAuth />
          </Box>
        </Paper>
      </Container>
    </motion.div>
  );
};

export default Login;