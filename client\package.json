{"name": "book-leasing-client", "version": "0.2.0", "private": true, "proxy": "http://localhost:8080", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@ericblade/quagga2": "^1.8.4", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/x-data-grid": "^7.28.2", "ajv": "^8.17.1", "axios": "^1.8.4", "chart.js": "^4.4.8", "dynamsoft-camera-enhancer": "^4.1.1", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase": "^11.5.0", "framer-motion": "^12.6.2", "moment": "^2.30.1", "quagga": "^0.12.1", "react": "^19.1.0", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-modal": "^3.16.3", "react-router-dom": "^7.4.1", "react-scripts": "^5.0.1", "react-toastify": "^11.0.5", "react-zxing": "^2.1.0", "tesseract.js": "^6.0.0"}, "scripts": {"start": "set PORT=80 && react-scripts start", "start-unix": "cross-env PORT=80 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:prod": "cross-env REACT_APP_API_URL=http://your-production-api-url.com react-scripts build", "serve": "node server.js", "analyze": "source-map-explorer 'build/static/js/*.js'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "A responsive book leasing application with beautiful UI and mobile-optimized features", "author": "Book Leasing Team", "license": "MIT", "devDependencies": {"http-proxy-middleware": "^3.0.3", "source-map-explorer": "^2.5.3"}}