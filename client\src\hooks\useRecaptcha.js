import { useState, useEffect, useCallback } from 'react';
import api from '../utils/api';

// reCAPTCHA v3 hook for handling reCAPTCHA integration
const useRecaptcha = (enabled = true) => {
  const [recaptchaLoaded, setRecaptchaLoaded] = useState(false);
  const [siteKey, setSiteKey] = useState(null);
  const [error, setError] = useState(null);

  // Load reCAPTCHA site key from server only if enabled
  useEffect(() => {
    if (!enabled) {
      setRecaptchaLoaded(false);
      setSiteKey(null);
      setError(null);
      return;
    }

    const loadSiteKey = async () => {
      try {
        const response = await api.direct.get('/api/security/recaptcha-key');
        setSiteKey(response.data.siteKey);
      } catch (error) {
        console.error('Failed to load reCAPTCHA site key:', error);
        setError('Failed to load reCAPTCHA configuration');
      }
    };

    loadSiteKey();
  }, [enabled]);

  // Load reCAPTCHA script only if enabled and we have a site key
  useEffect(() => {
    if (!enabled || !siteKey) return;

    const loadRecaptchaScript = () => {
      // Check if reCAPTCHA is already loaded
      if (window.grecaptcha) {
        setRecaptchaLoaded(true);
        return;
      }

      // Check if script is already in DOM
      if (document.querySelector('script[src*="recaptcha"]')) {
        // Script exists, wait for it to load
        const checkLoaded = setInterval(() => {
          if (window.grecaptcha) {
            setRecaptchaLoaded(true);
            clearInterval(checkLoaded);
          }
        }, 100);
        return;
      }

      // Create and load the script
      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
      script.async = true;
      script.defer = true;

      script.onload = () => {
        // Wait for grecaptcha to be available
        const checkReady = setInterval(() => {
          if (window.grecaptcha && window.grecaptcha.ready) {
            window.grecaptcha.ready(() => {
              setRecaptchaLoaded(true);
              clearInterval(checkReady);
            });
          }
        }, 100);
      };

      script.onerror = () => {
        setError('Failed to load reCAPTCHA script');
      };

      document.head.appendChild(script);
    };

    loadRecaptchaScript();
  }, [enabled, siteKey]);

  // Execute reCAPTCHA and get token
  const executeRecaptcha = useCallback(async (action = 'submit') => {
    if (!enabled) {
      console.log('reCAPTCHA disabled, skipping');
      return null;
    }

    if (!recaptchaLoaded || !window.grecaptcha || !siteKey) {
      console.warn('reCAPTCHA not loaded yet');
      return null;
    }

    try {
      const token = await window.grecaptcha.execute(siteKey, { action });
      console.log(`reCAPTCHA token generated for action: ${action}`);
      return token;
    } catch (error) {
      console.error('reCAPTCHA execution failed:', error);
      setError('reCAPTCHA verification failed');
      return null;
    }
  }, [enabled, recaptchaLoaded, siteKey]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    recaptchaLoaded,
    siteKey,
    error,
    executeRecaptcha,
    resetError,
    isReady: enabled ? (recaptchaLoaded && siteKey && !error) : true, // Always ready if disabled
    enabled
  };
};

export default useRecaptcha;
