const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { isAdmin } = require('../middleware/admin');
const rateLimiter = require('express-rate-limit');
const jwt = require('jsonwebtoken');

// Global security state
let securityMode = 'normal'; // normal, mild, high

// 24-hour data storage with timestamps
const hourlyData = {
  loginAttempts: new Array(24).fill(0),
  failedLogins: new Array(24).fill(0),
  apiRequests: new Array(24).fill(0),
  lastReset: new Date().setHours(0, 0, 0, 0) // Reset at midnight
};

// Security stats manager to avoid circular dependencies
class SecurityStatsManager {
  constructor() {
    this.stats = {
      suspiciousIPs: 0,
      rateLimitExceeds: 0,
      invalidTokens: 0,
      spamAttempts: 0,
      loginAttempts: [], // Will be populated from hourlyData
      failedLogins: [],  // Will be populated from hourlyData
      apiRequests: [],   // Will be populated from hourlyData
      timeLabels: [],    // Labels for the above arrays
      securityScore: 100, // Start at 100, decrease with incidents
      currentMode: 'normal',
      totalRequestsToday: 0,
      totalLoginsToday: 0,
      totalFailedLoginsToday: 0,
      lastUpdated: new Date().toISOString()
    };
  }

  getStats() {
    // Always return fresh stats with current timestamp
    return {
      ...this.stats,
      lastUpdated: new Date().toISOString()
    };
  }

  updateStats(updates) {
    this.stats = {
      ...this.stats,
      ...updates,
      lastUpdated: new Date().toISOString()
    };
  }

  incrementCounter(counter, amount = 1) {
    if (this.stats.hasOwnProperty(counter)) {
      this.stats[counter] += amount;
      this.stats.lastUpdated = new Date().toISOString();
    }
  }

  reset() {
    this.stats = {
      suspiciousIPs: 0,
      rateLimitExceeds: 0,
      invalidTokens: 0,
      spamAttempts: 0,
      loginAttempts: [],
      failedLogins: [],
      apiRequests: [],
      timeLabels: [],
      securityScore: 100,
      currentMode: securityMode,
      totalRequestsToday: 0,
      totalLoginsToday: 0,
      totalFailedLoginsToday: 0,
      lastUpdated: new Date().toISOString()
    };
  }
}

const securityStatsManager = new SecurityStatsManager();

// Initialize security monitoring
let suspiciousIPs = new Set();

// In-memory rate limiting for demo purposes
const ipRequests = new Map();
const resetInterval = 60 * 1000; // 1 minute

// Get the JWT_SECRET from the same place auth.js uses it
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// Helper to get the current date/time label
const getCurrentTimeLabel = () => {
  const now = new Date();
  return `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
};

// Check if we need to reset daily data
const checkDailyReset = () => {
  const now = new Date();
  const todayMidnight = new Date().setHours(0, 0, 0, 0);

  if (todayMidnight > hourlyData.lastReset) {
    // Reset all hourly data
    hourlyData.loginAttempts = new Array(24).fill(0);
    hourlyData.failedLogins = new Array(24).fill(0);
    hourlyData.apiRequests = new Array(24).fill(0);
    hourlyData.lastReset = todayMidnight;

    console.log('Daily security data reset at midnight');
  }
};

// Update security stats with current hourly data
const updateSecurityStats = () => {
  checkDailyReset();

  // Generate time labels for the last 24 hours
  const now = new Date();
  const currentHour = now.getHours();
  const timeLabels = [];

  // Create labels starting from current hour going back 24 hours
  for (let i = 0; i < 24; i++) {
    const hour = (currentHour - i + 24) % 24;
    timeLabels.unshift(`${String(hour).padStart(2, '0')}:00`);
  }

  // Reorder data to match the time labels (current hour last)
  const reorderedLoginAttempts = [];
  const reorderedFailedLogins = [];
  const reorderedApiRequests = [];

  for (let i = 0; i < 24; i++) {
    const hour = (currentHour - i + 24) % 24;
    reorderedLoginAttempts.unshift(hourlyData.loginAttempts[hour]);
    reorderedFailedLogins.unshift(hourlyData.failedLogins[hour]);
    reorderedApiRequests.unshift(hourlyData.apiRequests[hour]);
  }

  // Calculate totals for today
  const totalRequestsToday = hourlyData.apiRequests.reduce((sum, count) => sum + count, 0);
  const totalLoginsToday = hourlyData.loginAttempts.reduce((sum, count) => sum + count, 0);
  const totalFailedLoginsToday = hourlyData.failedLogins.reduce((sum, count) => sum + count, 0);

  // Update security score based on incidents
  const currentStats = securityStatsManager.getStats();
  const failureRate = totalLoginsToday > 0 ? (totalFailedLoginsToday / totalLoginsToday) * 100 : 0;

  const securityScore = Math.max(0, 100 - Math.floor(
    failureRate +
    (currentStats.rateLimitExceeds * 2) +
    (currentStats.suspiciousIPs * 5) +
    (currentStats.invalidTokens * 1)
  ));

  // Update all stats in the manager
  securityStatsManager.updateStats({
    timeLabels,
    loginAttempts: reorderedLoginAttempts,
    failedLogins: reorderedFailedLogins,
    apiRequests: reorderedApiRequests,
    totalRequestsToday,
    totalLoginsToday,
    totalFailedLoginsToday,
    securityScore,
    currentMode: securityMode,
    suspiciousIPs: suspiciousIPs.size
  });

  console.log(`Security stats updated: ${totalRequestsToday} requests, ${totalLoginsToday} logins, ${totalFailedLoginsToday} failed logins, score: ${securityScore}`);
};

// Initialize with real data structures
const initRealData = () => {
  // Update security stats with current data
  updateSecurityStats();

  // Reset counters to zero for fresh start
  securityStatsManager.updateStats({
    rateLimitExceeds: 0,
    invalidTokens: 0,
    spamAttempts: 0,
    currentMode: securityMode
  });
};

// Function to track login attempts and failures
const trackLoginAttempt = (success = true) => {
  const currentHour = new Date().getHours();
  checkDailyReset(); // Ensure we don't have stale data

  // Track login attempt
  hourlyData.loginAttempts[currentHour]++;

  // Track failed login if not successful
  if (!success) {
    hourlyData.failedLogins[currentHour]++;
  }

  console.log(`Login attempt tracked: ${success ? 'success' : 'failed'} at hour ${currentHour}`);
};

// Initialize with real data structures
initRealData();

// Set up periodic updates every 5 minutes to refresh the security stats
setInterval(() => {
  updateSecurityStats();
}, 5 * 60 * 1000); // 5 minutes

// Middleware to track requests by IP
const trackIPRequests = (req, res, next) => {
  const ip = req.ip;

  // Track API requests for analytics
  if (req.path.startsWith('/api/')) {
    const currentHour = new Date().getHours();
    checkDailyReset(); // Ensure we don't have stale data
    hourlyData.apiRequests[currentHour]++;
  }

  if (!ipRequests.has(ip)) {
    ipRequests.set(ip, { count: 0, timestamp: Date.now() });
  }

  const data = ipRequests.get(ip);

  // Reset if more than the reset interval has passed
  if (Date.now() - data.timestamp > resetInterval) {
    data.count = 0;
    data.timestamp = Date.now();
  }

  data.count++;

  // Check for suspicious activity
  if (data.count > 100) { // 100 requests per minute is suspicious
    if (!suspiciousIPs.has(ip)) { // Only add and increment if new
        suspiciousIPs.add(ip);
        securityStatsManager.updateStats({ suspiciousIPs: suspiciousIPs.size });
    }
    securityStatsManager.incrementCounter('rateLimitExceeds');

    // Log attempt
    console.log(`Rate limit exceeded for IP: ${ip}`);

    // In high security mode, block immediately unless it's an admin
    if (securityMode === 'high') {
      // Always allow these critical routes regardless of security mode
      const alwaysAllowedRoutes = [
        '/api/auth/login',
        '/api/auth/google',
        '/api/auth/refresh-token',
        '/api/auth/me',
        '/api/auth/logout',
        '/api/security/status',
        '/api/security/recaptcha-key',
        '/api/security/mode',
        '/api/security/stats',
        '/api/security/reset-stats',
        '/api/security/users',
        '/api/security/simulate-incident',
        '/api/security/emergency-reset'
      ];

      // Check both originalUrl and path to handle different routing scenarios
      const requestPath = req.originalUrl || req.path;

      // Check if this is an always allowed route
      const isAlwaysAllowed = alwaysAllowedRoutes.some(route =>
        requestPath.startsWith(route) || req.path.startsWith(route)
      );

      // Also allow admin routes and static files
      if (isAlwaysAllowed ||
          requestPath.includes('/admin') ||
          requestPath.startsWith('/static') ||
          requestPath.startsWith('/favicon') ||
          requestPath.endsWith('.js') ||
          requestPath.endsWith('.css') ||
          requestPath.endsWith('.html') ||
          requestPath.endsWith('.ico') ||
          requestPath.endsWith('.png') ||
          requestPath.endsWith('.jpg') ||
          requestPath.endsWith('.svg')) {
        return next();
      }

      // Check if the request is from an admin
      let isAdmin = false;

      // Check Authorization header (Bearer token)
      if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        try {
          const token = req.headers.authorization.split(' ')[1];
          const decoded = jwt.verify(token, JWT_SECRET);

          if (decoded && decoded.role === 'admin') {
            isAdmin = true;
          }
        } catch (error) {
          securityStatsManager.incrementCounter('invalidTokens');
          // Token verification failed
        }
      }

      // Check x-auth-token header
      if (!isAdmin && req.headers['x-auth-token']) {
        try {
          const token = req.headers['x-auth-token'];
          const decoded = jwt.verify(token, JWT_SECRET);

          if (decoded && decoded.role === 'admin') {
            isAdmin = true;
          }
        } catch (error) {
          securityStatsManager.incrementCounter('invalidTokens');
          // Token verification failed
        }
      }

      // If not an admin, block the request
      if (!isAdmin) {
        console.log(`Rate limit blocking non-admin access in high security mode for path: ${req.path}`);
        return res.status(429).json({
          message: 'System is currently in lockdown mode. Please contact administrator.',
          securityMode: 'high',
          path: req.path
        });
      }
    }
  }

  next();
};

// Security mode middleware
const securityModeCheck = (req, res, next) => {
  console.log(`[securityModeCheck] Checking path: ${req.path}, originalUrl: ${req.originalUrl}`);

  // Always allow these critical routes regardless of security mode
  const alwaysAllowedRoutes = [
    '/api/auth/login',
    '/api/auth/google',
    '/api/auth/refresh-token',
    '/api/auth/me',
    '/api/auth/logout',
    '/api/security/status',
    '/api/security/recaptcha-key',
    '/api/security/mode',
    '/api/security/stats',
    '/api/security/reset-stats',
    '/api/security/users',
    '/api/security/simulate-incident',
    '/api/security/emergency-reset'
  ];

  // Check both originalUrl and path to handle different routing scenarios
  const requestPath = req.originalUrl || req.path;

  // Check if this is an always allowed route
  const isAlwaysAllowed = alwaysAllowedRoutes.some(route =>
    requestPath.startsWith(route) || req.path.startsWith(route)
  );

  // Also allow admin routes and static files
  if (isAlwaysAllowed ||
      requestPath.includes('/admin') ||
      requestPath.startsWith('/static') ||
      requestPath.startsWith('/favicon') ||
      requestPath.endsWith('.js') ||
      requestPath.endsWith('.css') ||
      requestPath.endsWith('.html') ||
      requestPath.endsWith('.ico') ||
      requestPath.endsWith('.png') ||
      requestPath.endsWith('.jpg') ||
      requestPath.endsWith('.svg')) {
    console.log(`[securityModeCheck] Allowing route: ${requestPath}`);
    return next();
  }

  // Only apply lockdown for high security mode
  if (securityMode === 'high') {
    console.log(`High security check for path: ${req.path}`);

    // Check if the user is an admin based on their token
    let isAdmin = false;

    // Check Authorization header (Bearer token)
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const decoded = jwt.verify(token, JWT_SECRET);

        if (decoded && decoded.role === 'admin') {
          isAdmin = true;
          console.log(`Admin access granted via Bearer token for path: ${req.path}`);
        }
      } catch (error) {
        securityStatsManager.incrementCounter('invalidTokens');
        console.log('Token verification failed during security check (Bearer token)');
      }
    }

    // Also check x-auth-token header
    if (!isAdmin && req.headers['x-auth-token']) {
      try {
        const token = req.headers['x-auth-token'];
        const decoded = jwt.verify(token, JWT_SECRET);

        if (decoded && decoded.role === 'admin') {
          isAdmin = true;
          console.log(`Admin access granted via x-auth-token for path: ${req.path}`);
        }
      } catch (error) {
        securityStatsManager.incrementCounter('invalidTokens');
        console.log('Token verification failed during security check (x-auth-token)');
      }
    }

    // If not an admin, block access in high security mode
    if (!isAdmin) {
      console.log(`Access denied in high security mode for path: ${req.path}`);
      return res.status(503).json({
        message: 'System is currently in lockdown mode. Only administrators can access the system.',
        securityMode: 'high',
        path: req.path
      });
    }
  }

  next();
};

// Export security status
const getSecurityStatus = () => {
  // Make sure securityMode is always a valid, lowercase value
  const validatedMode = ['normal', 'mild', 'high'].includes(securityMode.toLowerCase())
    ? securityMode.toLowerCase()
    : 'normal';

  return {
    securityMode: validatedMode,
    currentMode: validatedMode, // Add for backward compatibility
    requiresCaptcha: validatedMode === 'mild' || validatedMode === 'high',
    lockdown: validatedMode === 'high'
  };
};

// Get security statistics (admin only)
router.get('/stats', authenticateToken, isAdmin, (req, res) => {
  // Update stats with latest data before returning
  updateSecurityStats();
  res.json(securityStatsManager.getStats());
});

// Set security mode (admin only)
router.post('/mode', authenticateToken, isAdmin, (req, res) => {
  const { mode } = req.body;

  // Normalize and validate security mode
  const normalizedMode = mode && typeof mode === 'string' ? mode.toLowerCase().trim() : '';

  if (!normalizedMode || !['normal', 'mild', 'high'].includes(normalizedMode)) {
    return res.status(400).json({ message: 'Invalid security mode. Valid values are: normal, mild, high' });
  }

  console.log(`Security mode change requested from ${securityMode} to ${normalizedMode}`);

  // Set the new mode
  securityMode = normalizedMode;
  securityStatsManager.updateStats({ currentMode: normalizedMode });

  // Clear cache to ensure immediate status update
  console.log('[SECURITY STATUS] Clearing cache due to mode change');
  cachedSecurityStatus = null;
  cacheTimestamp = 0;

  // Create a force logout flag to tell clients to logout non-admins if in high mode
  const forceLogout = normalizedMode === 'high';

  // Removed mock data updates here
  // securityStats.securityScore = Math.floor(Math.random() * 100);
  // securityStats.suspiciousIPs = Math.floor(Math.random() * 10);
  // securityStats.rateLimitExceeds = Math.floor(Math.random() * 20);
  // securityStats.invalidTokens = Math.floor(Math.random() * 15);
  // securityStats.spamAttempts = Math.floor(Math.random() * 30);

  console.log(`Security mode set to ${securityMode}. Force logout flag: ${forceLogout}`);

  res.json({
    message: `Security mode set to ${securityMode}`,
    currentMode: securityMode,
    forceLogout: forceLogout
  });
});

// Mock endpoint to simulate security incidents (admin only)
router.post('/simulate-incident', authenticateToken, isAdmin, (req, res) => {
  const { type, count = 1 } = req.body;

  if (!type || !['suspicious', 'rateLimit', 'invalidToken', 'spam'].includes(type)) {
    return res.status(400).json({ message: 'Invalid incident type' });
  }

  switch (type) {
    case 'suspicious':
      securityStatsManager.incrementCounter('suspiciousIPs', count);
      break;
    case 'rateLimit':
      securityStatsManager.incrementCounter('rateLimitExceeds', count);
      break;
    case 'invalidToken':
      securityStatsManager.incrementCounter('invalidTokens', count);
      break;
    case 'spam':
      securityStatsManager.incrementCounter('spamAttempts', count);
      break;
  }

  res.json({ message: `Simulated ${count} ${type} incidents`, securityStats: securityStatsManager.getStats() });
});

// Reset security statistics (admin only)
router.post('/reset-stats', authenticateToken, isAdmin, (req, res) => {
  suspiciousIPs.clear();
  securityStatsManager.reset();
  // Re-initialize with real data structures (zeros out counters)
  initRealData();

  res.json({ message: 'Security statistics reset' });
});

// Get reCAPTCHA site key (public endpoint)
router.get('/recaptcha-key', (req, res) => {
  console.log('reCAPTCHA key endpoint called');
  const config = require('../config');
  console.log('Returning site key:', config.security.recaptcha.siteKey);
  res.json({
    siteKey: config.security.recaptcha.siteKey
  });
});

// Emergency security reset endpoint (no authentication required)
// This is a safety mechanism to prevent complete lockout
router.post('/emergency-reset', (req, res) => {
  const { emergencyKey } = req.body;

  // Use a simple emergency key for safety (in production, this should be more secure)
  const EMERGENCY_KEY = process.env.EMERGENCY_RESET_KEY || 'emergency-reset-2024';

  if (emergencyKey !== EMERGENCY_KEY) {
    return res.status(401).json({ message: 'Invalid emergency key' });
  }

  console.log('EMERGENCY SECURITY RESET TRIGGERED');

  // Reset security mode to normal
  securityMode = 'normal';
  securityStatsManager.updateStats({ currentMode: 'normal' });

  // Clear cache to ensure immediate status update
  cachedSecurityStatus = null;
  cacheTimestamp = 0;

  // Clear suspicious IPs
  suspiciousIPs.clear();

  // Reset all security stats
  securityStatsManager.reset();
  initRealData();

  console.log('Security mode reset to normal via emergency endpoint');

  res.json({
    message: 'Emergency reset completed. Security mode set to normal.',
    currentMode: 'normal',
    timestamp: new Date().toISOString()
  });
});

// Get current security status (public endpoint with caching)
let cachedSecurityStatus = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 120000; // 2 minutes

// Request counter to detect and prevent DDoS
const requestCounts = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX = 30; // Max 30 requests per minute per IP (more forgiving)

// Cleanup timer for the request counts map
setInterval(() => {
  const now = Date.now();
  for (const [ip, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(ip);
    }
  }
}, 300000); // Clean up every 5 minutes

router.get('/status', (req, res) => {
  console.log('[SECURITY STATUS] Endpoint called');
  console.log('[SECURITY STATUS] Current security mode:', securityMode);
  // Apply rate limiting
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();

  // Initialize or get existing request count
  if (!requestCounts.has(clientIP)) {
    requestCounts.set(clientIP, {
      count: 0,
      resetTime: now + RATE_LIMIT_WINDOW
    });
  }

  const requestData = requestCounts.get(clientIP);

  // Reset counter if window has expired
  if (now > requestData.resetTime) {
    requestData.count = 0;
    requestData.resetTime = now + RATE_LIMIT_WINDOW;
  }

  // Increment count
  requestData.count++;

  // Check if rate limit exceeded
  if (requestData.count > RATE_LIMIT_MAX) {
    console.log(`Rate limit exceeded for IP: ${clientIP}`);
    // Set retry-after header
    const retryAfter = Math.ceil((requestData.resetTime - now) / 1000);
    res.set('Retry-After', retryAfter.toString());
    return res.status(429).json({
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: retryAfter
    });
  }

  // Use cached result if available and not expired
  if (cachedSecurityStatus && now - cacheTimestamp < CACHE_DURATION) {
    console.log('[SECURITY STATUS] Returning cached status:', cachedSecurityStatus);
    return res.json(cachedSecurityStatus);
  }

  // Generate new status
  const currentStats = securityStatsManager.getStats();
  const status = {
    mode: securityMode,
    score: currentStats.securityScore,
    time: new Date().toISOString()
  };

  // Cache the result
  cachedSecurityStatus = status;
  cacheTimestamp = now;

  console.log('[SECURITY STATUS] Returning status:', status);
  res.json(status);
});

// Get all users (admin only)
router.get('/users', authenticateToken, isAdmin, (req, res) => {
  const db = req.app.locals.db; // Ensure this line is present and correct

  db.all('SELECT id, username, email, role, is_banned, ban_reason FROM users', [], (err, rows) => {
    if (err) {
      console.error('Error fetching users:', err.message);
      return res.status(500).json({ message: 'Error fetching users' });
    }
    res.json(rows);
  });
});

// Ban a user (admin only)
router.post('/users/:id/ban', authenticateToken, isAdmin, (req, res) => {
  const db = req.app.locals.db; // Ensure this line is present and correct
  const userIdToBan = req.params.id;
  const { reason } = req.body;

  // Prevent admin from banning themselves
  if (req.user.id === parseInt(userIdToBan, 10)) {
    return res.status(400).json({ message: 'Administrators cannot ban themselves.' });
  }

  db.run(
    'UPDATE users SET is_banned = 1, ban_reason = ? WHERE id = ?',
    [reason || 'No reason provided', userIdToBan],
    function (err) {
      if (err) {
        console.error('Error banning user:', err.message);
        return res.status(500).json({ message: 'Error banning user' });
      }
      if (this.changes === 0) {
        return res.status(404).json({ message: 'User not found' });
      }
      console.log(`User ID ${userIdToBan} banned successfully`);
      res.json({ message: 'User banned successfully' });
    }
  );
});

// Unban a user (admin only)
router.post('/users/:id/unban', authenticateToken, isAdmin, (req, res) => {
  const db = req.app.locals.db; // Ensure this line is present and correct
  const userIdToUnban = req.params.id;

  db.run(
    'UPDATE users SET is_banned = 0, ban_reason = NULL WHERE id = ?',
    [userIdToUnban],
    function (err) {
      if (err) {
        console.error('Error unbanning user:', err.message);
        return res.status(500).json({ message: 'Error unbanning user' });
      }
      if (this.changes === 0) {
        return res.status(404).json({ message: 'User not found or already unbanned' });
      }
      console.log(`User ID ${userIdToUnban} unbanned successfully`);
      res.json({ message: 'User unbanned successfully' });
    }
  );
});

module.exports = {
  router,
  trackIPRequests,
  securityModeCheck,
  getSecurityStatus,
  trackLoginAttempt,
  getSecurityStats: () => securityStatsManager.getStats(),
  hourlyData
};
