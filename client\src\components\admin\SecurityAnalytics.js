import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Alert,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip
} from '@mui/material';
import {
  SecurityOutlined,
  ErrorOutline,
  WarningAmber,
  ShieldOutlined,
  RefreshOutlined as RefreshIcon,
  LockOutlined
} from '@mui/icons-material';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, BarElement } from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { useLanguage } from '../../context/LanguageContext';
import api from '../../utils/api';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

const SecurityAnalytics = () => {
  const { translate } = useLanguage();
  const [securityData, setSecurityData] = useState(null);
  const [securityMode, setSecurityMode] = useState('normal');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedMode, setSelectedMode] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch security data
  const fetchSecurityData = useCallback(async () => {
    setLoading(true);
    setError('');
    try {
      const response = await api.direct.get('/api/security/stats');

      if (response.data) {
        setSecurityData(response.data);
        // Only update security mode if it's a valid value
        if (response.data.currentMode) {
          setSecurityMode(response.data.currentMode);
        } else if (response.data.mode) {
          setSecurityMode(response.data.mode);
        }
        console.log('Security data loaded:', response.data);
      }
    } catch (err) {
      console.error('Error fetching security data:', err);
      // If unable to fetch, set default data to prevent UI errors
      setSecurityData({
        totalRequests: 0,
        suspiciousIPs: 0,
        blockedIPs: 0,
        mode: 'normal',
        recentIncidents: []
      });
    } finally {
      setLoading(false);
    }
  }, [refreshTrigger]);

  // Rate-limit the refresh
  useEffect(() => {
    fetchSecurityData();

    // Set up refresh on a slower interval (every 30 seconds)
    const interval = setInterval(fetchSecurityData, 30000);

    return () => clearInterval(interval);
  }, [fetchSecurityData]);

  // Handle security mode change
  const handleSecurityModeClick = (mode) => {
    setSelectedMode(mode);
    setConfirmDialogOpen(true);
  };

  // Confirm security mode change
  const handleConfirmModeChange = async () => {
    setLoading(true);
    setError('');
    try {
      console.log(`Attempting to change security mode to: ${selectedMode}`);
      const response = await api.direct.post('/api/security/mode', { mode: selectedMode });

      console.log('Security mode change response:', response.data);

      // Update the security mode from the response if possible
      if (response.data.mode) {
        setSecurityMode(response.data.mode);
      } else if (response.data.currentMode) {
        setSecurityMode(response.data.currentMode);
      } else {
        setSecurityMode(selectedMode);
      }

      setRefreshTrigger(prev => prev + 1);
    } catch (err) {
      console.error('Error changing security mode:', err);
      setError(err.response?.data?.message || translate('Failed to change security mode'));
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Render loading state
  if (loading && !securityData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Generate chart data
  const generateChartData = () => {
    if (!securityData) return null;

    const activityData = {
      labels: securityData.timeLabels || [],
      datasets: [
        {
          label: translate('Login Attempts'),
          data: securityData.loginAttempts || [],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          tension: 0.4,
          fill: false,
          pointRadius: 3,
          pointHoverRadius: 6
        },
        {
          label: translate('Failed Logins'),
          data: securityData.failedLogins || [],
          borderColor: '#F44336',
          backgroundColor: 'rgba(244, 67, 54, 0.1)',
          tension: 0.4,
          fill: false,
          pointRadius: 3,
          pointHoverRadius: 6
        },
        {
          label: translate('API Requests'),
          data: securityData.apiRequests || [],
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          tension: 0.4,
          fill: false,
          pointRadius: 3,
          pointHoverRadius: 6,
          yAxisID: 'y1'
        }
      ]
    };

    return activityData;
  };

  const generateThreatData = () => {
    if (!securityData) return null;

    const threatData = {
      labels: [
        translate('Suspicious IPs'),
        translate('Rate Limit Exceeds'),
        translate('Invalid Tokens'),
        translate('Spam Attempts')
      ],
      datasets: [
        {
          label: translate('Threat Incidents'),
          data: [
            securityData.suspiciousIPs || 0,
            securityData.rateLimitExceeds || 0,
            securityData.invalidTokens || 0,
            securityData.spamAttempts || 0
          ],
          backgroundColor: [
            'rgba(255, 152, 0, 0.6)',
            'rgba(233, 30, 99, 0.6)',
            'rgba(156, 39, 176, 0.6)',
            'rgba(121, 85, 72, 0.6)'
          ],
          borderColor: [
            'rgba(255, 152, 0, 1)',
            'rgba(233, 30, 99, 1)',
            'rgba(156, 39, 176, 1)',
            'rgba(121, 85, 72, 1)'
          ]
        }
      ]
    };

    return threatData;
  };

  const getSecurityModeColor = (mode) => {
    switch (mode) {
      case 'normal': return '#4CAF50'; // Green
      case 'mild': return '#FF9800'; // Orange
      case 'high': return '#F44336'; // Red
      default: return '#9E9E9E'; // Grey
    }
  };

  const getSecurityModeLabel = (mode) => {
    switch (mode && mode.toLowerCase()) {
      case 'normal': return translate('Normal');
      case 'mild': return translate('Mild Security (reCAPTCHA)');
      case 'high': return translate('High Security (Lockdown)');
      default: return translate('Unknown');
    }
  };

  return (
    <Box>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2" sx={{ display: 'flex', alignItems: 'center' }}>
          <SecurityOutlined sx={{ mr: 1 }} />
          {translate('Security Analytics')}
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          variant="outlined"
          onClick={handleRefresh}
          disabled={loading}
        >
          {translate('Refresh')}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Security Mode Card */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2, border: `1px solid ${getSecurityModeColor(securityMode)}` }}>
            <Typography variant="h6" gutterBottom>
              {translate('Current Security Mode')}:
              <Chip
                label={getSecurityModeLabel(securityMode)}
                sx={{
                  ml: 1,
                  color: 'white',
                  bgcolor: getSecurityModeColor(securityMode),
                  fontWeight: 'bold'
                }}
              />
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              {translate('Configure the system\'s security level based on threat detection.')}
              {securityMode === 'mild' && (
                <Alert severity="info" sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    {translate('reCAPTCHA is configured with a test key (6LeIxAcTAAAAA...). For production use, replace with your own keys in server/config.js or set environment variables.')}
                  </Typography>
                </Alert>
              )}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
              <Button
                variant={securityMode === 'normal' ? 'contained' : 'outlined'}
                color="success"
                onClick={() => handleSecurityModeClick('normal')}
                disabled={securityMode === 'normal' || loading}
              >
                {translate('Normal Mode')}
              </Button>
              <Button
                variant={securityMode === 'mild' ? 'contained' : 'outlined'}
                color="warning"
                onClick={() => handleSecurityModeClick('mild')}
                disabled={securityMode === 'mild' || loading}
              >
                {translate('Mild Security (reCAPTCHA)')}
              </Button>
              <Button
                variant={securityMode === 'high' ? 'contained' : 'outlined'}
                color="error"
                onClick={() => handleSecurityModeClick('high')}
                disabled={securityMode === 'high' || loading}
              >
                {translate('High Security (Lockdown)')}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Security Stats Summary */}
        {securityData && (
          <>
            {/* Threat Statistics */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardHeader
                  title={translate('Threat Analytics')}
                  titleTypographyProps={{ variant: 'h6' }}
                  avatar={<WarningAmber />}
                />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    <Bar
                      data={generateThreatData()}
                      options={{
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            display: false
                          },
                          title: {
                            display: true,
                            text: translate('Security Incidents')
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Activity Graph */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardHeader
                  title={translate('Activity Monitoring')}
                  titleTypographyProps={{ variant: 'h6' }}
                  avatar={<ShieldOutlined />}
                />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    <Line
                      data={generateChartData()}
                      options={{
                        maintainAspectRatio: false,
                        responsive: true,
                        interaction: {
                          mode: 'index',
                          intersect: false,
                        },
                        plugins: {
                          title: {
                            display: true,
                            text: translate('24-Hour Activity Monitor')
                          },
                          legend: {
                            position: 'top',
                          },
                          tooltip: {
                            callbacks: {
                              title: function(context) {
                                return `${translate('Time')}: ${context[0].label}`;
                              },
                              label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y}`;
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            display: true,
                            title: {
                              display: true,
                              text: translate('Time (24h)')
                            },
                            ticks: {
                              maxTicksLimit: 12
                            }
                          },
                          y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                              display: true,
                              text: translate('Login Activity')
                            },
                            beginAtZero: true
                          },
                          y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                              display: true,
                              text: translate('API Requests')
                            },
                            beginAtZero: true,
                            grid: {
                              drawOnChartArea: false,
                            },
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Security Summary */}
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title={translate('Security Status Summary')}
                  titleTypographyProps={{ variant: 'h6' }}
                />
                <Divider />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={2.4}>
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="h4" color="primary">
                          {securityData.totalRequestsToday || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('API Requests Today')}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="h4" color="success.main">
                          {securityData.totalLoginsToday || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('Logins Today')}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="h4" color="error">
                          {securityData.totalFailedLoginsToday || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('Failed Logins Today')}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="h4" color="warning.main">
                          {securityData.suspiciousIPs || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('Suspicious IPs')}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2.4}>
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="h4" color="success.main">
                          {securityData.securityScore || 0}/100
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('Security Score')}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </>
        )}
      </Grid>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>
          {translate('Confirm Security Mode Change')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {selectedMode === 'normal' && (
              translate("You are about to switch to Normal security mode. This mode provides standard security protections without additional verification steps for users.")
            )}
            {selectedMode === 'mild' && (
              translate("You are about to switch to Mild security mode. This will require reCAPTCHA verification for suspicious activities and may slow down some operations.")
            )}
            {selectedMode === 'high' && (
              translate("WARNING: You are about to enable High security (Lockdown) mode. Only administrators will be able to access the system. All regular user access will be blocked until this mode is disabled.")
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleConfirmModeChange}
            color={selectedMode === 'high' ? 'error' : 'primary'}
            autoFocus
          >
            {translate('Confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecurityAnalytics;
