import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { Container, Typography, TextField, Button, Box, Paper, Alert, CircularProgress } from '@mui/material';
import { firebaseInitPromise, verifyPasswordResetCode, confirmPasswordReset, applyActionCode } from '../firebase';
import { useLanguage } from '../context/LanguageContext';

const HandleFirebaseActionPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { translate } = useLanguage();

  const mode = searchParams.get('mode');
  const oobCode = searchParams.get('oobCode'); // Out Of Band code
  const apiKey = searchParams.get('apiKey'); // apiKey is also in the URL

  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [status, setStatus] = useState('loading'); // loading, readyToReset, success, error, info
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [userEmail, setUserEmail] = useState(''); // To display which user's password is being reset
  const [emailForPasswordReset, setEmailForPasswordReset] = useState('');
  const [currentAction, setCurrentAction] = useState('loading');

  useEffect(() => {
    if (!oobCode || !mode) {
      setStatus('error');
      setError(translate('Invalid action link. Code or mode is missing.'));
      return;
    }

    const handleAction = async () => {
      setLoading(true);
      switch (mode) {
        case 'resetPassword':
          try {
            const { auth } = await firebaseInitPromise;
            const email = await verifyPasswordResetCode(auth, oobCode);
            setUserEmail(email);
            setStatus('readyToReset');
            setMessage(translate('Please enter your new password for ') + email);
          } catch (err) {
            console.error('Invalid or expired oobCode for password reset:', err);
            setStatus('error');
            setError(translate('Invalid or expired password reset link. Please request a new one.'));
          }
          break;
        case 'verifyEmail':
          try {
            const { auth } = await firebaseInitPromise;
            await applyActionCode(auth, oobCode);
            // Call backend to update verification status
            try {
              const backendResponse = await fetch('/api/auth/verify-firebase-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ oobCode }),
              });
              const backendData = await backendResponse.json();
              if (!backendResponse.ok) {
                console.error('Backend verification update failed:', backendData.message);
                // Even if backend sync fails, Firebase verification was successful.
                // Set a message that indicates success with Firebase but potential delay in system update.
                setStatus('success'); // Or a custom status like 'warning' or 'partial_success'
                setMessage(translate('Your email address has been successfully verified with our authentication provider! System update may take a moment. You can now login.'));
                // Optionally set an error sub-message or log for support.
              } else {
                console.log('Backend verification update successful:', backendData.message);
                setStatus('success');
                setMessage(translate('Your email address has been successfully verified! You can now login.'));
              }
            } catch (backendError) {
              console.error('Error calling backend to verify email:', backendError);
              setStatus('success'); // As Firebase part was successful
              setMessage(translate('Your email address has been successfully verified with our authentication provider! System update may be delayed. You can now login.'));
            }
          } catch (err) {
            console.error('Error verifying email:', err);
            setStatus('error');
            setError(translate('Failed to verify email. The link may be expired or invalid.'));
          }
          break;
        default:
          setStatus('error');
          setError(translate('Unsupported action mode.'));
      }
      setLoading(false);
    };

    handleAction();
  }, [mode, oobCode, translate]);

  const handlePasswordResetSubmit = async (e) => {
    e.preventDefault();
    if (newPassword !== confirmNewPassword) {
      setError(translate('Passwords do not match.'));
      return;
    }
    if (newPassword.length < 6) {
      setError(translate('Password must be at least 6 characters long.'));
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      const { auth } = await firebaseInitPromise;
      await confirmPasswordReset(auth, oobCode, newPassword);
      setMessage(translate('Password has been reset successfully. You can now log in with your new password.'));
      
      // Now, sync this new password with the backend server
      try {
        console.log('Attempting to sync password with backend for email:', emailForPasswordReset);
        const response = await fetch('/api/auth/sync-firebase-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: emailForPasswordReset, newPassword }),
        });
        const data = await response.json();
        if (!response.ok) {
          throw new Error(data.message || 'Failed to sync password with backend.');
        }
        console.log('Backend password sync successful:', data.message);
        // Optionally provide more feedback to the user about backend sync
      } catch (syncError) {
        console.error('Error syncing password with backend:', syncError);
        setError(translate('Password reset in Firebase, but failed to update on our server. Please contact support if login issues persist.'));
        // Even if backend sync fails, the Firebase reset was successful.
        // The user can still log in via Firebase.
      }
      
      setCurrentAction('resetPassword_success');
      setLoading(false);
      setTimeout(() => navigate('/login'), 5000); // Redirect to login after a delay
    } catch (e) {
      console.error('Error resetting password:', e);
      setStatus('error');
      setError(translate('Failed to reset password. Please try again.'));
    }
  };

  if (loading || status === 'loading') {
    return (
      <Container sx={{textAlign: 'center', mt: 5}}>
        <CircularProgress />
        <Typography>{translate('Processing your request...')}</Typography>
      </Container>
    );
  }
  
  return (
    <Container component="main" maxWidth="xs">
      <Paper elevation={3} sx={{ marginTop: 8, padding: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography component="h1" variant="h5" sx={{ mb: 2 }}>
          {mode === 'resetPassword' ? translate('Reset Your Password') : translate('Verify Your Email')}
        </Typography>

        {error && <Alert severity="error" sx={{ width: '100%', mb: 2 }}>{error}</Alert>}
        {message && (status === 'success' || status === 'info' || status === 'readyToReset') && 
            <Alert severity={status === 'readyToReset' ? 'info' : 'success'} sx={{ width: '100%', mb: 2 }}>{message}</Alert>}

        {status === 'readyToReset' && mode === 'resetPassword' && (
          <Box component="form" onSubmit={handlePasswordResetSubmit} sx={{ mt: 1, width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              name="newPassword"
              label={translate('New Password')}
              type="password"
              id="newPassword"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              disabled={loading}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmNewPassword"
              label={translate('Confirm New Password')}
              type="password"
              id="confirmNewPassword"
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
              disabled={loading}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2, position: 'relative' }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} sx={{position: 'absolute'}} /> : translate('Set New Password')}
            </Button>
          </Box>
        )}

        {(status === 'success' || status === 'error') && (
          <Box textAlign="center" sx={{mt: 2}}>
            <Button component={RouterLink} to="/login" variant="contained">
                {translate('Back to Login')}
            </Button>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default HandleFirebaseActionPage; 