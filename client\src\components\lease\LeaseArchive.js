import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Chip,
  Divider,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  List,
  ListItem
} from '@mui/material';
import { 
  AccessTime as AccessTimeIcon,
  Event as EventIcon,
  AssignmentReturn as ReturnIcon,
  Cancel as CancelIcon,
  FilterAlt as FilterAltIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
  History as HistoryIcon,
  Sort as SortIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import api from '../../utils/api';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../context/LanguageContext';
import { useTheme } from '@mui/material/styles';

const LeaseArchive = () => {
  const [leaseHistory, setLeaseHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const theme = useTheme();

  // Fetch lease history on component mount
  useEffect(() => {
    fetchLeaseHistory();
  }, []);

  // Fetch lease history data
  const fetchLeaseHistory = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await api.direct.get('/api/leases', {
        params: { includeAll: true } // Ensure we get all leases including returned, rejected, etc.
      });
      console.log('Lease history response:', response);
      
      // Ensure we have an array of leases
      const leases = response.data?.leases || response.data || [];
      const leasesArray = Array.isArray(leases) ? leases : [];
      
      console.log('Processed lease history:', leasesArray);
      setLeaseHistory(leasesArray);
    } catch (error) {
      console.error('Error fetching leases:', error);
      setError('Failed to load lease history');
      setLeaseHistory([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Format date string to a readable format
  const formatDate = (dateString) => {
    if (!dateString) return translate('N/A');
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get status chip color based on lease status
  const getStatusColor = (status, approval_status) => {
    if (approval_status === 'rejected') {
      return 'error';
    } else if (approval_status === 'pending') {
      return 'warning';
    }
    
    switch (status) {
      case 'returned':
        return 'success';
      case 'overdue':
        return 'error';
      case 'cancelled':
        return 'default';
      case 'active':
        return 'info';
      default:
        return 'primary';
    }
  };
  
  // Get status display text
  const getStatusText = (status, approval_status) => {
    if (approval_status === 'rejected') {
      return translate('Rejected');
    } else if (approval_status === 'pending') {
      return translate('Pending Approval');
    }
    
    switch (status) {
      case 'returned':
        return translate('Returned');
      case 'overdue':
        return translate('Overdue');
      case 'cancelled':
        return translate('Cancelled');
      case 'active':
        return translate('Active');
      default:
        return translate(status || 'Unknown');
    }
  };

  // Navigate to book details page
  const navigateToBook = (bookId) => {
    navigate(`/books/${bookId}`);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
  };
  
  // Handle search query change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };
  
  // Handle refresh button click
  const handleRefresh = () => {
    setStatusFilter('');
    setSearchQuery('');
    fetchLeaseHistory();
  };

  // Filter and search leases
  const filteredLeases = useMemo(() => {
    return leaseHistory.filter(lease => {
      // Status filter
      if (statusFilter && statusFilter !== '') {
        if (statusFilter === 'rejected' && lease.approval_status !== 'rejected') {
          return false;
        } else if (statusFilter === 'pending' && lease.approval_status !== 'pending') {
          return false;
        } else if (statusFilter !== 'rejected' && statusFilter !== 'pending' && lease.status !== statusFilter) {
          return false;
        }
      }
      
      // Text search
      if (searchQuery && searchQuery.trim() !== '') {
        const query = searchQuery.toLowerCase().trim();
        return (
          (lease.title && lease.title.toLowerCase().includes(query)) ||
          (lease.author && lease.author.toLowerCase().includes(query)) ||
          (lease.isbn && lease.isbn.toLowerCase().includes(query))
        );
      }
      
      return true;
    });
  }, [leaseHistory, statusFilter, searchQuery]);

  // Group leases by month and year for better organization
  const groupLeasesByDate = (history) => {
    const grouped = {};
    
    // Ensure history is an array before using forEach
    if (!Array.isArray(history)) {
      console.error('Expected history to be an array, but got:', history);
      return {};
    }
    
    history.forEach(lease => {
      // Skip if lease or lease_date is undefined
      if (!lease || !lease.lease_date) {
        console.warn('Skipping invalid lease entry:', lease);
        return;
      }
      
      const date = new Date(lease.lease_date);
      const monthYear = `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
      if (!grouped[monthYear]) {
        grouped[monthYear] = [];
      }
      grouped[monthYear].push(lease);
    });
    
    return grouped;
  };

  // Memoize the grouped leases calculation
  const groupedLeases = useMemo(() => {
    console.log("Recalculating grouped leases..."); // Debug log
    return groupLeasesByDate(filteredLeases);
  }, [filteredLeases]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          {translate('Lease History Archive')}
        </Typography>
        
        <Tooltip title={translate('Refresh')}>
          <IconButton onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              placeholder={translate('Search by book title, author, ISBN...')}
              value={searchQuery}
              onChange={handleSearchChange}
              size="small"
              variant="outlined"
              sx={{ mb: { xs: 1, sm: 0 } }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth size="small">
              <InputLabel>{translate('Status')}</InputLabel>
              <Select
                value={statusFilter}
                onChange={handleStatusFilterChange}
                label={translate('Status')}
              >
                <MenuItem value="">{translate('All Statuses')}</MenuItem>
                <MenuItem value="active">{translate('Active')}</MenuItem>
                <MenuItem value="pending">{translate('Pending Approval')}</MenuItem>
                <MenuItem value="rejected">{translate('Rejected')}</MenuItem>
                <MenuItem value="returned">{translate('Returned')}</MenuItem>
                <MenuItem value="overdue">{translate('Overdue')}</MenuItem>
                <MenuItem value="cancelled">{translate('Cancelled')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <CircularProgress />
        </Box>
      ) : filteredLeases.length === 0 ? (
        <Alert severity="info">
          {searchQuery || statusFilter 
            ? translate('No leases match your current filters. Try adjusting your search criteria.')
            : translate('You have no past lease records. Completed, cancelled, or overdue leases will appear here.')}
        </Alert>
      ) : (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {Object.keys(groupedLeases).map(monthYear => (
            <Box key={monthYear} sx={{ mb: 4 }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: 2, 
                  pb: 1, 
                  borderBottom: 1, 
                  borderColor: 'divider'
                }}
              >
                {monthYear}
              </Typography>
              
              <Grid container spacing={3}>
                {groupedLeases[monthYear].map(lease => (
                  <Grid item xs={12} sm={6} md={4} key={lease.id}>
                    <motion.div variants={itemVariants}>
                      <Card 
                        elevation={2} 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          transition: '0.3s',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            boxShadow: 6
                          } 
                        }}
                      >
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="h6" component="h3" noWrap>
                              {lease.title}
                            </Typography>
                            <Chip 
                              label={getStatusText(lease.status, lease.approval_status)} 
                              color={getStatusColor(lease.status, lease.approval_status)}
                              size="small" 
                              sx={{ ml: 1 }}
                            />
                          </Box>
                          
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {lease.author}
                          </Typography>
                          
                          <Divider sx={{ my: 1.5 }} />
                          
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <EventIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
                              {translate('Borrowed')}: {formatDate(lease.lease_date)}
                            </Typography>
                            
                            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: theme.palette.secondary.main }} />
                              {translate('Due Date')}: {formatDate(lease.due_date)}
                            </Typography>
                            
                            {lease.return_date && (
                              <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                <ReturnIcon fontSize="small" sx={{ mr: 1, color: theme.palette.success.main }} />
                                {translate('Returned')}: {formatDate(lease.return_date)}
                              </Typography>
                            )}
                            
                            {lease.status === 'cancelled' && (
                              <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                <CancelIcon fontSize="small" sx={{ mr: 1, color: theme.palette.error.main }} />
                                {translate('Cancelled')}: {formatDate(lease.updated_at)}
                              </Typography>
                            )}
                            
                            {lease.approval_status === 'rejected' && lease.rejection_reason && (
                              <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mt: 1, color: theme.palette.error.main }}>
                                <ClearIcon fontSize="small" sx={{ mr: 1 }} />
                                {translate('Reason')}: {lease.rejection_reason}
                              </Typography>
                            )}
                          </Box>
                        </CardContent>
                        
                        <Box sx={{ p: 2, pt: 0 }}>
                          <Button 
                            variant="outlined" 
                            size="small" 
                            onClick={() => navigateToBook(lease.book_id)}
                            fullWidth
                          >
                            {translate('View Book')}
                          </Button>
                        </Box>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </motion.div>
      )}
    </Paper>
  );
};

export default LeaseArchive;
