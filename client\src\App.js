import React, { useEffect, useContext, Fragment } from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { Box } from '@mui/material';
import axios from 'axios';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { AuthContext } from './context/AuthContext';
import { LanguageProvider } from './context/LanguageContext';
import { SecurityProvider } from './context/SecurityContext';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import useProfileCompletion from './hooks/useProfileCompletion';

// Layout components
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';

// Common components
import LoadingScreen from './components/common/LoadingScreen';
import ProfileCompletionModal from './components/profile/ProfileCompletionModal';

// Page components
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import BookCatalog from './pages/BookCatalog';
import BookDetails from './pages/BookDetails';
import UserProfile from './pages/UserProfile';
import AdminDashboard from './pages/AdminDashboard';
import LeaseHistory from './pages/LeaseHistory';
import NotFound from './pages/NotFound';
import EmailVerification from './pages/EmailVerification';
import AuthPopup from './pages/AuthPopup';
import HandleFirebaseActionPage from './pages/HandleFirebaseActionPage';

// Auth components
import PrivateRoute from './components/auth/PrivateRoute';
import AdminRoute from './components/auth/AdminRoute';
import VerifiedRoute from './components/auth/VerifiedRoute';
import PublicRoute from './components/auth/PublicRoute';

// Set empty baseURL to make relative paths work correctly with proxy
axios.defaults.baseURL = '';

// Component to handle email verification from link - just redirect to main verification page
const VerifyEmailConfirmation = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Just redirect to the main email verification page
    navigate('/verify-email');
  }, [navigate]);

  return null; // Don't render anything since we're redirecting
};

const App = () => {
  const location = useLocation();
  const { loading, user } = useContext(AuthContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Profile completion hook
  const {
    showModal,
    handleProfileComplete,
    handleModalClose
  } = useProfileCompletion();

  useEffect(() => {
    // Setup global axios interceptors
    axios.interceptors.response.use(
      // Any status code that lie within the range of 2xx cause success to be triggered
      response => response,
      // Any status codes that fall outside the range of 2xx cause error to be triggered
      error => {
        const message = error.response?.data?.message || error.message || 'An unexpected error occurred';
        if (error.response?.status === 401) {
          // 401 Unauthorized
          toast.error('Please log in to continue');
        } else if (error.response?.status === 403) {
          // 403 Forbidden
          toast.error('You do not have permission to perform this action');
        } else if (error.response?.status === 500) {
          // 500 Internal Server Error
          toast.error('Server error. Please try again later');
        } else if (error.response?.status === 404) {
          // 404 Not Found - don't show toast for these
        } else {
          // Other errors
          toast.error(message);
        }
        return Promise.reject(error);
      }
    );
  }, []);

  return (
    <LanguageProvider>
      <SecurityProvider>
        {loading ? (
          <LoadingScreen message="Initializing..." />
        ) : (
        <Fragment>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              minHeight: '100vh',
              paddingBottom: isMobile ? '56px' : 0 // Add padding for mobile bottom navigation
            }}
          >
            <Navbar />
            <AnimatePresence mode="wait">
              <Box
                component="main"
                sx={{
                  flexGrow: 1,
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Routes location={location} key={location.pathname}>
                  <Route path="/" element={<Home />} />
                  <Route path="/books" element={
                    <PublicRoute>
                      <BookCatalog />
                    </PublicRoute>
                  } />
                  <Route path="/books/:id" element={
                    <PublicRoute>
                      <BookDetails />
                    </PublicRoute>
                  } />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/auth/action" element={<HandleFirebaseActionPage />} />
                  <Route path="/verify-email" element={
                    <PrivateRoute>
                      <EmailVerification />
                    </PrivateRoute>
                  } />
                  <Route path="/verify-email-confirmation" element={<VerifyEmailConfirmation />} />
                  <Route
                    path="/profile"
                    element={
                      <VerifiedRoute>
                        <UserProfile />
                      </VerifiedRoute>
                    }
                  />
                  <Route
                    path="/lease-history"
                    element={
                      <VerifiedRoute>
                        <LeaseHistory />
                      </VerifiedRoute>
                    }
                  />
                  <Route
                    path="/admin/*"
                    element={
                      <AdminRoute>
                        <AdminDashboard />
                      </AdminRoute>
                    }
                  />
                  <Route path="/auth-popup" element={<AuthPopup />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Box>
            </AnimatePresence>
            <Footer />
            <ToastContainer position="bottom-right" autoClose={5000} theme="dark" />

            {/* Profile Completion Modal */}
            <ProfileCompletionModal
              open={showModal}
              onClose={handleModalClose}
              onComplete={handleProfileComplete}
              user={user}
            />
          </Box>
        </Fragment>
        )}
      </SecurityProvider>
    </LanguageProvider>
  );
};

export default App;