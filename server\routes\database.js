const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// Protected tables and columns that cannot be modified
const PROTECTED_TABLES = ['sqlite_sequence', 'sqlite_master'];
const PROTECTED_COLUMNS = {
  // Removed all protections - admin has full control
};

// Get all tables
router.get('/tables', authenticateToken, requireAdmin, (req, res) => {
  try {
    const db = req.app.locals.db;

    // Get all tables
    db.all(`
      SELECT name, sql
      FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `, (err, tables) => {
      if (err) {
        return res.status(500).json({ message: 'Error fetching tables', error: err.message });
      }

      // Get row count for each table
      const tablePromises = tables.map(table => {
        return new Promise((resolve, reject) => {
          db.get(`SELECT COUNT(*) as count FROM "${table.name}"`, (err, result) => {
            if (err) {
              reject(err);
            } else {
              // Get column count
              db.all(`PRAGMA table_info("${table.name}")`, (err, columns) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({
                    name: table.name,
                    sql: table.sql,
                    row_count: result.count,
                    column_count: columns.length
                  });
                }
              });
            }
          });
        });
      });

      Promise.all(tablePromises)
        .then(tablesWithCounts => {
          res.json({ tables: tablesWithCounts });
        })
        .catch(error => {
          res.status(500).json({ message: 'Error getting table statistics', error: error.message });
        });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get table schema
router.get('/tables/:tableName/schema', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName } = req.params;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot access protected table' });
    }

    db.all(`PRAGMA table_info("${tableName}")`, (err, schema) => {
      if (err) {
        return res.status(500).json({ message: 'Error fetching table schema', error: err.message });
      }

      res.json({ schema });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get table data
router.get('/tables/:tableName/data', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName } = req.params;
    const { limit = 100, offset = 0 } = req.query;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot access protected table' });
    }

    db.all(`SELECT * FROM "${tableName}" LIMIT ? OFFSET ?`, [parseInt(limit), parseInt(offset)], (err, data) => {
      if (err) {
        return res.status(500).json({ message: 'Error fetching table data', error: err.message });
      }

      res.json({ data });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add new column to table
router.post('/tables/:tableName/columns', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName } = req.params;
    const { name, type, nullable, defaultValue } = req.body;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot modify protected table' });
    }

    if (!name || !type) {
      return res.status(400).json({ message: 'Column name and type are required' });
    }

    // Validate column name (basic validation)
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) {
      return res.status(400).json({ message: 'Invalid column name' });
    }

    // Build ALTER TABLE statement
    let sql = `ALTER TABLE "${tableName}" ADD COLUMN "${name}" ${type}`;

    if (!nullable) {
      sql += ' NOT NULL';
    }

    if (defaultValue) {
      sql += ` DEFAULT '${defaultValue}'`;
    }

    db.run(sql, (err) => {
      if (err) {
        return res.status(500).json({ message: 'Error adding column', error: err.message });
      }

      res.json({ message: 'Column added successfully' });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete column from table
router.delete('/tables/:tableName/columns/:columnName', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName, columnName } = req.params;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot modify protected table' });
    }

    if (PROTECTED_COLUMNS[tableName]?.includes(columnName)) {
      return res.status(403).json({ message: 'Cannot delete protected column' });
    }

    // SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    // First, get the current table schema
    db.all(`PRAGMA table_info("${tableName}")`, (err, columns) => {
      if (err) {
        return res.status(500).json({ message: 'Error getting table schema', error: err.message });
      }

      const remainingColumns = columns.filter(col => col.name !== columnName);

      if (remainingColumns.length === columns.length) {
        return res.status(404).json({ message: 'Column not found' });
      }

      // Create new table without the column
      const columnDefs = remainingColumns.map(col => {
        let def = `"${col.name}" ${col.type}`;
        if (col.pk) def += ' PRIMARY KEY';
        if (col.notnull && !col.pk) def += ' NOT NULL';
        if (col.dflt_value) def += ` DEFAULT ${col.dflt_value}`;
        return def;
      }).join(', ');

      const columnNames = remainingColumns.map(col => `"${col.name}"`).join(', ');

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        db.run(`CREATE TABLE "${tableName}_temp" (${columnDefs})`, (err) => {
          if (err) {
            db.run('ROLLBACK');
            return res.status(500).json({ message: 'Error creating temporary table', error: err.message });
          }

          db.run(`INSERT INTO "${tableName}_temp" SELECT ${columnNames} FROM "${tableName}"`, (err) => {
            if (err) {
              db.run('ROLLBACK');
              return res.status(500).json({ message: 'Error copying data', error: err.message });
            }

            db.run(`DROP TABLE "${tableName}"`, (err) => {
              if (err) {
                db.run('ROLLBACK');
                return res.status(500).json({ message: 'Error dropping original table', error: err.message });
              }

              db.run(`ALTER TABLE "${tableName}_temp" RENAME TO "${tableName}"`, (err) => {
                if (err) {
                  db.run('ROLLBACK');
                  return res.status(500).json({ message: 'Error renaming table', error: err.message });
                }

                db.run('COMMIT', (err) => {
                  if (err) {
                    return res.status(500).json({ message: 'Error committing transaction', error: err.message });
                  }
                  res.json({ message: 'Column deleted successfully' });
                });
              });
            });
          });
        });
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Add new row to table
router.post('/tables/:tableName/rows', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName } = req.params;
    const rowData = req.body;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot modify protected table' });
    }

    const columns = Object.keys(rowData);
    const values = Object.values(rowData);
    const placeholders = columns.map(() => '?').join(', ');
    const columnNames = columns.map(col => `"${col}"`).join(', ');

    const sql = `INSERT INTO "${tableName}" (${columnNames}) VALUES (${placeholders})`;

    db.run(sql, values, function(err) {
      if (err) {
        return res.status(500).json({ message: 'Error inserting row', error: err.message });
      }

      res.json({ message: 'Row added successfully', id: this.lastID });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update row in table
router.put('/tables/:tableName/rows/:rowId', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName, rowId } = req.params;
    const rowData = req.body;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot modify protected table' });
    }

    // Remove protected columns from update data
    const protectedCols = PROTECTED_COLUMNS[tableName] || [];
    const updateData = { ...rowData };
    protectedCols.forEach(col => delete updateData[col]);

    const columns = Object.keys(updateData);
    const values = Object.values(updateData);

    if (columns.length === 0) {
      return res.status(400).json({ message: 'No updatable columns provided' });
    }

    const setClause = columns.map(col => `"${col}" = ?`).join(', ');

    // First, get the primary key column name
    db.all(`PRAGMA table_info("${tableName}")`, (err, schema) => {
      if (err) {
        return res.status(500).json({ message: 'Error getting table schema', error: err.message });
      }

      const primaryKey = schema.find(col => col.pk)?.name || 'id';
      const sql = `UPDATE "${tableName}" SET ${setClause} WHERE "${primaryKey}" = ?`;

      db.run(sql, [...values, rowId], function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error updating row', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'Row not found' });
        }

        res.json({ message: 'Row updated successfully', changes: this.changes });
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete row from table
router.delete('/tables/:tableName/rows/:rowId', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { tableName, rowId } = req.params;
    const db = req.app.locals.db;

    if (PROTECTED_TABLES.includes(tableName)) {
      return res.status(403).json({ message: 'Cannot modify protected table' });
    }

    // Get the primary key column name
    db.all(`PRAGMA table_info("${tableName}")`, (err, schema) => {
      if (err) {
        return res.status(500).json({ message: 'Error getting table schema', error: err.message });
      }

      const primaryKey = schema.find(col => col.pk)?.name || 'id';
      const sql = `DELETE FROM "${tableName}" WHERE "${primaryKey}" = ?`;

      db.run(sql, [rowId], function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error deleting row', error: err.message });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'Row not found' });
        }

        res.json({ message: 'Row deleted successfully', changes: this.changes });
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Execute custom SQL query (with restrictions)
router.post('/query', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { sql } = req.body;
    const db = req.app.locals.db;

    if (!sql) {
      return res.status(400).json({ message: 'SQL query is required' });
    }

    // Basic security checks - only allow SELECT, INSERT, UPDATE, DELETE
    const trimmedSql = sql.trim().toUpperCase();
    const allowedOperations = ['SELECT', 'INSERT', 'UPDATE', 'DELETE'];
    const operation = trimmedSql.split(' ')[0];

    if (!allowedOperations.includes(operation)) {
      return res.status(403).json({ message: 'Only SELECT, INSERT, UPDATE, DELETE operations are allowed' });
    }

    // Prevent operations on protected tables
    const protectedTableRegex = new RegExp(`\\b(${PROTECTED_TABLES.join('|')})\\b`, 'i');
    if (protectedTableRegex.test(sql)) {
      return res.status(403).json({ message: 'Cannot execute queries on protected tables' });
    }

    if (operation === 'SELECT') {
      db.all(sql, (err, rows) => {
        if (err) {
          return res.status(500).json({ message: 'Error executing query', error: err.message });
        }
        res.json({ result: rows, type: 'select' });
      });
    } else {
      db.run(sql, function(err) {
        if (err) {
          return res.status(500).json({ message: 'Error executing query', error: err.message });
        }
        res.json({
          result: {
            changes: this.changes,
            lastID: this.lastID
          },
          type: 'modify'
        });
      });
    }
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;
