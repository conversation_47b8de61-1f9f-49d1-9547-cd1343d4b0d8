import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Snackbar,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
  Grid,
  Card,
  CardContent,
  ButtonGroup,
  Menu
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Delete as DeleteIcon,
  Assignment as AssignmentIcon,
  Save as SaveIcon,
  Badge,
  CheckCircle,
  ListAlt,
  Add as AddIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  KeyboardArrowDown,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Add,
  Book
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../context/LanguageContext';
import { useTheme } from '@mui/material/styles';
import api from '../../utils/api';

const SuggestionsManager = () => {
  const { translate } = useLanguage();
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [categories, setCategories] = useState([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [processing, setProcessing] = useState(false);
  const [viewMode, setViewMode] = useState('pending'); // 'pending' or 'all'
  const [allSuggestions, setAllSuggestions] = useState([]);
  const [counts, setCounts] = useState({
    pending: 0,
    acknowledged: 0,
    approved: 0,
    rejected: 0,
    total: 0
  });
  
  const theme = useTheme();
  
  // Fetch all suggestions on component mount
  useEffect(() => {
    fetchSuggestions();
    fetchCategories();
  }, [viewMode]); // Refetch when view mode changes
  
  // Fetch all book suggestions
  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log(`Fetching suggestions with viewMode: ${viewMode}`);
      
      // Set status filter based on view mode
      const query = viewMode === 'all' ? 'status=all' : `status=${viewMode}`;
      const response = await api.direct.get(`/api/suggestions?${query}`);
      
      // The response now includes suggestions and counts
      console.log('Suggestions API response:', response.data);
      
      // Extract data from response
      const { suggestions = [], counts = {} } = response.data;
      
      // Set all suggestions
      setAllSuggestions(suggestions);
      setSuggestions(suggestions);
      
      // Set counts in state
      setCounts({
        pending: counts.pending || 0,
        acknowledged: counts.acknowledged || 0,
        approved: counts.approved || 0,
        rejected: counts.rejected || 0,
        total: counts.total || 0
      });
      
      console.log(`Loaded ${suggestions.length} suggestions with viewMode: ${viewMode}`);
    } catch (err) {
      console.error('Error fetching suggestions:', err);
      console.error('Error response:', err.response?.data);
      
      if (err.response?.status === 403) {
        setError(translate('You do not have permission to view suggestions. Admin access required.'));
      } else {
        setError(translate('Failed to load suggestions. Please try again.'));
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Refresh suggestions when viewMode changes
  useEffect(() => {
    fetchSuggestions();
  }, [viewMode]);
  
  // Fetch categories
  const fetchCategories = async () => {
    try {
      // For suggestions, we use special status categories instead of book categories
      // We hardcode these values since they're suggestion statuses, not regular categories
      setCategories(['pending', 'acknowledged']);
    } catch (err) {
      console.error('Error setting suggestion categories:', err);
    }
  };
  
  // Open approval dialog
  const handleOpenApprovalDialog = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setApprovalDialogOpen(true);
  };
  
  // Close approval dialog
  const handleCloseApprovalDialog = () => {
    setApprovalDialogOpen(false);
    setSelectedSuggestion(null);
  };
  
  // Open rejection dialog
  const handleOpenRejectDialog = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setRejectDialogOpen(true);
  };
  
  // Close rejection dialog
  const handleCloseRejectDialog = () => {
    setRejectDialogOpen(false);
    setSelectedSuggestion(null);
  };
  
  // Open category assignment dialog
  const handleOpenCategoryDialog = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setSelectedCategory(suggestion.status || 'pending');
    setCategoryDialogOpen(true);
  };
  
  // Close category assignment dialog
  const handleCloseCategoryDialog = () => {
    setCategoryDialogOpen(false);
    setSelectedSuggestion(null);
    setSelectedCategory('pending');
    setNewCategory('');
  };
  
  // Handle approve suggestion
  const handleApproveSuggestion = async () => {
    if (!selectedSuggestion) return;
    
    setProcessing(true);
    
    try {
      // Send copies=1 to the server
      await api.direct.put(`/api/suggestions/${selectedSuggestion.id}/approve`, { copies: 1 });
      
      console.log(`Suggestion ${selectedSuggestion.id} approved successfully`);
      
      // If we're viewing only pending, remove from list
      if (viewMode === 'pending') {
        setSuggestions(prev => prev.filter(s => s.id !== selectedSuggestion.id));
      } else {
        // If we're viewing all, update status in the list
        setSuggestions(prev => prev.map(s => 
          s.id === selectedSuggestion.id ? { ...s, status: 'approved' } : s
        ));
      }
      
      // Show success message
      setSnackbarMessage(translate('Suggestion approved successfully'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Close dialog
      handleCloseApprovalDialog();
      
      // Refresh the suggestions list to ensure state is consistent with server
      setTimeout(() => {
        fetchSuggestions();
      }, 500);
    } catch (err) {
      console.error('Error approving suggestion:', err);
      console.error('Error details:', err.response?.data);
      setSnackbarMessage(translate('Failed to approve suggestion. Please check console for details.'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setProcessing(false);
    }
  };
  
  // Handle reject suggestion
  const handleRejectSuggestion = async () => {
    if (!selectedSuggestion) return;
    
    setProcessing(true);
    
    try {
      // Use DELETE instead of PUT to completely remove the suggestion
      await api.direct.delete(`/api/suggestions/${selectedSuggestion.id}`);
      
      console.log(`Suggestion ${selectedSuggestion.id} completely deleted`);
      
      // Always remove from list since it's deleted from DB
      setSuggestions(prev => prev.filter(s => s.id !== selectedSuggestion.id));
      
      // Show success message
      setSnackbarMessage(translate('Suggestion deleted successfully'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Close dialog
      handleCloseRejectDialog();
      
      // Refresh the suggestions list
      setTimeout(() => {
        fetchSuggestions();
      }, 500);
    } catch (err) {
      console.error('Error deleting suggestion:', err);
      console.error('Error details:', err.response?.data);
      setSnackbarMessage(translate('Failed to delete suggestion. Please check console for details.'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setProcessing(false);
    }
  };
  
  // Handle assign category
  const handleAssignCategory = async () => {
    if (!selectedSuggestion) return;
    
    setProcessing(true);
    
    // For suggestions, the "category" is actually the suggestion's status
    const newStatus = selectedCategory === 'pending' ? 'pending' : 'acknowledged';
    
    try {
      console.log(`Updating suggestion ${selectedSuggestion.id} status to "${newStatus}"`);
      
      // Update suggestion status
      const response = await api.direct.put(`/api/suggestions/${selectedSuggestion.id}`, 
        { status: newStatus }
      );
      
      console.log('Update response:', response.data);
      
      // Update in UI
      setSuggestions(prev => prev.map(s => 
        s.id === selectedSuggestion.id ? { ...s, status: newStatus } : s
      ));
      
      // Show success message
      setSnackbarMessage(translate(`Suggestion status updated to ${newStatus}`));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Close dialog
      handleCloseCategoryDialog();
      
      // Refresh the suggestions list
      setTimeout(() => {
        fetchSuggestions();
      }, 500);
    } catch (err) {
      console.error('Error updating suggestion status:', err);
      console.error('Error details:', err.response?.data);
      setSnackbarMessage(translate('Failed to update suggestion status. Please check console for details.'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setProcessing(false);
    }
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };
  
  // Toggle view mode function
  const toggleViewMode = (newMode) => {
    // If a specific mode is passed, use that
    if (newMode) {
      setViewMode(newMode);
    } 
    // Otherwise toggle between modes
    else {
      if (viewMode === 'pending') {
        setViewMode('all');
      } else {
        setViewMode('pending');
      }
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }
  
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          {translate('Book Suggestions')}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="body1" color="textSecondary">
              {translate('Manage user-submitted book suggestions')}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
              <Chip 
                icon={<Badge />} 
                label={`${translate('Pending')}: ${counts.pending}`} 
                color="warning" 
                size="small"
                variant={viewMode === 'pending' ? 'filled' : 'outlined'}
                onClick={() => toggleViewMode('pending')}
              />
              <Chip 
                icon={<CheckCircle />} 
                label={`${translate('Acknowledged')}: ${counts.acknowledged}`} 
                color="success" 
                size="small"
                variant={viewMode === 'acknowledged' ? 'filled' : 'outlined'}
                onClick={() => toggleViewMode('acknowledged')}
              />
              <Chip 
                icon={<ListAlt />} 
                label={`${translate('Total')}: ${counts.total}`} 
                color="primary" 
                size="small"
                variant={viewMode === 'all' ? 'filled' : 'outlined'}
                onClick={() => toggleViewMode('all')}
              />
            </Box>
          </Box>
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {suggestions.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="textSecondary">
            {translate('No pending suggestions')}
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table aria-label="suggestions table">
            <TableHead>
              <TableRow>
                <TableCell>{translate('Title')}</TableCell>
                <TableCell>{translate('Author')}</TableCell>
                <TableCell>{translate('ISBN')}</TableCell>
                <TableCell>{translate('User')}</TableCell>
                <TableCell>{translate('Submitted')}</TableCell>
                <TableCell>{translate('Category')}</TableCell>
                <TableCell align="center">{translate('Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {suggestions.map((suggestion) => (
                <TableRow key={suggestion.id}>
                  <TableCell>{suggestion.title}</TableCell>
                  <TableCell>{suggestion.author}</TableCell>
                  <TableCell>{suggestion.isbn || '—'}</TableCell>
                  <TableCell>{suggestion.username || suggestion.user_id}</TableCell>
                  <TableCell>{formatDate(suggestion.created_at)}</TableCell>
                  <TableCell>
                    {suggestion.status === 'pending' ? (
                      <Chip label={translate('Pending')} size="small" color="warning" />
                    ) : (
                      <Chip label={translate('Acknowledged')} size="small" color="success" />
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <Tooltip title={translate('Approve')}>
                        <IconButton 
                          color="success" 
                          onClick={() => handleOpenApprovalDialog(suggestion)}
                          size="small"
                        >
                          <CheckCircleIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={translate('Delete')}>
                        <IconButton 
                          color="error" 
                          onClick={() => handleOpenRejectDialog(suggestion)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={translate('Assign Category')}>
                        <IconButton 
                          color="primary" 
                          onClick={() => handleOpenCategoryDialog(suggestion)}
                          size="small"
                        >
                          <AssignmentIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onClose={handleCloseApprovalDialog}>
        <DialogTitle>{translate('Approve Suggestion')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {translate('Are you sure you want to approve this suggestion? This will add the book to the library catalog.')}
          </DialogContentText>
          {selectedSuggestion && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1">
                <strong>{translate('Title')}:</strong> {selectedSuggestion.title}
              </Typography>
              <Typography variant="subtitle1">
                <strong>{translate('Author')}:</strong> {selectedSuggestion.author}
              </Typography>
              {selectedSuggestion.isbn && (
                <Typography variant="subtitle1">
                  <strong>{translate('ISBN')}:</strong> {selectedSuggestion.isbn}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseApprovalDialog} disabled={processing}>
            {translate('Cancel')}
          </Button>
          <Button 
            onClick={handleApproveSuggestion} 
            color="primary" 
            variant="contained"
            disabled={processing}
          >
            {processing ? translate('Processing...') : translate('Approve')}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={handleCloseRejectDialog}>
        <DialogTitle>{translate('Delete Suggestion')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {translate('Are you sure you want to delete this suggestion? This action will permanently remove it from the database and cannot be undone.')}
          </DialogContentText>
          {selectedSuggestion && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1">
                <strong>{translate('Title')}:</strong> {selectedSuggestion.title}
              </Typography>
              <Typography variant="subtitle1">
                <strong>{translate('Author')}:</strong> {selectedSuggestion.author}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRejectDialog} disabled={processing}>
            {translate('Cancel')}
          </Button>
          <Button 
            onClick={handleRejectSuggestion} 
            color="error" 
            variant="contained"
            disabled={processing}
          >
            {processing ? translate('Processing...') : translate('Delete')}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Category Assignment Dialog */}
      <Dialog open={categoryDialogOpen} onClose={handleCloseCategoryDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{translate('Update Suggestion Status')}</DialogTitle>
        <DialogContent>
          <Box sx={{ py: 1 }}>
            <FormControl fullWidth sx={{ mt: 1 }}>
              <InputLabel id="category-select-label">{translate('Status')}</InputLabel>
              <Select
                labelId="category-select-label"
                id="category-select"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                label={translate('Status')}
              >
                <MenuItem value="">{translate('Uncategorized')}</MenuItem>
                {categories.map((category) => (
                  <MenuItem 
                    key={category} 
                    value={category}
                    sx={{ 
                      textTransform: 'capitalize',
                      color: category === 'pending' ? 'orange' : 'green'
                    }}
                  >
                    {category === 'pending' ? translate('Pending') : translate('Acknowledged')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCategoryDialog} color="primary">
            {translate('Cancel')}
          </Button>
          <Button
            onClick={handleAssignCategory}
            color="primary"
            disabled={processing}
            startIcon={processing ? <CircularProgress size={24} /> : <SaveIcon />}
          >
            {translate('Update Status')}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </motion.div>
  );
};

export default SuggestionsManager; 