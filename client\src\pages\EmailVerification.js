import React, { useState, useContext, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Button,
  Box,
  Paper,
  Alert,
  CircularProgress,
  Link,
  Divider,
  LinearProgress
} from '@mui/material';
import { Email as EmailIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { AuthContext } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { firebaseInitPromise } from '../firebase';
import api from '../utils/api';

const EmailVerification = () => {
  const { user, emailVerificationSent, sendVerificationEmail, refreshUserData, isRateLimited } = useContext(AuthContext);
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendSuccess, setResendSuccess] = useState(false);
  const [verificationChecking, setVerificationChecking] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState('pending'); // 'pending', 'verified', 'unverified'
  const navigate = useNavigate();
  const [lastSentTime, setLastSentTime] = useState(() => {
    // Try to load the last sent time from localStorage
    const storedTime = localStorage.getItem('lastVerificationEmailSent');
    return storedTime ? parseInt(storedTime, 10) : 0;
  });
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [resendCount, setResendCount] = useState(() => {
    // Track how many times user has resent (for rate limiting)
    const storedCount = localStorage.getItem('verificationResendCount');
    return storedCount ? parseInt(storedCount, 10) : 0;
  });

  // Initial check and periodic verification monitoring
  useEffect(() => {
    let intervalId = null;
    let authInstance = null; // Keep authInstance scoped here

    const performInitialCheck = async (auth) => {
      if (!auth || !user || !user.id) return;
      setVerificationChecking(true);
      try {
        if (user.email_verified === 1) {
          setVerificationStatus('verified');
          return;
        }
        if (auth.currentUser) {
          await auth.currentUser.reload();
          if (auth.currentUser.emailVerified) {
            console.log('User is verified according to Firebase, syncing with backend');
            try {
              await api.direct.post('/api/auth/sync-verification-status', {
                firebase_verified: true,
                user_id: user.id,
                email: user.email,
                firebase_uid: auth.currentUser.uid,
                force_update: true
              });
              await refreshUserData(true);
              setVerificationStatus('verified');
              return;
            } catch (syncError) {
              console.error('Error syncing verification status:', syncError);
              if (syncError.response) console.error('Server response:', syncError.response.data);
            }
          }
        }
        setVerificationStatus('unverified');
      } catch (err) {
        console.error('Error during initial verification check:', err);
        setError(t('emailVerification.checkStatusError'));
      } finally {
        setVerificationChecking(false);
      }
    };

    const setupVerificationChecks = async () => {
      if (!user || !user.id) return;

      try {
        const { auth } = await firebaseInitPromise;
        authInstance = auth; // Initialize authInstance
        await performInitialCheck(authInstance);

        if (verificationStatus !== 'verified') {
          intervalId = setInterval(async () => {
            if (!authInstance || !authInstance.currentUser || !user || !user.id || verificationStatus === 'verified') {
              if (verificationStatus === 'verified' && intervalId) clearInterval(intervalId);
              return;
            }
            try {
              await authInstance.currentUser.reload();
              if (authInstance.currentUser.emailVerified) {
                console.log('User verified in Firebase during interval check');
                try {
                  await api.direct.post('/api/auth/sync-verification-status', {
                    firebase_verified: true,
                    user_id: user.id,
                    email: user.email,
                    firebase_uid: authInstance.currentUser.uid,
                    force_update: true
                  });
                  const updatedUser = await refreshUserData(true);
                  if (updatedUser && updatedUser.email_verified === 1) {
                    setVerificationStatus('verified');
                    setTimeout(() => navigate('/profile'), 2000);
                    if (intervalId) clearInterval(intervalId);
                  }
                } catch (syncError) {
                  console.error('Error syncing verification during interval:', syncError);
                  // Consider a more robust retry or error handling strategy here
                }
              }
            } catch (err) {
              console.error('Error checking verification during interval:', err);
            }
          }, 5000);
        }
      } catch (err) {
        console.error('Failed to initialize Firebase for verification checks:', err);
        setError(t('emailVerification.firebaseInitError'));
      }
    };

    setupVerificationChecks();

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [user, refreshUserData, navigate, verificationStatus, t]); // Added t to dependencies

  // Calculate time remaining on initial load and when lastSentTime changes
  useEffect(() => {
    (async () => {
      try {
        // Reload Firebase user to get latest status
        // This part seems to be a remnant of a previous structure or a merge issue.
        // The main verification logic is handled in the useEffect above.
        // I will comment it out to resolve the await error, as its functionality
        // is likely duplicated or superseded by the setupVerificationChecks logic.
        /*
        if (auth && auth.currentUser) { // Ensure auth and currentUser exist
          await auth.currentUser.reload();

          if (auth.currentUser.emailVerified) {
            console.log('User verified in Firebase during interval check (second useEffect)');
            // Sync with backend
            try {
              const response = await api.direct.post('/api/auth/sync-verification-status', {
                firebase_verified: true,
                user_id: user.id,
                email: user.email,
                firebase_uid: auth.currentUser.uid,
                force_update: true
              });

              console.log('Sync response (second useEffect):', response.data);

              // Refresh user data
              const updatedUser = await refreshUserData(true);

              // Only update status if backend confirms verification
              if (updatedUser && updatedUser.email_verified === 1) {
                setVerificationStatus('verified');

                // Show success message for a moment before redirecting
                setTimeout(() => {
                  navigate('/profile');
                }, 2000);
              }
            } catch (syncError) {
              console.error('Error syncing verification during interval (second useEffect):', syncError);
              if (syncError.response) {
                console.error('Server response (second useEffect):', syncError.response.data);
              }

              // If we get too many errors, slow down the interval
              // This clearInterval might be problematic if intervalId is not in scope or managed by this useEffect
              // clearInterval(intervalId);
              // setTimeout(() => {
              //   // Re-run the initial check after a delay
              //   initialCheck(); // initialCheck might also not be in scope here
              // }, 10000);
            }
          }
        }
        */
      } catch (err) {
        console.error('Error checking verification during interval (second useEffect):', err);
      }
    })();
// The rest of this useEffect seems to be related to the timer for resending emails,
// not directly to the Firebase verification check that was causing the await error.
// The original error was at line 197, which was part of a Firebase check.

    // Progressive cooldown timer logic
    if (lastSentTime > 0) {
      const updateTimer = () => {
        const now = Date.now();
        const timeSinceLastSent = now - lastSentTime;

        // Progressive cooldown: first resend = no limit, second = 5 minutes, third+ = 15 minutes
        let cooldownPeriod = 0;
        if (resendCount === 1) {
          cooldownPeriod = 5 * 60 * 1000; // 5 minutes
        } else if (resendCount >= 2) {
          cooldownPeriod = 15 * 60 * 1000; // 15 minutes
        }

        const remaining = cooldownPeriod - timeSinceLastSent;
        if (remaining > 0) {
          setTimeRemaining(Math.ceil(remaining / 1000));
        } else {
          setTimeRemaining(0);
          // Reset after 24 hours of no activity
          const resetPeriod = 24 * 60 * 60 * 1000; // 24 hours
          if (timeSinceLastSent > resetPeriod) {
            setResendCount(0);
            localStorage.setItem('verificationResendCount', '0');
            localStorage.removeItem('lastVerificationEmailSent');
          }
        }
      };

      updateTimer(); // Initial call
      const timerInterval = setInterval(updateTimer, 1000);
      return () => clearInterval(timerInterval);
    }
  }, [lastSentTime, resendCount, user, refreshUserData, navigate]); // Dependencies for the timer logic

  const handleResendVerification = async () => {
    if (timeRemaining > 0) {
      setError(t('emailVerification.rateLimited'));
      return;
    }

    setIsLoading(true);
    setError('');
    setResendSuccess(false);

    try {
      // Use the sendVerificationEmail from AuthContext (no parameters needed)
      await sendVerificationEmail();

      setResendSuccess(true);
      const newSentTime = Date.now();
      const newResendCount = resendCount + 1;

      // Update state and localStorage
      setLastSentTime(newSentTime);
      setResendCount(newResendCount);
      localStorage.setItem('lastVerificationEmailSent', newSentTime.toString());
      localStorage.setItem('verificationResendCount', newResendCount.toString());

      // Set appropriate cooldown based on resend count
      if (newResendCount === 1) {
        setTimeRemaining(5 * 60); // 5 minutes for second resend
      } else if (newResendCount >= 2) {
        setTimeRemaining(15 * 60); // 15 minutes for third+ resend
      }

    } catch (err) {
      console.error('Error resending verification email:', err);
      if (isRateLimited && isRateLimited(err)) {
        setError(t('emailVerification.rateLimited'));
      } else {
        setError(t('emailVerification.resendError'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (verificationChecking) {
    return (
      <Container component="main" maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>{t('emailVerification.checkingStatus')}</Typography>
      </Container>
    );
  }

  if (verificationStatus === 'verified') {
    return (
      <Container component="main" maxWidth="sm" sx={{ mt: 8 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center', backgroundColor: 'success.light' }}>
          <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ duration: 0.5 }}>
            <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
          </motion.div>
          <Typography variant="h5" gutterBottom sx={{ color: 'success.contrastText' }}>
            {t('emailVerification.statusVerified')}
          </Typography>
          <Typography sx={{ color: 'success.contrastText', mb: 3 }}>
            {t('emailVerification.statusVerifiedMessage')}
          </Typography>
          <Button variant="contained" color="primary" component={RouterLink} to="/profile">
            {t('emailVerification.goToProfile')}
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container component="main" maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={6} sx={{ p: { xs: 2, sm: 3, md: 4 }, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
          <EmailIcon color="primary" sx={{ fontSize: 50, mb: 1 }} />
          <Typography component="h1" variant="h4" gutterBottom sx={{ textAlign: 'center' }}>
            {t('emailVerification.title')}
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {resendSuccess && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {t('emailVerification.resendSuccess')}
          </Alert>
        )}

        <Typography variant="body1" sx={{ mb: 1, textAlign: 'center' }}>
          {emailVerificationSent
            ? t('emailVerification.emailSentTo')
            : t('emailVerification.statusUnverifiedMessage')}
          {user && <strong> {user.email}</strong>}.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
          {t('emailVerification.pleaseCheckInbox')}
        </Typography>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            {t('emailVerification.didNotReceive')}
          </Typography>
          <Button
            onClick={handleResendVerification}
            disabled={isLoading || timeRemaining > 0}
            variant="contained"
            color="secondary"
            fullWidth
            sx={{ mb: 1 }}
          >
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : timeRemaining > 0 ? (
              `${t('emailVerification.resendWait')} ${timeRemaining} ${t('emailVerification.seconds')}`
            ) : (
              t('emailVerification.resendEmail')
            )}
          </Button>
          {timeRemaining > 0 && (
            <Box sx={{ width: '100%', mt: 1, mb: 2 }}>
              <LinearProgress
                variant="determinate"
                value={(() => {
                  // Calculate progress based on current cooldown period
                  let totalCooldown = 0;
                  if (resendCount === 1) {
                    totalCooldown = 5 * 60; // 5 minutes
                  } else if (resendCount >= 2) {
                    totalCooldown = 15 * 60; // 15 minutes
                  }
                  return totalCooldown > 0 ? ((totalCooldown - timeRemaining) / totalCooldown) * 100 : 0;
                })()}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')} remaining
              </Typography>
            </Box>
          )}
          <Typography variant="caption" color="text.secondary">
            {t('emailVerification.ifNotReceived')}
          </Typography>
        </Box>

        <Button
          component={RouterLink}
          to="/login"
          fullWidth
          variant="outlined"
          sx={{ mt: 4 }}
        >
          {t('emailVerification.backToLogin')}
        </Button>
      </Paper>
    </Container>
  );
};

export default EmailVerification;