const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Check if Firebase app is already initialized
let auth;
try {
  // Try to get the existing default app
  const existingApp = admin.app();
  console.log('Firebase Admin: Using existing Firebase app');
  auth = existingApp.auth();
} catch (err) {
  // No existing app, need to initialize
  console.log('Firebase Admin: No existing app found, initializing new app');
  
  // Check if we have service account credentials
  let serviceAccount;
  try {
    // Try to load service account from environment variable
    if (process.env.FIREBASE_SERVICE_ACCOUNT) {
      serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT);
      console.log('Firebase Admin: Using service account from environment variable');
    } else {
      // Try to load from file
      const serviceAccountPath = path.resolve(__dirname, '../firebaseServiceAccount.json');
      if (fs.existsSync(serviceAccountPath)) {
        serviceAccount = require(serviceAccountPath);
        console.log('Firebase Admin: Using service account from file');
      }
    }
  } catch (error) {
    console.error('Firebase Admin: Error loading service account:', error.message);
  }
  
  // Initialize with a unique name to avoid conflicts
  try {
    const app = serviceAccount 
      ? admin.initializeApp({
          credential: admin.credential.cert(serviceAccount)
        }, 'educanet-verification-app')
      : admin.initializeApp(undefined, 'educanet-verification-app');
      
    console.log('Firebase Admin: Successfully initialized app with name "educanet-verification-app"');
    auth = app.auth();
  } catch (initError) {
    console.error('Firebase Admin: Error initializing app:', initError.message);
    
    // Try to get existing app by name
    try {
      const app = admin.app('educanet-verification-app');
      console.log('Firebase Admin: Using existing app with name "educanet-verification-app"');
      auth = app.auth();
    } catch (appError) {
      console.error('Firebase Admin: Could not get app by name:', appError.message);
      
      // Last resort - try to use default app
      try {
        auth = admin.auth();
        console.log('Firebase Admin: Using default app auth');
      } catch (defaultError) {
        console.error('Firebase Admin: Could not access auth:', defaultError.message);
      }
    }
  }
}

/**
 * Verify a Firebase ID token
 * @param {string} idToken - The Firebase ID token to verify
 * @returns {Promise<object>} The decoded token claims
 */
const verifyIdToken = async (idToken) => {
  try {
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    const decodedToken = await auth.verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying Firebase ID token:', error);
    throw error;
  }
};

/**
 * Get a user by email
 * @param {string} email - The user's email
 * @returns {Promise<object>} The Firebase user record
 */
const getUserByEmail = async (email) => {
  try {
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    const userRecord = await auth.getUserByEmail(email);
    return userRecord;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
};

/**
 * Verify an email verification code
 * @param {string} code - The verification code from email
 * @returns {Promise<string>} The email that was verified
 */
const verifyEmailCode = async (code) => {
  try {
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    return await auth.verifyEmailVerificationCode(code);
  } catch (error) {
    console.error('Error verifying email code:', error);
    throw error;
  }
};

/**
 * Apply the email verification code
 * @param {string} code - The verification code from email
 * @returns {Promise<void>}
 */
const applyEmailVerificationCode = async (code) => {
  try {
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    return await auth.applyActionCode(code);
  } catch (error) {
    console.error('Error applying email verification code:', error);
    throw error;
  }
};

module.exports = {
  admin,
  auth,
  verifyIdToken,
  getUserByEmail,
  verifyEmailCode,
  applyEmailVerificationCode
}; 