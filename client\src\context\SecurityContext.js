import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../utils/api';

// Create Security Context
const SecurityContext = createContext();

// Security Context Provider
export const SecurityProvider = ({ children }) => {
  const [securityStatus, setSecurityStatus] = useState({
    mode: 'normal',
    requiresRecaptcha: false,
    loading: true
  });

  // Function to fetch security status from server
  const fetchSecurityStatus = async () => {
    try {
      console.log('[SecurityContext] Fetching security status from server...');
      const response = await api.direct.get('/api/security/status');
      console.log('[SecurityContext] Server response:', response.data);
      const { mode } = response.data;

      const newStatus = {
        mode,
        requiresRecaptcha: mode === 'mild',
        loading: false
      };

      console.log('[SecurityContext] Setting security status:', newStatus);
      setSecurityStatus(newStatus);
      return newStatus;
    } catch (error) {
      console.error('[SecurityContext] Failed to fetch security status:', error);
      const fallbackStatus = {
        mode: 'normal',
        requiresRecaptcha: false,
        loading: false
      };
      console.log('[SecurityContext] Using fallback status:', fallbackStatus);
      setSecurityStatus(fallbackStatus);
      return fallbackStatus;
    }
  };

  // Initial load and auto-sync setup
  useEffect(() => {
    fetchSecurityStatus();

    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchSecurityStatus();
    }, 30000);

    // Refresh when window becomes visible (user switches back to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchSecurityStatus();
      }
    };

    // Refresh when window gains focus
    const handleFocus = () => {
      fetchSecurityStatus();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Refresh security status (can be called by components)
  const refreshSecurityStatus = () => {
    return fetchSecurityStatus();
  };

  const value = {
    securityStatus,
    refreshSecurityStatus,
    isLoading: securityStatus.loading,
    fetchSecurityStatus // Expose for manual calls
  };

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

// Custom hook to use security context
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

export default SecurityContext;
