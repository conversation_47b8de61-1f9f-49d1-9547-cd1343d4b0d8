import React, { useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import { CircularProgress, Box } from '@mui/material';

/**
 * PublicRoute component
 * Allows both authenticated and unauthenticated users to access the route
 * Unlike VerifiedRoute, this doesn't redirect to login for unauthenticated users
 */
const PublicRoute = ({ children }) => {
  const { loading } = useContext(AuthContext);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render the component for everyone
  return children;
};

export default PublicRoute; 