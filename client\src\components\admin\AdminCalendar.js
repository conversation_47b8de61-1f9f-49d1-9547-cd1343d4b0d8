import React from 'react';
import { Box, Typography, Paper, useTheme, Tooltip, IconButton } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import LeaseCalendar from '../lease/LeaseCalendar';
import { useLanguage } from '../../context/LanguageContext';

const AdminCalendar = ({ leases = [] }) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  const [refreshKey, setRefreshKey] = React.useState(0);
  
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: { xs: 2, sm: 3 }, 
        mb: { xs: 2, sm: 4 },
        borderRadius: { xs: 1, sm: 2 }
      }}
    >
      <Box sx={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: { xs: 1, sm: 2 }
      }}>
        <Typography 
          variant="h5" 
          sx={{ 
            fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' }
          }}
        >
          {translate('Lease Management Calendar')}
        </Typography>
        
        <Tooltip title={translate('Refresh Calendar')}>
          <IconButton onClick={handleRefresh} size="small">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Typography 
        variant="body2" 
        color="text.secondary" 
        paragraph
        sx={{ 
          fontSize: { xs: '0.875rem', sm: '1rem' },
          mb: { xs: 2, sm: 3 }
        }}
      >
        {translate('This calendar shows all lease activities across all users. Use the filters to view specific books or users. Switch to the "Expiring Leases" tab to see leases that are due in the next 7 days.')}
      </Typography>
      
      <Box sx={{ 
        mt: { xs: 1, sm: 2 },
        '& .MuiPaper-root': {
          borderRadius: { xs: 1, sm: 2 }
        }
      }}>
        <LeaseCalendar key={refreshKey} isAdmin={true} initialLeases={leases} />
      </Box>
    </Paper>
  );
};

export default AdminCalendar;
