{"name": "book-leasing-app", "version": "1.2.0", "description": "A responsive application for leasing books with a beautiful UI, animations, and mobile-optimized experience", "main": "server/index.js", "scripts": {"start": "concurrently npm:server npm:client", "server": "nodemon server/index.js", "client": "cd client && npm run start", "install-all": "npm install && cd client && npm install", "build": "cd client && npm run build", "dev": "nodemon server/index.js", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "npm run build && npm run prod", "vercel-build": "npm run install-all && npm run build", "prod": "cross-env NODE_ENV=production PORT=80 node server/index.js", "prod:win": "cross-env NODE_ENV=production PORT=80 node server/index.js", "prod:build": "npm run build && npm run prod", "prod:safe": "npm run build && cross-env NODE_ENV=production PORT=80 node server/index.js"}, "keywords": ["book", "leasing", "sqlite", "react", "responsive", "mobile-friendly"], "author": "Book Leasing Team", "license": "MIT", "dependencies": {"@zxing/library": "^0.21.3", "ajv": "^8.17.1", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase-admin": "^13.2.0", "google-auth-library": "^9.15.1", "html5-qrcode": "^2.3.8", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "quagga": "^0.12.1", "react-device-detect": "^2.2.3", "react-modal": "^3.16.3", "sqlite3": "^5.1.7"}, "devDependencies": {"concurrently": "^9.1.2", "cross-env": "^7.0.3", "nodemon": "^3.1.9"}, "repository": {"type": "git", "url": "https://github.com/bookleasingteam/book-leasing-app"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}