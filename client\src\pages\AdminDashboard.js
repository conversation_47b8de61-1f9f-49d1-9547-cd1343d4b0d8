import React, { useState, useEffect, useContext } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  InputAdornment,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  CameraAlt as CameraIcon,
  Dashboard,
  MenuBook,
  History,
  Group,
  QrCodeScanner,
  AssignmentOutlined,
  SecurityOutlined,
  CalendarMonth,
  ViewList,
  Refresh as RefreshIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import { AuthContext } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import UserManagement from '../components/admin/UserManagement';
import SecurityAnalytics from '../components/admin/SecurityAnalytics';
import BookScannerAdmin from '../components/admin/BookScannerAdmin';
import BulkLeaseActions from '../components/admin/BulkLeaseActions';
import AdminCalendar from '../components/admin/AdminCalendar';
import SuggestionManagement from '../components/admin/SuggestionManagement';
import BookManagement from '../components/admin/BookManagement';
import DatabaseManagement from '../components/admin/DatabaseManagement';
import api from '../utils/api';

// Lease Management Component
const LeaseManagement = () => {
  const [leases, setLeases] = useState([]);
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const { translate } = useLanguage();

  // Load leases when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError('');

        console.log('LeaseManagement: Fetching lease data');

        // Fetch leases
        const leasesRes = await api.direct.get('/api/leases');

        // Better handling of response data structure
        let leaseData;
        if (Array.isArray(leasesRes.data)) {
          leaseData = leasesRes.data;
        } else if (leasesRes.data && leasesRes.data.leases) {
          leaseData = leasesRes.data.leases;
        } else {
          leaseData = [];
        }

        console.log('Admin dashboard lease data:', leaseData);
        setLeases(leaseData);

        // Fetch books
        const booksRes = await api.direct.get('/api/books');
        setBooks(Array.isArray(booksRes.data) ? booksRes.data :
                (booksRes.data.books || []));
      } catch (err) {
        console.error('Error fetching data for lease management:', err);
        setError('Failed to load lease data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up refresh interval
    const refreshInterval = setInterval(() => {
      fetchData();
    }, 60000); // Refresh every minute

    return () => clearInterval(refreshInterval);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (loading && leases.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
        <Button
          variant="contained"
          onClick={() => window.location.reload()}
          startIcon={<RefreshIcon />}
        >
          {translate('Refresh')}
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>{translate('Lease Management')}</Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label={translate('Bulk Actions')} icon={<ViewList />} />
          <Tab label={translate('Calendar View')} icon={<CalendarMonth />} />
        </Tabs>
      </Box>

      {activeTab === 0 && (
        <BulkLeaseActions leases={leases} books={books} setLeases={setLeases} />
      )}

      {activeTab === 1 && (
        <AdminCalendar leases={leases} />
      )}
    </Box>
  );
};

const AdminDashboard = () => {
  const { user, isAuthenticated } = useContext(AuthContext);
  const { translate } = useLanguage();
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  // Define dashboard home component inline
  const AdminDashboardHome = () => (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>{translate('Welcome to Admin Dashboard')}</Typography>
      <Typography variant="body1">
        {translate('Use the tabs above to manage books, leases, users, and security settings.')}
      </Typography>
    </Box>
  );

  // Tab options
  const tabOptions = [
    { label: translate('Dashboard'), icon: <Dashboard /> },
    { label: translate('Books'), icon: <MenuBook /> },
    { label: translate('Leases'), icon: <History /> },
    { label: translate('Users'), icon: <Group /> },
    { label: translate('Suggestions'), icon: <AssignmentOutlined /> },
    { label: translate('Security'), icon: <SecurityOutlined /> },
    { label: translate('Database'), icon: <StorageIcon /> },
  ];

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({ open: false, message: '', severity: 'info' });
  };

  // Render tab content based on selected tab
  const renderTabContent = () => {
    switch (tabValue) {
      case 0:
        return <AdminDashboardHome />;
      case 1:
        return <BookManagement />;
      case 2:
        return <LeaseManagement />;
      case 3:
        return <UserManagement />;
      case 4:
        return <SuggestionManagement />;
      case 5:
        return <SecurityAnalytics />;
      case 6:
        return <DatabaseManagement />;
      default:
        return <AdminDashboardHome />;
    }
  };

  // Add an event listener for tab changes triggered by other components
  useEffect(() => {
    // Handler for tab change events from other components
    const handleTabChangeEvent = (event) => {
      const { tabIndex } = event.detail;
      setTabValue(tabIndex);
    };

    // Listen for the custom event
    window.addEventListener('adminTabChange', handleTabChangeEvent);

    // Make the setter function available globally
    window.setAdminTab = (index) => {
      setTabValue(index);
    };

    // Clean up
    return () => {
      window.removeEventListener('adminTabChange', handleTabChangeEvent);
      delete window.setAdminTab;
    };
  }, []);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minHeight: 64,
                textTransform: 'none'
              }
            }}
          >
            {tabOptions.map((option) => (
              <Tab
                key={option.label}
                label={option.label}
                icon={option.icon}
              />
            ))}
          </Tabs>
        </Box>

        {renderTabContent()}
      </Paper>
    </Container>
  );
};

export default AdminDashboard;