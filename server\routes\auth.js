const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const router = express.Router();
const { OAuth2Client } = require('google-auth-library');
const { downloadProfileImage } = require('../utils/profileImageHandler');
const firebaseAdmin = require('../utils/firebaseAdmin');
const { trackLoginAttempt } = require('./security'); // Import trackLoginAttempt
const rateLimit = require('express-rate-limit'); // Added
const axios = require('axios'); // For reCAPTCHA verification
const config = require('../config'); // For reCAPTCHA keys

// Removed unused rate limiters for forgot password and email verification

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const client = new OAuth2Client(GOOGLE_CLIENT_ID);

// JWT Secret - should be in environment variables in production
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

// reCAPTCHA verification function
const verifyRecaptcha = async (recaptchaToken, action = 'login') => {
  if (!recaptchaToken) {
    return { success: false, error: 'reCAPTCHA token is required' };
  }

  try {
    const response = await axios.post('https://www.google.com/recaptcha/api/siteverify', null, {
      params: {
        secret: config.security.recaptcha.secretKey,
        response: recaptchaToken
      }
    });

    const { success, score, action: responseAction } = response.data;

    // For reCAPTCHA v3, check score (0.0 to 1.0, higher is better)
    // Typical threshold is 0.5, but we'll use 0.3 for medium security
    const minScore = 0.3;

    if (!success) {
      console.log('reCAPTCHA verification failed:', response.data);
      return { success: false, error: 'reCAPTCHA verification failed' };
    }

    if (score < minScore) {
      console.log(`reCAPTCHA score too low: ${score} (minimum: ${minScore})`);
      return { success: false, error: 'reCAPTCHA score too low', score };
    }

    if (responseAction && responseAction !== action) {
      console.log(`reCAPTCHA action mismatch: expected ${action}, got ${responseAction}`);
      return { success: false, error: 'reCAPTCHA action mismatch' };
    }

    console.log(`reCAPTCHA verification successful: score ${score}, action ${responseAction}`);
    return { success: true, score };
  } catch (error) {
    console.error('reCAPTCHA verification error:', error.message);
    return { success: false, error: 'reCAPTCHA verification failed' };
  }
};

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) return res.status(401).json({ message: 'Access denied. No token provided.' });

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ message: 'Invalid token.' });
    req.user = user;
    next();
  });
};

// Register a new user
router.post('/register', async (req, res) => {
  console.log('[REGISTER] Received request body:', req.body); // Log incoming request body
  const { username, email, password, sendVerification = true, recaptchaToken } = req.body;

  if (!username || !email || !password) {
    return res.status(400).json({ message: 'All fields are required' });
  }

  // Check if reCAPTCHA is required (medium security mode)
  const { getSecurityStatus } = require('./security');
  const { securityMode } = getSecurityStatus();

  if (securityMode === 'mild') {
    const recaptchaResult = await verifyRecaptcha(recaptchaToken, 'register');
    if (!recaptchaResult.success) {
      return res.status(400).json({
        message: 'reCAPTCHA verification failed',
        error: recaptchaResult.error,
        requiresRecaptcha: true
      });
    }
  }

  try {
    const db = req.app.locals.db;

    // Check if user already exists
    db.get('SELECT * FROM users WHERE email = ? OR username = ?', [email, username], (err, user) => {
      console.log('[REGISTER] DB check for existing user - email:', email, 'username:', username, 'Found user:', user); // Log DB check result
      if (err) {
        console.error('Database error during user lookup:', err);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (user) {
        console.log('[REGISTER] User already exists with email or username.'); // Log if user exists
        return res.status(400).json({ message: 'User already exists' });
      }

      // Hash password
      bcrypt.hash(password, 10, (err, hashedPassword) => {
        if (err) {
          console.error('Password hashing error:', err);
          return res.status(500).json({ message: 'Error hashing password', error: err.message });
        }

        // Insert new user (email_verified set to 0 - users need to verify their email)
        db.run(
          'INSERT INTO users (username, email, password, email_verified) VALUES (?, ?, ?, ?)',
          [username, email, hashedPassword, 0],
          function(err) {
            if (err) {
              console.error('Error creating user in database:', err);
              return res.status(500).json({ message: 'Error creating user', error: err.message });
            }

            // Create token
            const token = jwt.sign(
              { id: this.lastID, username, email, role: 'user' },
              JWT_SECRET,
              { expiresIn: '24h' }
            );

            res.status(201).json({
              message: 'User registered successfully.',
              token,
              user: {
                id: this.lastID,
                username,
                email,
                role: 'user',
                email_verified: 0
              }
            });
          }
        );
      });
    });
  } catch (error) {
    console.error('Server error during registration:', error);
    res.status(500).json({ message: 'Server error', error: error.message, stack: error.stack });
  }
});

// Endpoint to finalize email verification using Firebase oobCode
router.post('/verify-firebase-email', async (req, res) => {
  const { oobCode } = req.body;

  if (!oobCode) {
    return res.status(400).json({ message: 'Verification code is required.' });
  }

  try {
    const db = req.app.locals.db;
    // Verify the oobCode with Firebase Admin SDK
    // Note: Firebase Admin SDK does not have a direct 'verifyEmailVerificationCode' like it does for password resets.
    // Instead, we get the user's email from the oobCode, then update our database.
    // The client should have already called Firebase client SDK's applyActionCode.
    // A more robust solution would be to get user details from the oobCode if possible via Admin SDK.
    // For now, we'll assume applyActionCode on client was successful and proceed to find user by a temporary claim or lookup.
    // A common pattern is to get the email from the code using checkActionCode.
    const actionCodeInfo = await firebaseAdmin.auth().checkActionCode(oobCode);
    const email = actionCodeInfo.data.email;

    if (!email) {
      return res.status(400).json({ message: 'Invalid or expired verification code. Unable to extract email.' });
    }

    // Find user by email and update their verification status
    db.get('SELECT * FROM users WHERE email = ?', [email], (err, user) => {
      if (err) {
        console.error('Database error during email verification:', err.message);
        return res.status(500).json({ message: 'Database error during email verification.' });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found for this email address.' });
      }

      if (user.email_verified === 1) {
        return res.status(200).json({ message: 'Email already verified.', user });
      }

      db.run('UPDATE users SET email_verified = 1 WHERE email = ?', [email], function(updateErr) {
        if (updateErr) {
          console.error('Error updating email verification status:', updateErr.message);
          return res.status(500).json({ message: 'Failed to update email verification status.' });
        }
        console.log(`Email verified successfully for ${email}`);
        // Return updated user info
        db.get('SELECT id, username, email, role, email_verified FROM users WHERE email = ?', [email], (fetchErr, updatedUser) => {
          if (fetchErr) {
            return res.status(500).json({ message: 'Failed to fetch updated user details.'});
          }
          res.status(200).json({ message: 'Email verified successfully!', user: updatedUser });
        });
      });
    });
  } catch (error) {
    console.error('Error verifying email with Firebase oobCode:', error);
    if (error.code === 'auth/invalid-action-code' || error.code === 'auth/expired-action-code') {
      return res.status(400).json({ message: 'Invalid or expired verification code. Please try again.' });
    }
    res.status(500).json({ message: 'Server error during email verification.', error: error.message });
  }
});


// Email verification removed - users are automatically verified upon registration

// Login user
router.post('/login', async (req, res) => {
  const { email, password, recaptchaToken } = req.body;

  console.log(`[LOGIN] Attempt for email: ${email}`);
  console.log(`[LOGIN] reCAPTCHA token provided: ${!!recaptchaToken}`);

  if (!email || !password) {
    return res.status(400).json({ message: 'All fields are required' });
  }

  // Check if reCAPTCHA is required (medium security mode)
  const { getSecurityStatus } = require('./security');
  const { securityMode } = getSecurityStatus();

  console.log(`[LOGIN] Current security mode: ${securityMode}`);

  if (securityMode === 'mild') {
    console.log(`[LOGIN] reCAPTCHA verification required`);
    if (!recaptchaToken) {
      console.log(`[LOGIN] reCAPTCHA token missing`);
      return res.status(400).json({
        message: 'reCAPTCHA verification required',
        requiresRecaptcha: true
      });
    }

    const recaptchaResult = await verifyRecaptcha(recaptchaToken, 'login');
    console.log(`[LOGIN] reCAPTCHA verification result:`, recaptchaResult);
    if (!recaptchaResult.success) {
      trackLoginAttempt(false); // Track failed login due to reCAPTCHA
      return res.status(400).json({
        message: 'reCAPTCHA verification failed',
        error: recaptchaResult.error,
        requiresRecaptcha: true
      });
    }
    console.log(`[LOGIN] reCAPTCHA verification passed`);
  } else {
    console.log(`[LOGIN] reCAPTCHA not required (mode: ${securityMode})`);
  }

  try {
    const db = req.app.locals.db;

    // Find user by email
    db.get('SELECT * FROM users WHERE email = ?', [email], async (err, user) => {
      if (err) {
        // This is a server/DB error, not necessarily a failed login attempt by user
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        // Failed login: Invalid credentials (user not found)
        trackLoginAttempt(false);
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      if (user.is_banned) {
        // Failed login: Banned user
        trackLoginAttempt(false);
        return res.status(403).json({
          message: 'Your account has been banned',
          reason: user.ban_reason
        });
      }

      // Check if system is in high security mode (lockdown)
      const { getSecurityStatus } = require('./security');
      const { securityMode } = getSecurityStatus();
      if (securityMode === 'high' && user.role !== 'admin') {
        // Failed login: Lockdown mode
        trackLoginAttempt(false);
        return res.status(403).json({
          message: 'System is in lockdown mode. Only administrators can access the system.',
          securityMode: 'high'
        });
      }

      // Compare password
      const isMatch = await bcrypt.compare(password, user.password);

      if (!isMatch) {
        // Failed login: Invalid credentials (password mismatch)
        trackLoginAttempt(false);
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // Successful login
      trackLoginAttempt(true);
      // Create token
      const token = jwt.sign(
        { id: user.id, username: user.username, email: user.email, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // Update last login
      db.run('UPDATE users SET last_login = ? WHERE id = ?', [new Date().toISOString(), user.id], (err) => {
        if (err) {
          console.error('Error updating last login:', err.message);
        }
      });

      res.json({
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          email_verified: user.email_verified || 0,
          profile_image: user.profile_image
        }
      });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get current user
router.get('/me', authenticateToken, (req, res) => {
  try {
    const db = req.app.locals.db;

    // Get fresh user data from the database
    db.get('SELECT id, username, email, role, is_banned, email_verified, profile_image, class_teacher, grade, profile_completed, created_at FROM users WHERE id = ?', [req.user.id], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json({ user });
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Google authentication
router.post('/google', async (req, res) => {
  const { username, email, googleId, photoURL, recaptchaToken } = req.body;

  console.log('Received Google auth request:', { username, email, googleId });

  if (!username || !email || !googleId) {
    return res.status(400).json({ message: 'Username, email, and googleId are required' });
  }

  // Check if reCAPTCHA is required (medium security mode)
  const { getSecurityStatus } = require('./security');
  const { securityMode } = getSecurityStatus();

  if (securityMode === 'mild') {
    const recaptchaResult = await verifyRecaptcha(recaptchaToken, 'google_login');
    if (!recaptchaResult.success) {
      return res.status(400).json({
        message: 'reCAPTCHA verification failed',
        error: recaptchaResult.error,
        requiresRecaptcha: true
      });
    }
  }

  // Check if system is in high security mode (lockdown)
  if (securityMode === 'high') {
    // In high security mode, only allow admins to login
    const db = req.app.locals.db;
    try {
      const adminCheck = await new Promise((resolve, reject) => {
        db.get('SELECT role FROM users WHERE email = ? AND role = "admin"', [email], (err, user) => {
          if (err) reject(err);
          resolve(user);
        });
      });

      if (!adminCheck) {
        return res.status(403).json({
          message: 'System is in lockdown mode. Only administrators can access the system.',
          securityMode: 'high'
        });
      }
    } catch (error) {
      console.error('Error checking admin status during lockdown:', error);
      return res.status(500).json({ message: 'Server error', error: error.message });
    }
  }

  try {
    const db = req.app.locals.db;

    // Download profile image if available
    let profileImagePath = null;
    if (photoURL) {
      try {
        // First check if user already has a profile image
        const existingUser = await new Promise((resolve, reject) => {
          db.get('SELECT profile_image FROM users WHERE email = ? OR google_id = ?', [email, googleId], (err, user) => {
            if (err) reject(err);
            else resolve(user);
          });
        });

        // Only download new image if user doesn't have one or it's different
        if (!existingUser || !existingUser.profile_image) {
          profileImagePath = await downloadProfileImage(photoURL, googleId);
          console.log('Profile image downloaded and saved:', profileImagePath);
        } else {
          console.log('User already has profile image, skipping download');
          profileImagePath = existingUser.profile_image;
        }
      } catch (imageError) {
        console.error('Error handling profile image:', imageError.message);
        // Continue with registration even if image handling fails
      }
    }

    // Check if user already exists with this email or googleId
    db.get('SELECT * FROM users WHERE email = ? OR google_id = ?', [email, googleId], (err, user) => {
      if (err) {
        console.error('Database error during Google auth:', err.message);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (user) {
        console.log('[REGISTER] User already exists with email or username.'); // Log if user exists
        console.log('Existing user found for Google auth:', user.id);
        // User exists, update googleId if needed and login
        // Always update Google users to be verified
        // Update user with Google ID, verification status, and possibly profile image
        const updateFields = [googleId];
        let updateQuery = 'UPDATE users SET google_id = ?, email_verified = 1';

        if (profileImagePath) {
          updateQuery += ', profile_image = ?';
          updateFields.push(profileImagePath);
        }

        updateQuery += ' WHERE id = ?';
        updateFields.push(user.id);

        db.run(updateQuery, updateFields, (err) => {
          if (err) {
            console.error('Error updating user with Google ID:', err.message);
            return res.status(500).json({ message: 'Error updating user', error: err.message });
          }
          console.log('Updated user with Google ID and set as verified');
        });

        // Check if we need to update the profile image separately
        if (profileImagePath && (!user.profile_image || user.profile_image !== profileImagePath)) {
          // Update profile image if it's new
          db.run('UPDATE users SET profile_image = ? WHERE id = ?', [profileImagePath, user.id], (err) => {
            if (err) {
              console.error('Error updating profile image:', err.message);
              // Don't return error, just log it - we can continue with login
            } else {
              console.log('Updated user profile image');
            }
          });
        }

        // Create token
        try {
          const token = jwt.sign(
            { id: user.id, username: user.username, email: user.email, role: user.role || 'user' },
            JWT_SECRET,
            { expiresIn: '24h' }
          );

          console.log('Google login successful for existing user');
          return res.json({
            message: 'Google login successful',
            token,
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              role: user.role || 'user',
              email_verified: 1, // Always set Google users as verified
              profile_image: user.profile_image || profileImagePath
            }
          });
        } catch (tokenError) {
          console.error('JWT sign error:', tokenError.message);
          return res.status(500).json({ message: 'Error creating authentication token', error: tokenError.message });
        }
      } else {
        console.log('Creating new user with Google credentials');
        // Create new user with Google info
        // Generate a random password for the user (they'll login with Google)
        const randomPassword = Math.random().toString(36).slice(-10);

        bcrypt.hash(randomPassword, 10, (err, hashedPassword) => {
          if (err) {
            console.error('Error hashing password:', err.message);
            return res.status(500).json({ message: 'Error hashing password', error: err.message });
          }

          console.log('Inserting new user with Google credentials');
          // Insert new user
          db.run(
            'INSERT INTO users (username, email, password, google_id, profile_image, email_verified, role) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [username, email, hashedPassword, googleId, profileImagePath, 1, 'user'], // Set email_verified to 1 for Google users
            function(err) {
              if (err) {
                console.error('Error creating user:', err.message);
                return res.status(500).json({ message: 'Error creating user', error: err.message });
              }

              console.log('New user created with ID:', this.lastID);
              // Create token with correct user data
              try {
                const token = jwt.sign(
                  { id: this.lastID, username, email, role: 'user' },
                  JWT_SECRET,
                  { expiresIn: '24h' }
                );

                console.log('Google registration successful for new user');
                return res.status(201).json({
                  message: 'User registered with Google successfully',
                  token,
                  user: {
                    id: this.lastID,
                    username,
                    email,
                    role: 'user',
                    email_verified: 1,
                    profile_image: profileImagePath
                  }
                });
              } catch (tokenError) {
                console.error('JWT sign error for new user:', tokenError.message);
                return res.status(500).json({ message: 'Error creating authentication token', error: tokenError.message });
              }
            }
          );
        });
      }
    });
  } catch (error) {
    console.error('Google auth server error:', error.message);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Password reset functionality removed - not implemented

// All password reset and email verification routes removed

// Google authentication with token verification
router.post('/google-auth', async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ message: 'ID token is required' });
  }

  try {
    // Verify Google token
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    const googleId = payload['sub'];
    const email = payload['email'];
    const name = payload['name'];
    const picture = payload['picture'];

    // Extracting a username from email or name
    const username = email.split('@')[0] || name.replace(/\s+/g, '_').toLowerCase();

    const db = req.app.locals.db;

    // Check if user exists
    db.get('SELECT * FROM users WHERE google_id = ? OR email = ?', [googleId, email], (err, user) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (user) {
        console.log('[REGISTER] User already exists with email or username.'); // Log if user exists
        // User exists, update googleId if needed
        if (!user.google_id) {
          db.run('UPDATE users SET google_id = ?, email_verified = 1 WHERE id = ?', [googleId, user.id], (err) => {
            if (err) {
              return res.status(500).json({ message: 'Error updating user', error: err.message });
            }
          });
        }

        // Create JWT token
        const token = jwt.sign(
          { id: user.id, username: user.username, email: user.email, role: user.role },
          JWT_SECRET,
          { expiresIn: '24h' }
        );

        return res.json({
          message: 'Google login successful',
          token,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            email_verified: 1
          }
        });
      } else {
        // Create new user
        const randomPassword = Math.random().toString(36).slice(-10);

        bcrypt.hash(randomPassword, 10, (err, hashedPassword) => {
          if (err) {
            return res.status(500).json({ message: 'Error hashing password', error: err.message });
          }

          // Insert new user
          db.run(
            'INSERT INTO users (username, email, password, google_id, profile_image, email_verified) VALUES (?, ?, ?, ?, ?, ?)',
            [username, email, hashedPassword, googleId, picture || null, 1],
            function(err) {
              if (err) {
                return res.status(500).json({ message: 'Error creating user', error: err.message });
              }

              // Create JWT token
              const token = jwt.sign(
                { id: this.lastID, username, email, role: 'user' },
                JWT_SECRET,
                { expiresIn: '24h' }
              );

              res.status(201).json({
                message: 'User registered with Google successfully',
                token,
                user: {
                  id: this.lastID,
                  username,
                  email,
                  role: 'user',
                  email_verified: 1
                }
              });
            }
          );
        });
      }
    });
  } catch (error) {
    res.status(401).json({ message: 'Invalid Google token', error: error.message });
  }
});

// Sync verification status with Firebase
router.post('/sync-verification-status', authenticateToken, async (req, res) => {
  try {
    const { firebase_verified, user_id, email, firebase_uid, force_update } = req.body;
    const db = req.app.locals.db;

    console.log('Syncing verification status:', { firebase_verified, user_id, email, firebase_uid, force_update });

    // Get the user to sync
    const targetUserId = user_id || req.user.id;

    db.get('SELECT * FROM users WHERE id = ?', [targetUserId], (err, user) => {
      if (err) {
        console.error('Database error during sync:', err);
        return res.status(500).json({ message: 'Database error', error: err.message });
      }

      if (!user) {
        console.warn('User not found during sync:', targetUserId);
        return res.status(404).json({ message: 'User not found' });
      }

      // Update verification status if Firebase says user is verified
      if (firebase_verified && (force_update || !user.email_verified)) {
        db.run(
          'UPDATE users SET email_verified = 1 WHERE id = ?',
          [targetUserId],
          function(updateErr) {
            if (updateErr) {
              console.error('Error updating verification status:', updateErr);
              return res.status(500).json({ message: 'Error updating verification status', error: updateErr.message });
            }

            console.log(`User ${targetUserId} verification status synced successfully`);
            res.json({
              message: 'Verification status synced successfully',
              user: {
                ...user,
                email_verified: 1
              }
            });
          }
        );
      } else {
        // No update needed
        res.json({
          message: 'Verification status already up to date',
          user: user
        });
      }
    });
  } catch (error) {
    console.error('Error syncing verification status:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Verify Firebase email with oobCode
router.post('/verify-firebase-email', async (req, res) => {
  try {
    const { oobCode, force_update } = req.body;

    if (!oobCode) {
      return res.status(400).json({ message: 'Verification code is required' });
    }

    console.log('Processing Firebase email verification with oobCode');

    // Import Firebase Admin utilities
    const { verifyEmailCode, applyEmailVerificationCode } = require('../utils/firebaseAdmin');

    try {
      // Verify the code with Firebase
      const verifiedEmail = await verifyEmailCode(oobCode);
      console.log('Firebase verification successful for email:', verifiedEmail);

      // Apply the verification code
      await applyEmailVerificationCode(oobCode);
      console.log('Firebase verification code applied successfully');

      // Update our database
      const db = req.app.locals.db;

      db.get('SELECT * FROM users WHERE email = ?', [verifiedEmail], (err, user) => {
        if (err) {
          console.error('Database error during Firebase verification:', err);
          return res.status(500).json({ message: 'Database error', error: err.message });
        }

        if (!user) {
          console.warn('User not found for verified email:', verifiedEmail);
          return res.status(404).json({ message: 'User not found for verified email' });
        }

        // Update verification status
        db.run(
          'UPDATE users SET email_verified = 1 WHERE id = ?',
          [user.id],
          function(updateErr) {
            if (updateErr) {
              console.error('Error updating verification status after Firebase verification:', updateErr);
              return res.status(500).json({ message: 'Error updating verification status', error: updateErr.message });
            }

            console.log(`User ${user.id} (${verifiedEmail}) verified successfully via Firebase`);
            res.json({
              message: 'Email verified successfully',
              user: {
                ...user,
                email_verified: 1
              }
            });
          }
        );
      });
    } catch (firebaseError) {
      console.error('Firebase verification error:', firebaseError);

      // Check if it's an invalid code error
      if (firebaseError.code === 'auth/invalid-action-code' ||
          firebaseError.code === 'auth/expired-action-code') {
        return res.status(400).json({
          message: 'Invalid or expired verification link',
          error: firebaseError.message
        });
      }

      return res.status(500).json({
        message: 'Firebase verification failed',
        error: firebaseError.message
      });
    }
  } catch (error) {
    console.error('Error in Firebase email verification:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// All verification and password reset routes removed

// Export the authenticateToken middleware and router
module.exports = { router, authenticateToken };