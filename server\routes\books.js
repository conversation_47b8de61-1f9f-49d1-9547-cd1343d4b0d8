const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const axios = require('axios'); // Add axios for external API requests
const { authenticateToken } = require('../middleware/auth');
const { isAdmin } = require('../middleware/admin');
const { check, validationResult } = require('express-validator');
const moment = require('moment');

// Remove the global authentication middleware since we'll apply it selectively
// router.use(authenticateToken);

// File upload configuration
// Configure storage for book cover images
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    // Use the server/uploads/book_covers directory, consistent with index.js
    const uploadDir = path.join(__dirname, '../uploads/book_covers'); // Corrected path
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    // Prepend 'book-cover-' to match the filename format in index.js (optional but good for consistency)
    cb(null, 'book-cover-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// Create the multer upload object
const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // Increased limit to 10MB
  fileFilter: function(req, file, cb) {
    // Accept only image files
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Get all books with optional filtering (public route)
router.get('/', (req, res) => {
  console.log('GET /books route handler called - req.path:', req.path);
  console.log('Full URL:', req.originalUrl);
  console.log('Query parameters:', req.query);
  
  try {
    const db = req.app.locals.db;
    const { title, author, category, available } = req.query;
    
    let query = 'SELECT * FROM books';
    const params = [];
    const conditions = [];
    
    // Add filters if provided
    if (title) {
      conditions.push('title LIKE ?');
      params.push(`%${title}%`);
    }
    
    if (author) {
      conditions.push('author LIKE ?');
      params.push(`%${author}%`);
    }
    
    if (category) {
      conditions.push('category = ?');
      params.push(category);
    }
    
    if (available === 'true') {
      conditions.push('available_copies > 0');
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    db.all(query, params, (err, books) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }
      
      // Return the books array directly for consistency
      res.json(books);
    });
  } catch (error) {
    console.error('Error in book creation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get book categories (public route)
router.get('/categories', async (req, res) => {
  try {
    console.log('Fetching all book categories');
    
    const db = req.app.locals.db;
    
    // Check if categories table exists
    const tableExists = await new Promise((resolve, reject) => {
      db.get(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='categories'",
        (err, row) => {
          if (err) return reject(err);
          resolve(!!row);
        }
      );
    });
    
    if (tableExists) {
      // Fetch from dedicated categories table
      const categories = await new Promise((resolve, reject) => {
        db.all('SELECT * FROM categories ORDER BY name', (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        });
      });
      
      console.log(`Found ${categories.length} categories`);
      res.json(categories);
    } else {
      // Fallback to old method (for backward compatibility)
      const booksHasCategory = await new Promise((resolve, reject) => {
        req.app.locals.db.all(
          "PRAGMA table_info(books)",
          function(err, rows) {
            if (err) return reject(err);
            resolve(rows.some(col => col.name === 'category'));
          }
        );
      });
      
      if (booksHasCategory) {
        const categories = await new Promise((resolve, reject) => {
          req.app.locals.db.all(
            "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category <> ''",
            (err, rows) => {
              if (err) return reject(err);
              resolve(rows.map(row => row.category).filter(Boolean));
            }
          );
        });
        
        console.log(`Found ${categories.length} categories from books table`);
        res.json(categories.map((name, index) => ({ id: index + 1, name })));
      } else {
        console.log('No categories found');
        res.json([]);
      }
    }
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ 
      message: 'Server error fetching categories', 
      error: error.message 
    });
  }
});

// Get a single book by ID (public route)
router.get('/:id', (req, res) => {
  try {
    const db = req.app.locals.db;
    
    db.get('SELECT * FROM books WHERE id = ?', [req.params.id], (err, book) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }
      
      if (!book) {
        return res.status(404).json({ message: 'Book not found' });
      }
      
      // Return the book object directly for consistency
      res.json(book);
    });
  } catch (error) {
    console.error('Error in book creation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// All admin routes below this point should use authentication
// Apply authentication middleware only to admin routes

// Add a new book (admin only)
router.post('/', authenticateToken, isAdmin, upload.single('coverFile'), async (req, res) => {
  try {
    const db = req.app.locals.db;
    console.log("Adding Book - Body:", req.body);
    console.log("Adding Book - File:", req.file);

    // Destructure known fields from req.body
    // Handle data potentially nested under a 'book' key if sent via FormData
    const bookData = req.body.book ? JSON.parse(req.body.book) : req.body;

    const {
      title,
      author,
      isbn,
      category,
      newCategoryName, // Expect this if category === 'new_category'
      description,
      cover_image, // May contain a URL if no file uploaded
      total_copies,
      available_copies, // Added available_copies
      publisher,
      published_year
    } = bookData;

    // Validate required fields
    const errors = validationResult(req); // Reuse express-validator if set up, or add manual checks
    if (!title || !author) {
        // If validation fails, and we potentially uploaded a file, delete it
        if (req.file) {
            fs.unlink(req.file.path, (err) => {
                if (err) console.error("Error deleting uploaded file on validation fail:", err);
            });
        }
        return res.status(400).json({ message: 'Title and author are required' });
    }

    // Determine the final category name
    let finalCategory = category;
    if (category === 'new_category' && newCategoryName) {
      finalCategory = newCategoryName.trim();
    }

    // Determine the final cover image path/URL
    let finalCoverImage = cover_image; // Use provided URL/value by default
    if (req.file) {
      // If a file was uploaded by multer, use its path
      // TODO: Add MIME type verification here for req.file.path
      // e.g., using a library like 'file-type' to check magic bytes.
      // If validation fails, fs.unlink(req.file.path, (err) => { if (err) console.error('Error deleting invalid file', err); }) and return an error response.
      finalCoverImage = `/uploads/book_covers/${req.file.filename}`;
      console.log("Using uploaded file path for new book:", finalCoverImage);
    }

    // Generate an ISBN if not provided (consider if ISBN should be mandatory or truly unique)
    const bookIsbn = isbn || `GEN-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

    // Use the provided total_copies or default to 1, ensure it's a number
    const numTotalCopies = parseInt(total_copies, 10) || 1;
    // Use provided available_copies, default to total_copies if not provided or invalid
    let numAvailableCopies = parseInt(available_copies, 10);
    if (isNaN(numAvailableCopies) || numAvailableCopies < 0 || numAvailableCopies > numTotalCopies) {
        numAvailableCopies = numTotalCopies;
    }

    // First, check if updated_at column exists in the books table
    const hasUpdatedAt = await new Promise((resolve) => {
      db.all("PRAGMA table_info(books)", (err, columns) => {
        if (err) {
          console.error("Error checking table schema:", err);
          resolve(false);
          return;
        }
        // Check if updated_at column exists
        const exists = columns.some(col => col.name === 'updated_at');
        resolve(exists);
      });
    });

    // Add the book to the database with query adjusted based on schema
    let insertQuery, insertParams;
    
    if (hasUpdatedAt) {
      // If updated_at column exists, use it
      insertQuery = `
        INSERT INTO books
        (isbn, title, author, description, category, cover_image,
         total_copies, available_copies, publisher, published_year,
         created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `;
      insertParams = [
        bookIsbn,
        title,
        author,
        description || null,
        finalCategory || 'Uncategorized',
        finalCoverImage || null,
        numTotalCopies,
        numAvailableCopies,
        publisher || null,
        published_year || null
      ];
    } else {
      // If updated_at column doesn't exist, omit it
      insertQuery = `
        INSERT INTO books
        (isbn, title, author, description, category, cover_image,
         total_copies, available_copies, publisher, published_year,
         created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
      `;
      insertParams = [
        bookIsbn,
        title,
        author,
        description || null,
        finalCategory || 'Uncategorized',
        finalCoverImage || null,
        numTotalCopies,
        numAvailableCopies,
        publisher || null,
        published_year || null
      ];
    }

    const result = await new Promise((resolve, reject) => {
      db.run(insertQuery, insertParams, function(err) {
        if (err) {
          // If insert fails (e.g., UNIQUE constraint), and we uploaded a file, delete it
          if (req.file) {
            fs.unlink(req.file.path, (err) => {
              if (err) console.error("Error deleting uploaded file on DB insert fail:", err);
            });
          }
          return reject(err);
        }
        resolve({ id: this.lastID });
      });
    });

    // Get the newly added book
    const book = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM books WHERE id = ?',
        [result.id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });

    res.status(201).json({
      message: 'Book added successfully',
      book
    });

  } catch (err) {
    console.error('Error adding book:', err);
    // Attempt to delete file if exists and an error occurred after potential upload but before response
    if (req.file && !res.headersSent) { // Check headersSent to avoid acting after response
        fs.unlink(req.file.path, (unlinkErr) => {
            if (unlinkErr) console.error("Error deleting uploaded file on general error:", unlinkErr);
        });
    }

    // Check for duplicate ISBN specifically
    if (err.message && err.message.includes('UNIQUE constraint failed')) {
      // Ensure response is sent only once
      if (!res.headersSent) {
        return res.status(400).json({ message: 'A book with this ISBN already exists' });
      }
    }

    // General server error
    if (!res.headersSent) {
        res.status(500).json({ message: 'Server error adding book', error: err.message });
    }
  }
});

// Update a book (admin only)
router.put('/:id', authenticateToken, isAdmin, upload.single('coverFile'), async (req, res) => {
  try {
    const db = req.app.locals.db;
    const bookId = req.params.id;
    console.log("Request Body:", req.body); // Log body
    console.log("Request File:", req.file); // Log file info if uploaded

    // Destructure known fields from req.body
    // Use 'book' object if data is nested, otherwise use direct fields
    const bookData = req.body.book ? JSON.parse(req.body.book) : req.body; 

    const {
      title,
      author,
      isbn,
      category,
      newCategoryName, // Expect this if category === 'new_category'
      description,
      cover_image, // This might be the existing URL or null if uploading new
      total_copies,
      available_copies,
      published_year,
      publisher
    } = bookData;

    // Determine the final category name
    let finalCategory = category;
    if (category === 'new_category' && newCategoryName) {
        finalCategory = newCategoryName.trim();
        // Optional: Add validation for new category name if needed
    }

    // Determine the final cover image path/URL
    let finalCoverImage = cover_image; // Keep existing URL by default
    if (req.file) {
      // If a file was uploaded by multer, use its path (relative to server root)
      // Ensure the path starts with /uploads/book_covers/ to match static serving
      // TODO: Add MIME type verification here for req.file.path
      // e.g., using a library like 'file-type' to check magic bytes.
      // If validation fails, fs.unlink(req.file.path, (err) => { if (err) console.error('Error deleting invalid file', err); }) and return an error response.
      finalCoverImage = `/uploads/book_covers/${req.file.filename}`;
      console.log("Using uploaded file path:", finalCoverImage);
    } else {
      console.log("No new file uploaded, keeping existing cover_image:", finalCoverImage);
    }


    // Validate required fields
    if (!title || !author) {
      // If validation fails, and we uploaded a file, delete it
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
            if (err) console.error("Error deleting uploaded file on validation fail:", err);
        });
      }
      return res.status(400).json({ message: 'Title and author are required' });
    }
    
    // --- Database Update Logic ---
    // Use async/await for cleaner database operations
    const book = await new Promise((resolve, reject) => {
       db.get('SELECT * FROM books WHERE id = ?', [bookId], (err, row) => {
         if (err) return reject(new Error(`Database error checking book: ${err.message}`));
         if (!row) return reject({ status: 404, message: 'Book not found' });
         resolve(row);
       });
    });

    // If ISBN is changed, check for conflicts
    if (isbn && isbn !== book.isbn) {
       const existingBook = await new Promise((resolve, reject) => {
         db.get('SELECT id FROM books WHERE isbn = ? AND id != ?', [isbn, bookId], (err, row) => {
           if (err) return reject(new Error(`Database error checking ISBN: ${err.message}`));
           resolve(row);
         });
       });
       if (existingBook) {
          // If validation fails, and we uploaded a file, delete it
          if (req.file) {
            fs.unlink(req.file.path, (err) => {
              if (err) console.error("Error deleting uploaded file on ISBN conflict:", err);
            });
          }
          return res.status(400).json({ message: 'Another book with this ISBN already exists' });
       }
    }

    // Perform the update
    const query = `
      UPDATE books SET
        title = ?,
        author = ?,
        isbn = ?,
        category = ?,
        description = ?,
        cover_image = ?,
        total_copies = ?,
        available_copies = ?,
        published_year = ?,
        publisher = ?,
        updated_at = datetime('now')
      WHERE id = ?
    `;

    // Ensure copies are numbers, provide defaults if necessary
    const numTotalCopies = parseInt(total_copies, 10) || 1;
    // Ensure available_copies is not greater than total_copies
    let numAvailableCopies = parseInt(available_copies, 10);
    if (isNaN(numAvailableCopies) || numAvailableCopies < 0) {
        numAvailableCopies = 0; // Default to 0 if invalid
    }
    if (numAvailableCopies > numTotalCopies) {
        numAvailableCopies = numTotalCopies; // Cap at total copies
    }


    await new Promise((resolve, reject) => {
       db.run(
         query,
         [
           title,
           author,
           isbn || book.isbn, // Keep original ISBN if not provided
           finalCategory || book.category, // Use determined category or keep original
           description || book.description,
           finalCoverImage, // Use determined cover image path/URL
           numTotalCopies,
           numAvailableCopies,
           published_year || book.published_year,
           publisher || book.publisher,
           bookId
         ],
         function(err) {
           if (err) return reject(new Error(`Error updating book: ${err.message}`));
           if (this.changes === 0) return reject({ status: 404, message: 'Book not found or no changes made' }); // Should not happen if check passed, but good practice
           resolve();
         }
       );
    });

    // Get the updated book to return
    const updatedBook = await new Promise((resolve, reject) => {
        db.get('SELECT * FROM books WHERE id = ?', [bookId], (err, row) => {
          if (err) return reject(new Error(`Error retrieving updated book: ${err.message}`));
          resolve(row);
        });
    });

    res.json({
      message: 'Book updated successfully',
      book: updatedBook
    });

  } catch (error) {
    console.error('Error updating book:', error);
    // If an error occurred after file upload, attempt to delete the file
    if (req.file) {
        fs.unlink(req.file.path, (err) => {
            if (err) console.error("Error deleting uploaded file on main error:", err);
        });
    }
    // Send specific status codes if available from rejected promises
    if (error.status) {
      return res.status(error.status).json({ message: error.message });
    }
    res.status(500).json({ message: 'Server error updating book', error: error.message });
  }
});

// Delete a book (admin only)
router.delete('/:id', authenticateToken, isAdmin, (req, res) => {
  try {
    const db = req.app.locals.db;
    const bookId = req.params.id;
    
    // Check if book exists
    db.get('SELECT * FROM books WHERE id = ?', [bookId], (err, book) => {
      if (err) {
        return res.status(500).json({ message: 'Database error', error: err.message });
      }
      
      if (!book) {
        return res.status(404).json({ message: 'Book not found' });
      }
      
      // Check if book has active leases
      db.get('SELECT COUNT(*) as count FROM leases WHERE book_id = ? AND status = "active"', [bookId], (err, result) => {
        if (err) {
          return res.status(500).json({ message: 'Database error', error: err.message });
        }
        
        if (result.count > 0) {
          return res.status(400).json({ message: 'Cannot delete book with active leases' });
        }
        
        // Delete the book
        db.run('DELETE FROM books WHERE id = ?', [bookId], function(err) {
          if (err) {
            return res.status(500).json({ message: 'Error deleting book', error: err.message });
          }
          
          res.json({ message: 'Book deleted successfully' });
        });
      });
    });
  } catch (error) {
    console.error('Error in book creation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST api/books/import
// @desc    Import books from CSV file
// @access  Admin
router.post('/import', authenticateToken, isAdmin, upload.single('file'), async (req, res) => {
  try {
    console.log('Importing books from CSV');
    
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }
    
    // Verify file type
    if (!req.file.mimetype.includes('csv') && !req.file.mimetype.includes('text/plain')) {
      return res.status(400).json({ message: 'File must be CSV format' });
    }
    
    // Read the CSV file
    const csvData = fs.readFileSync(req.file.path, 'utf8');
    
    // Parse CSV - handle different line endings (CRLF, LF)
    const rows = csvData.replace(/\r\n/g, '\n').split('\n').filter(row => row.trim());
    
    if (rows.length < 2) {
      return res.status(400).json({ message: 'CSV file must contain a header row and at least one data row' });
    }
    
    // Get header row and normalize column names
    const headers = rows[0].split(',')
      .map(header => header.trim().toLowerCase())
      .map(header => {
        // Normalize common headers
        if (['book title', 'booktitle', 'name'].includes(header)) return 'title';
        if (['book author', 'bookauthor', 'writer'].includes(header)) return 'author';
        if (['book isbn', 'bookisbn', 'isbn-13', 'isbn13'].includes(header)) return 'isbn';
        if (['year', 'publication year', 'pub year', 'publication_year'].includes(header)) return 'published_year';
        if (['publisher name', 'publishername'].includes(header)) return 'publisher';
        if (['copies', 'quantity', 'count'].includes(header)) return 'total_copies';
        return header;
      });
    
    // Check for required columns
    if (!headers.includes('title') || !headers.includes('author')) {
      return res.status(400).json({ 
        message: 'CSV must include title and author columns. Found columns: ' + headers.join(', ')
      });
    }
    
    let imported = 0;
    let errors = [];
    let warnings = [];
    
    // Begin transaction
    await new Promise((resolve, reject) => {
      req.app.locals.db.run('BEGIN TRANSACTION', err => {
        if (err) return reject(err);
        resolve();
      });
    });
    
    try {
      // Process each data row
      for (let i = 1; i < rows.length; i++) {
        if (!rows[i].trim()) continue; // Skip empty rows
        
        // Handle quoted values with commas inside them
        let values = [];
        let currentValue = '';
        let inQuotes = false;
        
        for (let char of rows[i]) {
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            values.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        
        // Don't forget to add the last value
        values.push(currentValue.trim());
        
        // Clean up values - remove quotes
        values = values.map(value => {
          if (value.startsWith('"') && value.endsWith('"')) {
            return value.substring(1, value.length - 1).trim();
          }
          return value.trim();
        });
        
        // Handle case where we have fewer values than headers
        if (values.length < headers.length) {
          // Pad with empty strings
          while (values.length < headers.length) {
            values.push('');
          }
          warnings.push(`Row ${i+1}: Contains fewer columns than expected. Added empty values.`);
        } 
        // Handle case where we have more values than headers
        else if (values.length > headers.length) {
          values = values.slice(0, headers.length);
          warnings.push(`Row ${i+1}: Contains more columns than expected. Extra values ignored.`);
        }
        
        // Create book object from row
        const book = {};
        headers.forEach((header, index) => {
          book[header] = values[index];
        });
        
        // Check required fields
        if (!book.title || !book.author) {
          errors.push(`Row ${i+1}: Missing title or author`);
          continue;
        }
        
        // Validate and clean up book data
        
        // Title and author: capitalize first letter of each word
        book.title = book.title
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        book.author = book.author
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        // ISBN: generate if not provided
        if (!book.isbn) {
          book.isbn = `IMP-${Date.now()}-${i}`;
          warnings.push(`Row ${i+1}: Generated ISBN ${book.isbn} as none was provided`);
        } else {
          // Clean ISBN - remove dashes and spaces
          book.isbn = book.isbn.replace(/[-\s]/g, '');
        }
        
        // Category: use default if not provided
        if (!book.category || book.category.trim() === '') {
          book.category = 'Uncategorized';
          warnings.push(`Row ${i+1}: Used default category 'Uncategorized'`);
        }
        
        // Copies: set defaults and ensure they are numbers
        let totalCopies = book.total_copies ? parseInt(book.total_copies, 10) : 1;
        if (isNaN(totalCopies) || totalCopies < 1) {
          totalCopies = 1;
          warnings.push(`Row ${i+1}: Invalid total_copies value. Using default of 1.`);
        }
        
        let availableCopies = book.available_copies ? parseInt(book.available_copies, 10) : totalCopies;
        if (isNaN(availableCopies) || availableCopies < 0 || availableCopies > totalCopies) {
          availableCopies = totalCopies;
          warnings.push(`Row ${i+1}: Invalid available_copies value. Using total copies (${totalCopies}).`);
        }
        
        // Published year: ensure it's a valid year
        let publishedYear = book.published_year ? parseInt(book.published_year, 10) : null;
        if (book.published_year && (isNaN(publishedYear) || publishedYear < 1000 || publishedYear > new Date().getFullYear())) {
          publishedYear = null;
          warnings.push(`Row ${i+1}: Invalid published_year value. Field cleared.`);
        }
        
        try {
          // Check if book already exists by ISBN
          const existingBook = await new Promise((resolve, reject) => {
            req.app.locals.db.get(
              'SELECT id FROM books WHERE isbn = ?',
              [book.isbn],
              (err, row) => {
                if (err) return reject(err);
                resolve(row);
              }
            );
          });
          
          if (existingBook) {
            // Update existing book
            await new Promise((resolve, reject) => {
              req.app.locals.db.run(
                `UPDATE books SET 
                 title = ?, author = ?, description = ?, category = ?,
                 total_copies = ?, available_copies = ?, publisher = ?, 
                 published_year = ?, updated_at = datetime('now')
                 WHERE id = ?`,
                [
                  book.title,
                  book.author,
                  book.description || null,
                  book.category || 'Uncategorized',
                  totalCopies,
                  availableCopies,
                  book.publisher || null,
                  publishedYear,
                  existingBook.id
                ],
                function(err) {
                  if (err) return reject(err);
                  resolve();
                }
              );
            });
            
            warnings.push(`Row ${i+1}: Updated existing book with ISBN ${book.isbn}`);
          } else {
            // Insert new book
            await new Promise((resolve, reject) => {
              req.app.locals.db.run(
                `INSERT INTO books 
                 (isbn, title, author, description, category, 
                  total_copies, available_copies, publisher, published_year, 
                  created_at, updated_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
                [
                  book.isbn,
                  book.title,
                  book.author,
                  book.description || null,
                  book.category || 'Uncategorized',
                  totalCopies,
                  availableCopies,
                  book.publisher || null,
                  publishedYear
                ],
                function(err) {
                  if (err) return reject(err);
                  resolve();
                }
              );
            });
          }
          
          imported++;
        } catch (err) {
          errors.push(`Row ${i+1}: ${err.message}`);
        }
      }
      
      // Commit transaction
      await new Promise((resolve, reject) => {
        req.app.locals.db.run('COMMIT', err => {
          if (err) return reject(err);
          resolve();
        });
      });
      
      // Delete uploaded file
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting uploaded file:', err);
      });
      
      res.json({
        success: true,
        message: `Imported ${imported} books successfully`,
        imported,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined
      });
    } catch (err) {
      // Rollback on error
      await new Promise(resolve => {
        req.app.locals.db.run('ROLLBACK', () => resolve());
      });
      
      // Delete uploaded file
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting uploaded file:', err);
      });
      
      throw err;
    }
  } catch (err) {
    console.error('Error importing books:', err.message);
    res.status(500).json({ message: 'Server error importing books', error: err.message });
  }
});

// Add a new category (admin only)
router.post('/categories', authenticateToken, isAdmin, [
  check('name', 'Category name is required').not().isEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name } = req.body;
    const db = req.app.locals.db;

    // Check if category already exists
    const existingCategory = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM categories WHERE name = ?', [name], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });

    if (existingCategory) {
      return res.status(400).json({ message: 'Category already exists' });
    }

    // Create categories table if it doesn't exist
    await new Promise((resolve, reject) => {
      db.run(`CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`, (err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    // Insert new category
    const result = await new Promise((resolve, reject) => {
      db.run('INSERT INTO categories (name) VALUES (?)', [name], function(err) {
        if (err) return reject(err);
        resolve({ id: this.lastID });
      });
    });

    res.status(201).json({
      id: result.id,
      name,
      message: 'Category added successfully'
    });
  } catch (error) {
    console.error('Error adding category:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update a category (admin only)
router.put('/categories/:id', authenticateToken, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    
    if (!name || name.trim() === '') {
      return res.status(400).json({ message: 'Category name is required' });
    }
    
    const db = req.app.locals.db;
    
    // Check if category exists
    const category = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM categories WHERE id = ?', [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Update category
    await new Promise((resolve, reject) => {
      db.run('UPDATE categories SET name = ? WHERE id = ?', [name, id], function(err) {
        if (err) return reject(err);
        resolve();
      });
    });
    
    // Also update all books with this category
    await new Promise((resolve, reject) => {
      db.run('UPDATE books SET category = ? WHERE category = ?', [name, category.name], function(err) {
        if (err) return reject(err);
        resolve();
      });
    });
    
    res.json({
      id: parseInt(id),
      name,
      message: 'Category updated successfully'
    });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete a category (admin only)
router.delete('/categories/:id', authenticateToken, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const db = req.app.locals.db;
    
    // Check if category exists
    const category = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM categories WHERE id = ?', [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Delete category
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM categories WHERE id = ?', [id], function(err) {
        if (err) return reject(err);
        resolve();
      });
    });
    
    // Update books with this category to 'Uncategorized'
    await new Promise((resolve, reject) => {
      db.run('UPDATE books SET category = ? WHERE category = ?', ['Uncategorized', category.name], function(err) {
        if (err) return reject(err);
        resolve();
      });
    });
    
    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;