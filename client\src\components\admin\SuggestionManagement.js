import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Divider,
  Stack,
  IconButton,
  Alert,
  Snackbar,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  useTheme,
  useMediaQuery,
  Badge
} from '@mui/material';
import {
  Check as ApproveIcon,
  Close as RejectIcon,
  Book as BookIcon,
  Search as SearchIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import api from '../../utils/api';
import moment from 'moment';
import { useLanguage } from '../../context/LanguageContext';

const SuggestionManagement = () => {
  const { translate } = useLanguage();

  // Initialize suggestions as an empty array to prevent filtering errors
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  // Removed copies state since we're not adding books anymore
  const [rejectionReason, setRejectionReason] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filter, setFilter] = useState('all');

  // Use the theme and media query for responsive design
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Fetch suggestions on component mount and when filter changes
  useEffect(() => {
    fetchSuggestions(filter);
  }, [filter]);

  // Fetch suggestions from API
  const fetchSuggestions = async (filterValue = 'all') => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.direct.get(`/api/suggestions?status=${filterValue}`);
      console.log('Suggestions API response:', response);

      // Ensure suggestions is always an array
      const suggestionsData = response.data?.suggestions || response.data || [];
      const suggestionsArray = Array.isArray(suggestionsData) ? suggestionsData : [];

      console.log('Parsed suggestions:', suggestionsArray);
      setSuggestions(suggestionsArray);
    } catch (err) {
      console.error('Error fetching suggestions:', err);
      setError(translate('Failed to load suggestions'));
      setSuggestions([]); // Set to empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Open suggestion details dialog
  const handleDetailsOpen = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setDetailsOpen(true);
  };

  // Close suggestion details dialog
  const handleDetailsClose = () => {
    setDetailsOpen(false);
    setSelectedSuggestion(null);
  };

  // Open approve dialog
  const handleApproveOpen = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setApproveDialogOpen(true);
  };

  // Close approve dialog
  const handleApproveClose = () => {
    setApproveDialogOpen(false);
    // Removed setCopies call since we removed the copies state
  };

  // Open reject dialog
  const handleRejectOpen = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setRejectDialogOpen(true);
  };

  // Close reject dialog
  const handleRejectClose = () => {
    setRejectDialogOpen(false);
    setRejectionReason('');
  };

  // Handle approve suggestion
  const handleApproveSuggestion = async () => {
    try {
      await api.direct.put(`/api/suggestions/${selectedSuggestion.id}/approve`);

      // Update local state
      setSuggestions(suggestions.map(suggestion =>
        suggestion.id === selectedSuggestion.id
          ? { ...suggestion, status: 'approved' }
          : suggestion
      ));

      setSuccessMessage(translate('Book suggestion approved successfully!'));
      setShowSuccess(true);
      handleApproveClose();
    } catch (err) {
      console.error('Error approving suggestion:', err);
      setError(translate('Failed to approve suggestion. Please try again.'));
    }
  };

  // Handle reject suggestion
  const handleRejectSuggestion = async () => {
    try {
      await api.direct.put(`/api/suggestions/${selectedSuggestion.id}/reject`, {
        reason: rejectionReason
      });

      // Update local state
      setSuggestions(suggestions.map(suggestion =>
        suggestion.id === selectedSuggestion.id
          ? { ...suggestion, status: 'rejected', rejection_reason: rejectionReason }
          : suggestion
      ));

      setSuccessMessage(translate('Book suggestion rejected.'));
      setShowSuccess(true);
      handleRejectClose();
    } catch (err) {
      console.error('Error rejecting suggestion:', err);
      setError(translate('Failed to reject suggestion. Please try again.'));
    }
  };

  // Handle delete suggestion
  const handleDeleteSuggestion = async (suggestionId) => {
    if (!window.confirm(translate('Are you sure you want to permanently delete this suggestion?'))) {
      return;
    }

    try {
      await api.direct.delete(`/api/suggestions/${suggestionId}`);

      // Update local state by removing the deleted suggestion
      setSuggestions(suggestions.filter(suggestion => suggestion.id !== suggestionId));

      setSuccessMessage(translate('Book suggestion deleted successfully.'));
      setShowSuccess(true);
    } catch (err) {
      console.error('Error deleting suggestion:', err);
      setError(translate('Failed to delete suggestion. Please try again.'));
    }
  };

  // Get status chip color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  // Handle page change
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(event.target.value);
    setPage(1);
  };

  // Handle filter change
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setPage(1);
  };

  // Filter suggestions based on status
  const filteredSuggestions = Array.isArray(suggestions)
    ? suggestions.filter(suggestion => {
        if (filter === 'all') return true;
        return suggestion.status === filter;
      })
    : [];

  // Calculate pagination
  const paginatedSuggestions = filteredSuggestions.slice(
    (page - 1) * rowsPerPage,
    page * rowsPerPage
  );

  // Get pending suggestions count for badge
  const pendingSuggestionsCount = Array.isArray(suggestions)
    ? suggestions.filter(s => s.status === 'pending').length
    : 0;

  // Show loading spinner
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error message
  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ maxWidth: '100%', overflow: 'hidden' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
        <Typography variant="h5" component="h2" gutterBottom sx={{ mr: 2 }}>
          {translate('Book Suggestions')}
          {pendingSuggestionsCount > 0 && (
            <Badge badgeContent={pendingSuggestionsCount} color="error" sx={{ ml: 2 }}>
              <BookIcon />
            </Badge>
          )}
        </Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={translate('Refresh suggestions')}>
            <IconButton onClick={() => fetchSuggestions(filter)} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="suggestion-filter-label">{translate('Filter')}</InputLabel>
            <Select
              labelId="suggestion-filter-label"
              id="suggestion-filter"
              value={filter}
              onChange={handleFilterChange}
              label={translate('Filter')}
            >
              <MenuItem value="all">{translate('All Suggestions')}</MenuItem>
              <MenuItem value="pending">{translate('Pending')}</MenuItem>
              <MenuItem value="approved">{translate('Approved')}</MenuItem>
              <MenuItem value="rejected">{translate('Rejected')}</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {suggestions.length === 0 ? (
        <Alert severity="info" sx={{ my: 2 }}>
          {translate('No book suggestions found.')}
        </Alert>
      ) : (
        <>
          <TableContainer component={Paper} sx={{ mb: 2 }}>
            <Table sx={{ minWidth: 650 }} aria-label="suggestions table">
              <TableHead>
                <TableRow>
                  <TableCell>{translate('ISBN')}</TableCell>
                  <TableCell>{translate('Title')}</TableCell>
                  {!isMobile && <TableCell>{translate('Author')}</TableCell>}
                  {!isMobile && <TableCell>{translate('Suggested By')}</TableCell>}
                  <TableCell>{translate('Date')}</TableCell>
                  <TableCell>{translate('Status')}</TableCell>
                  <TableCell align="right">{translate('Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedSuggestions.map((suggestion) => (
                  <TableRow
                    key={suggestion.id}
                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                  >
                    <TableCell>{suggestion.isbn}</TableCell>
                    <TableCell>
                      <Tooltip title={suggestion.title} arrow placement="top">
                        <Typography noWrap sx={{ maxWidth: isMobile ? 100 : 200 }}>
                          {suggestion.title}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    {!isMobile && <TableCell>{suggestion.author}</TableCell>}
                    {!isMobile && <TableCell>{suggestion.user_name}</TableCell>}
                    <TableCell>{moment(suggestion.created_at).format('MMM D, YYYY')}</TableCell>
                    <TableCell>
                      <Chip
                        label={suggestion.status.charAt(0).toUpperCase() + suggestion.status.slice(1)}
                        color={getStatusColor(suggestion.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleDetailsOpen(suggestion)}
                          aria-label="details"
                        >
                          <InfoIcon />
                        </IconButton>

                        {suggestion.status === 'pending' && (
                          <>
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleApproveOpen(suggestion)}
                              aria-label="approve"
                            >
                              <ApproveIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRejectOpen(suggestion)}
                              aria-label="reject"
                            >
                              <RejectIcon />
                            </IconButton>
                          </>
                        )}

                        {/* Delete button for all suggestions */}
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteSuggestion(suggestion.id)}
                          aria-label="delete"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
            <Pagination
              count={Math.ceil(filteredSuggestions.length / rowsPerPage)}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        </>
      )}

      {/* Suggestion Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={handleDetailsClose}
        maxWidth="md"
        fullWidth
      >
        {selectedSuggestion && (
          <>
            <DialogTitle>
              {translate('Book Suggestion Details')}
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Card elevation={2}>
                    <CardMedia
                      component="img"
                      height={300}
                      image={selectedSuggestion.cover_image || '/placeholder-book.svg'}
                      alt={selectedSuggestion.title}
                      sx={{ objectFit: 'contain', bgcolor: '#f5f5f5' }}
                    />
                  </Card>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Typography variant="h5" gutterBottom>
                    {selectedSuggestion.title}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                    {translate('by')} {selectedSuggestion.author}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', my: 2 }}>
                    <Chip
                      label={`ISBN: ${selectedSuggestion.isbn}`}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      label={selectedSuggestion.status.charAt(0).toUpperCase() + selectedSuggestion.status.slice(1)}
                      color={getStatusColor(selectedSuggestion.status)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body1" paragraph>
                    <strong>{translate('Suggested by:')}</strong> {selectedSuggestion.user_name}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    <strong>{translate('Date suggested:')}</strong> {moment(selectedSuggestion.created_at).format('MMMM D, YYYY')}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="body2" paragraph sx={{ whiteSpace: 'pre-line' }}>
                    {selectedSuggestion.description || translate("No description available.")}
                  </Typography>

                  {selectedSuggestion.status === 'rejected' && selectedSuggestion.rejection_reason && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="subtitle2">{translate('Rejection Reason:')}</Typography>
                      {selectedSuggestion.rejection_reason}
                    </Alert>
                  )}
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDetailsClose}>{translate('Close')}</Button>
              {selectedSuggestion.status === 'pending' && (
                <>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => {
                      handleDetailsClose();
                      handleRejectOpen(selectedSuggestion);
                    }}
                    startIcon={<RejectIcon />}
                  >
                    {translate('Reject')}
                  </Button>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => {
                      handleDetailsClose();
                      handleApproveOpen(selectedSuggestion);
                    }}
                    startIcon={<ApproveIcon />}
                  >
                    {translate('Approve')}
                  </Button>
                </>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Approve Dialog */}
      <Dialog
        open={approveDialogOpen}
        onClose={handleApproveClose}
        maxWidth="sm"
        fullWidth
      >
        {selectedSuggestion && (
          <>
            <DialogTitle>
              Approve Book Suggestion
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                You are about to approve the following book suggestion:
              </Typography>
              <Typography variant="h6" gutterBottom>
                {selectedSuggestion.title}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                by {selectedSuggestion.author}
              </Typography>
              <Typography variant="body2" gutterBottom>
                ISBN: {selectedSuggestion.isbn}
              </Typography>

              <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                This will mark the suggestion as approved. It will be visible in the approved suggestions list.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleApproveClose}>Cancel</Button>
              <Button
                variant="contained"
                color="success"
                onClick={handleApproveSuggestion}
              >
                Approve Suggestion
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Reject Dialog */}
      <Dialog
        open={rejectDialogOpen}
        onClose={handleRejectClose}
        maxWidth="sm"
        fullWidth
      >
        {selectedSuggestion && (
          <>
            <DialogTitle>
              Reject Book Suggestion
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                You are about to reject the following book suggestion:
              </Typography>
              <Typography variant="h6" gutterBottom>
                {selectedSuggestion.title}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                by {selectedSuggestion.author}
              </Typography>

              <TextField
                margin="normal"
                label="Rejection Reason (Optional)"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                helperText="This message will be visible to the user who suggested the book"
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={handleRejectClose}>Cancel</Button>
              <Button
                variant="contained"
                color="error"
                onClick={handleRejectSuggestion}
              >
                Reject Suggestion
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Success message */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={() => setShowSuccess(false)}
      >
        <Alert
          onClose={() => setShowSuccess(false)}
          severity="success"
          variant="filled"
        >
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SuggestionManagement;
