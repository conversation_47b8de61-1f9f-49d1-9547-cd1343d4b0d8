import React from 'react';
import { Box, Container, Typography, Link, Grid } from '@mui/material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../context/LanguageContext';

const Footer = () => {
  const { translate } = useLanguage();
  
  return (
    <motion.div
      initial={{ y: 50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box
        component="footer"
        sx={{
          py: 3,
          px: 2,
          mt: 'auto',
          backgroundColor: 'background.paper',
          borderTop: '1px solid',
          borderColor: 'divider',
          color: 'text.secondary',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={3} justifyContent="space-between" alignItems="flex-start">
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom sx={{ color: 'text.primary' }}>
                {translate('School Library')}
              </Typography>
              <Typography variant="body2">
                {translate('Praha EDUCAnet Library System')}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                {translate('Official Site:')} <Link href="http://www.praha.educanet.cz" target="_blank" rel="noopener noreferrer" color="primary">www.praha.educanet.cz</Link>
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom sx={{ color: 'text.primary' }}>
                {translate('Opening Hours')}
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Typography component="li" variant="body2">{translate('Monday')}: 10:10 - 10:30</Typography>
                <Typography component="li" variant="body2">{translate('Tuesday')}: {translate('Closed')}</Typography>
                <Typography component="li" variant="body2">{translate('Wednesday')}: 14:30 - 15:00</Typography>
                <Typography component="li" variant="body2">{translate('Thursday')}: {translate('Closed')}</Typography>
                <Typography component="li" variant="body2">{translate('Friday')}: 10:10 - 10:30</Typography>
              </Box>
            </Grid>
          </Grid>
          <Box mt={3}>
            <Typography variant="body2" align="center">
              {'© '}
              {new Date().getFullYear()}
              {' '}
              {translate('EDUCAnet Praha. All rights reserved.')}
            </Typography>
          </Box>
        </Container>
      </Box>
    </motion.div>
  );
};

export default Footer;