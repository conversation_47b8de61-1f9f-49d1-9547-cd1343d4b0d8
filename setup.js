const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create .env file if it doesn't exist
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('Creating .env file...');
  fs.writeFileSync(
    envPath,
    'GOOGLE_BOOKS_API_KEY=AIzaSyD5aSwx9Lb7uuwBLBsBGxZTjeh2K37xgvQ\nPORT=5000\n'
  );
  console.log('.env file created');
}

// Function to run commands
function runCommand(command, args, cwd) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')} in ${cwd}`);
    
    const proc = spawn(command, args, {
      cwd,
      shell: true,
      stdio: 'inherit'
    });
    
    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });
  });
}

// Setup server
async function setupServer() {
  const serverDir = path.join(__dirname, 'server');
  
  // Create server directory if it doesn't exist
  if (!fs.existsSync(serverDir)) {
    fs.mkdirSync(serverDir, { recursive: true });
  }
  
  console.log('Installing server dependencies...');
  try {
    await runCommand('npm', ['install'], serverDir);
    console.log('Server dependencies installed');
  } catch (error) {
    console.error('Failed to install server dependencies:', error);
    process.exit(1);
  }
}

// Setup client
async function setupClient() {
  const clientDir = path.join(__dirname, 'client');
  
  // Create client directory if it doesn't exist
  if (!fs.existsSync(clientDir)) {
    fs.mkdirSync(clientDir, { recursive: true });
  }
  
  console.log('Installing client dependencies...');
  try {
    await runCommand('npm', ['install'], clientDir);
    console.log('Client dependencies installed');
  } catch (error) {
    console.error('Failed to install client dependencies:', error);
    process.exit(1);
  }
}

// Start both server and client
async function startApplication() {
  const serverDir = path.join(__dirname, 'server');
  const clientDir = path.join(__dirname, 'client');
  
  console.log('Starting server and client...');
  
  // Start server in background
  const server = spawn('npm', ['run', 'dev'], {
    cwd: serverDir,
    shell: true,
    stdio: 'pipe',
    detached: true
  });
  
  server.stdout.on('data', (data) => {
    console.log(`[SERVER] ${data.toString().trim()}`);
  });
  
  server.stderr.on('data', (data) => {
    console.error(`[SERVER ERROR] ${data.toString().trim()}`);
  });
  
  // Wait a bit for server to start
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Start client
  const client = spawn('npm', ['start'], {
    cwd: clientDir,
    shell: true,
    stdio: 'inherit'
  });
  
  client.on('close', (code) => {
    console.log(`Client exited with code ${code}`);
    // Kill server when client exits
    process.kill(-server.pid);
    process.exit(0);
  });
}

// Main function
async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  try {
    // Setup
    await setupServer();
    await setupClient();
    
    rl.question('Setup complete! Start the application now? (y/n) ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        await startApplication();
      } else {
        console.log('You can start the server with: cd server && npm run dev');
        console.log('You can start the client with: cd client && npm start');
        rl.close();
      }
    });
  } catch (error) {
    console.error('Setup failed:', error);
    rl.close();
    process.exit(1);
  }
}

// Run the main function
main(); 