import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Checkbox,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import { motion } from 'framer-motion';
import api from '../../utils/api';
import LeaseDetailsDialog from './LeaseDetailsDialog';

const BulkLeaseActions = ({ books, leases, setLeases }) => {
  const [selectedLeases, setSelectedLeases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedLease, setSelectedLease] = useState(null);
  const [pendingLeases, setPendingLeases] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [processingError, setProcessingError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [sendEmails, setSendEmails] = useState(false);
  
  // Fetch pending leases when component mounts or leases prop changes
  useEffect(() => {
    const processLeases = () => {
      if (leases && leases.length > 0) {
        console.log('Processing leases from props:', leases);
        // Filter pending leases from the leases prop
        const filtered = leases.filter(lease => 
          lease.status === 'pending' && lease.approval_status === 'pending'
        );
        console.log('Filtered pending leases:', filtered);
        setPendingLeases(filtered);
      } else {
        // If no leases prop or empty, fetch pending leases directly
        fetchPendingLeases();
      }
    };

    processLeases();
    
    // Set up a refresh interval
    const refreshInterval = setInterval(() => {
      fetchPendingLeases();
    }, 30000); // Refresh every 30 seconds
    
    // Clean up interval on component unmount
    return () => clearInterval(refreshInterval);
  }, [leases]);
  
  // Fetch all pending leases
  const fetchPendingLeases = async () => {
    setLoading(true);
    setError('');
    
    try {
      const leasesRes = await api.direct.get('/api/leases', {
        params: { includeAll: true } // Request all leases including rejected, canceled, returned
      });
      
      // Ensure pendingLeases is always an array
      const pendingLeasesData = Array.isArray(leasesRes.data) ? leasesRes.data : 
                               (leasesRes.data?.leases && Array.isArray(leasesRes.data.leases)) ? leasesRes.data.leases : [];
      
      // Filter to only show pending leases initially
      const filteredLeases = pendingLeasesData.filter(lease => 
        lease.status === 'pending' && lease.approval_status === 'pending'
      );
      
      setPendingLeases(filteredLeases);
      
      // Create initial selection state based on leases
      const initialSelection = {};
      filteredLeases.forEach(lease => {
        initialSelection[lease.id] = false;
      });
      setSelectedLeases(initialSelection);
      
    } catch (err) {
      console.error('Error fetching pending leases:', err);
      setError('Failed to load pending leases');
      setPendingLeases([]); // Set to empty array on error
    } finally {
      setLoading(false);
    }
  };
  
  // Open details dialog
  const handleOpenDetailsDialog = (lease) => {
    setSelectedLease(lease);
    setDetailsDialogOpen(true);
  };
  
  // Close details dialog
  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false);
    setSelectedLease(null);
  };
  
  // Handle checkbox selection
  const handleSelectLease = (leaseId) => {
    setSelectedLeases(prev => {
      const newSelection = { ...prev };
      newSelection[leaseId] = !newSelection[leaseId];
      return newSelection;
    });
  };
  
  // Handle select all
  const handleSelectAll = (event) => {
    if (event.target.checked) {
      // Only select pending leases
      const pendingLeaseIds = leases
        .filter(lease => lease.approval_status === 'pending')
        .map(lease => lease.id);
      setSelectedLeases(pendingLeaseIds.reduce((acc, id) => ({ ...acc, [id]: true }), {}));
    } else {
      setSelectedLeases({});
    }
  };
  
  // Open action dialog
  const handleOpenActionDialog = () => {
    if (Object.keys(selectedLeases).length === 0) {
      setError('Please select at least one lease to perform an action');
      return;
    }
    setSelectedAction('');
    setRejectionReason('');
    setActionDialogOpen(true);
  };
  
  // Close action dialog
  const handleCloseActionDialog = () => {
    setActionDialogOpen(false);
  };
  
  // Execute bulk action
  const handleExecuteAction = async () => {
    setLoading(true);
    setError('');
    setSuccess('');
    
    try {
      if (selectedAction === 'approve') {
        await Promise.all(Object.keys(selectedLeases).filter(id => selectedLeases[id]).map(leaseId => {
          console.log(`Approving lease ${leaseId}`);
          return api.direct.put(`/api/leases/${leaseId}/approve`);
        }));
        setSuccess(`${Object.keys(selectedLeases).filter(id => selectedLeases[id]).length} lease requests approved successfully`);
      } else if (selectedAction === 'reject') {
        await Promise.all(Object.keys(selectedLeases).filter(id => selectedLeases[id]).map(leaseId => {
          console.log(`Rejecting lease ${leaseId} with reason: ${rejectionReason || 'Not provided'}`);
          return api.direct.put(`/api/leases/${leaseId}/reject`, { reason: rejectionReason || 'Rejected by administrator' });
        }));
        setSuccess(`${Object.keys(selectedLeases).filter(id => selectedLeases[id]).length} lease requests rejected successfully`);
      }
      
      // Clear selections
      setSelectedLeases({});
      setSelectedAction('');
      setRejectionReason('');
      setActionDialogOpen(false);
      
      // Force refresh of lease data
      fetchPendingLeases();
    } catch (err) {
      console.error('Error performing bulk action:', err);
      let errorMessage = 'Failed to perform bulk action. Please try again.';
      if (err.response && err.response.data) {
        console.log('Error response:', err.response.data);
        errorMessage = err.response.data.message || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // Process selected leases
  const processLeases = async () => {
    setProcessing(true);
    setProcessingError('');
    
    try {
      // Get IDs of selected leases
      const selectedIds = Object.keys(selectedLeases).filter(id => selectedLeases[id]);
      
      if (selectedIds.length === 0) {
        setProcessingError('No leases selected');
        return;
      }
      
      // Call API to process leases
      const response = await api.direct.post('/api/leases/bulk-action', {
        leaseIds: selectedIds,
        action: selectedAction,
        withEmail: sendEmails
      });
      
      setSuccessMessage(`Successfully processed ${selectedIds.length} leases`);
      
      // Refresh lease list
      fetchPendingLeases();
      
      // Reset selection
      setSelectedLeases({});
      
    } catch (err) {
      console.error('Error processing leases:', err);
      setProcessingError(err.response?.data?.message || 'Failed to process leases');
    } finally {
      setProcessing(false);
    }
  };
  
  return (
    <motion.div>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}
      
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" component="h2">
          Pending Lease Requests ({Array.isArray(pendingLeases) ? pendingLeases.length : 0})
        </Typography>
        
        <Button
          variant="contained"
          color="primary"
          disabled={Object.keys(selectedLeases).length === 0 || loading}
          onClick={handleOpenActionDialog}
        >
          Bulk Actions ({Object.keys(selectedLeases).length})
        </Button>
      </Box>
      
      {loading && (!Array.isArray(pendingLeases) || pendingLeases.length === 0) ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={Object.keys(selectedLeases).length > 0 && Object.keys(selectedLeases).length < (Array.isArray(pendingLeases) ? pendingLeases.length : 0)}
                    checked={Array.isArray(pendingLeases) && pendingLeases.length > 0 && Object.keys(selectedLeases).length === pendingLeases.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>Book</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Request Date</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(pendingLeases) && pendingLeases.length > 0 ? (
                pendingLeases.map((lease) => (
                  <TableRow key={lease.id}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedLeases[lease.id]}
                        onChange={() => handleSelectLease(lease.id)}
                      />
                    </TableCell>
                    <TableCell>{lease.title}</TableCell>
                    <TableCell>{lease.username}</TableCell>
                    <TableCell>
                      {new Date(lease.lease_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </TableCell>
                    <TableCell>
                      {new Date(lease.due_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={lease.status === 'pending' ? lease.approval_status : lease.status}
                        color={
                          lease.approval_status === 'pending' ? 'warning' :
                          lease.approval_status === 'rejected' ? 'error' :
                          lease.status === 'active' ? 'info' :
                          lease.status === 'returned' ? 'success' : 'default'
                        }
                        size="small"
                        onClick={() => handleOpenDetailsDialog(lease)}
                        sx={{ cursor: 'pointer' }}
                      />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      No pending lease requests found.
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Bulk Action Dialog */}
      <Dialog open={actionDialogOpen} onClose={handleCloseActionDialog}>
        <DialogTitle>Bulk Lease Actions</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, pt: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Select an action to perform on {Object.keys(selectedLeases).length} selected lease(s):
            </Typography>
            
            <FormControl fullWidth margin="normal">
              <InputLabel>Action</InputLabel>
              <Select
                value={selectedAction || ''}
                onChange={(e) => setSelectedAction(e.target.value)}
                label="Action"
              >
                <MenuItem value="approve">Approve All</MenuItem>
                <MenuItem value="reject">Reject All</MenuItem>
              </Select>
            </FormControl>
            
            {selectedAction === 'reject' && (
              <TextField
                fullWidth
                label="Rejection Reason"
                value={rejectionReason || ''}
                onChange={(e) => setRejectionReason(e.target.value)}
                margin="normal"
                multiline
                rows={3}
                required
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseActionDialog}>Cancel</Button>
          <Button
            onClick={handleExecuteAction}
            color={selectedAction === 'approve' ? 'success' : 'error'}
            variant="contained"
            disabled={loading || (selectedAction === 'reject' && !rejectionReason)}
          >
            {loading ? <CircularProgress size={24} /> : 'Execute'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Lease Details Dialog */}
      <LeaseDetailsDialog 
        open={detailsDialogOpen} 
        onClose={handleCloseDetailsDialog} 
        lease={selectedLease} 
      />
    </motion.div>
  );
};

export default BulkLeaseActions;