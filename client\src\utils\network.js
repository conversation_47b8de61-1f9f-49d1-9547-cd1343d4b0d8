import api from './api';

/**
 * Network Utility Functions
 * Helper functions for testing and debugging API connectivity
 */

// Test API connectivity with ping endpoints
export const testApiConnectivity = async () => {
  const results = {
    proxy: { status: 'unknown', error: null },
    direct: { status: 'unknown', error: null },
  };
  
  // Test the proxy first
  try {
    const proxyRes = await api.direct.get('/api/debug');
    results.proxy = {
      status: 'success',
      statusCode: proxyRes.status,
      data: proxyRes.data
    };
  } catch (error) {
    results.proxy = {
      status: 'failed',
      statusCode: error.response?.status || 'no response',
      error: error.message
    };
  }
  
  // Test direct API connection
  try {
    const directRes = await api.direct.get('/api/debug');
    results.direct = {
      status: 'success',
      statusCode: directRes.status,
      data: directRes.data
    };
  } catch (error) {
    results.direct = {
      status: 'failed',
      statusCode: error.response?.status || 'no response',
      error: error.message
    };
  }
  
  return results;
};

// Get connection info
export const getConnectionInfo = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const productionApiUrl = process.env.REACT_APP_API_URL || window.location.origin;

  return {
    proxy: {
      baseUrl: api.defaults.baseURL,
      withCredentials: api.defaults.withCredentials || false,
    },
    direct: {
      serverUrl: isDevelopment 
        ? 'http://localhost:8080' 
        : productionApiUrl
    },
    browser: {
      origin: window.location.origin,
      hostname: window.location.hostname,
      port: window.location.port || (window.location.protocol === 'https:' ? '443' : '80')
    }
  };
};

// API diagnostic information
export const getDiagnosticInfo = async () => {
  const connectivity = await testApiConnectivity();
  const connectionInfo = getConnectionInfo();
  
  return {
    timestamp: new Date().toISOString(),
    connectivity,
    connectionInfo,
    environment: process.env.NODE_ENV,
    userAgent: navigator.userAgent
  };
};

export default {
  testApiConnectivity,
  getConnectionInfo,
  getDiagnosticInfo
}; 