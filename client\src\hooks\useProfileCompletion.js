import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import api from '../utils/api';

const useProfileCompletion = () => {
  const { user, isAuthenticated, loading: authLoading } = useContext(AuthContext);
  const [profileStatus, setProfileStatus] = useState({
    profile_completed: false,
    class_teacher: null,
    grade: null,
    loading: true,
    error: null
  });
  const [showModal, setShowModal] = useState(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  // Check if profile completion modal should be shown
  const shouldShowModal = () => {
    return (
      isAuthenticated &&
      user &&
      user.email_verified === 1 &&
      (!user.profile_completed || !user.class_teacher || !user.grade) &&
      !profileStatus.loading &&
      !profileStatus.error
    );
  };

  // Fetch profile status
  const fetchProfileStatus = async () => {
    if (!isAuthenticated || !user) {
      setProfileStatus(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setProfileStatus(prev => ({ ...prev, loading: true, error: null }));

      const response = await api.direct.get('/api/users/me/profile-status');

      setProfileStatus({
        profile_completed: response.data.profile_completed,
        class_teacher: response.data.class_teacher,
        grade: response.data.grade,
        loading: false,
        error: null
      });

      // Show modal if profile is incomplete and user is verified
      if (
        user.email_verified === 1 &&
        (!response.data.profile_completed || !response.data.class_teacher || !response.data.grade)
      ) {
        setShowModal(true);
      }
    } catch (error) {
      console.error('Error fetching profile status:', error);
      setProfileStatus(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Failed to fetch profile status'
      }));
    }
  };

  // Handle profile completion
  const handleProfileComplete = (profileData) => {
    setProfileStatus({
      profile_completed: true,
      class_teacher: profileData.class_teacher,
      grade: profileData.grade,
      loading: false,
      error: null
    });
    setShowModal(false);
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowModal(false);
  };

  // Check if user can perform actions that require complete profile
  const canPerformAction = () => {
    return (
      isAuthenticated &&
      user &&
      user.email_verified === 1 &&
      user.profile_completed &&
      user.class_teacher &&
      user.grade
    );
  };

  // Get profile completion error message for actions
  const getActionErrorMessage = () => {
    if (!isAuthenticated) {
      return 'Please log in to continue.';
    }
    if (!user) {
      return 'User information not available.';
    }
    if (user.email_verified !== 1) {
      return 'Please verify your email address first.';
    }
    if (!user.profile_completed || !user.class_teacher || !user.grade) {
      return 'Please complete your profile by providing your class teacher and grade information.';
    }
    return null;
  };

  // Effect to fetch profile status when user changes
  useEffect(() => {
    if (!isAuthenticated || !user) {
      // Reset profile status when user logs out
      setProfileStatus({
        profile_completed: false,
        class_teacher: null,
        grade: null,
        loading: false,
        error: null
      });
      setShowModal(false);
      setInitialCheckDone(false);
    } else {
      setInitialCheckDone(false);
      fetchProfileStatus();
    }
  }, [isAuthenticated, user]);

  // Effect to show modal when conditions are met
  useEffect(() => {
    // Don't do anything if auth is still loading
    if (authLoading) {
      return;
    }

    // Don't show modal if we already know the profile is completed from our local state
    if (profileStatus.profile_completed) {
      setShowModal(false);
      setInitialCheckDone(true);
      return;
    }

    // Add a small delay to ensure user data is fully loaded
    const timer = setTimeout(() => {
      // Only show modal after initial auth check is complete
      if (
        !authLoading &&
        isAuthenticated &&
        user &&
        user.email_verified === 1 &&
        (!user.profile_completed || !user.class_teacher || !user.grade)
      ) {
        setShowModal(true);
      } else {
        setShowModal(false);
      }

      setInitialCheckDone(true);
    }, 100); // Small delay to ensure user data is processed

    return () => clearTimeout(timer);
  }, [isAuthenticated, user, profileStatus.profile_completed, authLoading]);

  return {
    profileStatus,
    showModal: showModal && initialCheckDone,
    handleProfileComplete,
    handleModalClose,
    canPerformAction,
    getActionErrorMessage,
    refetchProfileStatus: fetchProfileStatus
  };
};

export default useProfileCompletion;
