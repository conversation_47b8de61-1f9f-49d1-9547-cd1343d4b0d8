import React, { useState, useRef, useEffect } from 'react';
import { createWorker } from 'tesseract.js';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  useTheme,
  Button,
} from '@mui/material';
import {
  Cameraswitch as SwitchCameraIcon,
  FlashOn as FlashOnIcon,
  FlashOff as FlashOffIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useLanguage } from '../../context/LanguageContext';

const TextRecognizer = ({ onDetected, onClose }) => {
  const { translate } = useLanguage();
  const theme = useTheme();
  
  // State management
  const [torch, setTorch] = useState(false);
  const [facingMode, setFacingMode] = useState('environment');
  const [status, setStatus] = useState('initializing');
  const [errorMessage, setErrorMessage] = useState('');
  const [recognizing, setRecognizing] = useState(false);
  const [progressPercent, setProgressPercent] = useState(0);
  const [devices, setDevices] = useState([]);
  const [hasMultipleCameras, setHasMultipleCameras] = useState(false);
  
  // Refs
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const workerRef = useRef(null);
  const recognitionIntervalRef = useRef(null);
  
  // Initialize Tesseract worker
  useEffect(() => {
    const initWorker = async () => {
      try {
        workerRef.current = await createWorker({
          logger: m => {
            if (m.status === 'recognizing text') {
              setProgressPercent(parseInt(m.progress * 100));
            }
          }
        });
        
        // Only load English language and initialize with number whitelist
        await workerRef.current.loadLanguage('eng');
        await workerRef.current.initialize('eng');
        // Configure Tesseract for digit recognition
        await workerRef.current.setParameters({
          tessedit_char_whitelist: '0123456789-',
          tessedit_ocr_engine_mode: 1, // Fast but less accurate
        });
        
        setStatus('ready');
      } catch (err) {
        console.error('Error initializing Tesseract worker:', err);
        setErrorMessage('Failed to initialize text recognition');
        setStatus('error');
      }
    };
    
    initWorker();
    
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
      
      if (recognitionIntervalRef.current) {
        clearInterval(recognitionIntervalRef.current);
      }
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // Check available cameras
  useEffect(() => {
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices()
        .then(deviceList => {
          const videoDevices = deviceList.filter(device => device.kind === 'videoinput');
          setDevices(videoDevices);
          setHasMultipleCameras(videoDevices.length > 1);
        })
        .catch(err => {
          console.error('Error enumerating devices:', err);
        });
    }
  }, []);
  
  // Start camera
  const startCamera = async () => {
    if (videoRef.current) {
      try {
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
        }
        
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode,
            width: { ideal: 1280 },
            height: { ideal: 720 },
            advanced: [{ torch }]
          },
          audio: false
        });
        
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        
        setStatus('active');
        
        // Start recognition loop
        startRecognition();
      } catch (error) {
        console.error('Error starting camera:', error);
        if (error.name === 'NotAllowedError') {
          setErrorMessage(translate('Camera access denied. Please grant permission.'));
        } else if (error.name === 'NotFoundError') {
          setErrorMessage(translate('No camera found on this device.'));
        } else {
          setErrorMessage(`Error: ${error.message}`);
        }
        setStatus('error');
      }
    }
  };
  
  // Start text recognition loop
  const startRecognition = () => {
    if (recognitionIntervalRef.current) {
      clearInterval(recognitionIntervalRef.current);
    }
    
    // Process frame every 2 seconds
    recognitionIntervalRef.current = setInterval(() => {
      captureAndRecognize();
    }, 2000);
  };
  
  // Capture frame and recognize text
  const captureAndRecognize = async () => {
    if (
      !videoRef.current ||
      !canvasRef.current ||
      !workerRef.current ||
      !videoRef.current.videoWidth ||
      recognizing
    ) {
      return;
    }
    
    try {
      setRecognizing(true);
      
      // Draw video frame to canvas
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      // Set canvas size to match video
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      
      // Draw current video frame to canvas
      ctx.drawImage(
        videoRef.current,
        0,
        0,
        canvas.width,
        canvas.height
      );
      
      // Recognize text in the image
      const { data } = await workerRef.current.recognize(canvas);
      
      // Process result - extract digits
      processResult(data);
      
    } catch (error) {
      console.error('Error recognizing text:', error);
    } finally {
      setRecognizing(false);
    }
  };
  
  // Process recognition result
  const processResult = (result) => {
    if (!result || !result.text) return;
    
    // Clean the recognized text (keep only digits and hyphens)
    const text = result.text.trim();
    console.log('Recognized text:', text);
    
    // Extract potential ISBNs or numbers
    const numberPattern = /(\d[\d-]{7,})/g;
    const matches = text.match(numberPattern);
    
    if (matches && matches.length > 0) {
      console.log('Found potential numbers:', matches);
      
      // Find potential ISBNs (10 or 13 digits)
      for (const match of matches) {
        // Remove non-digit characters
        const digits = match.replace(/[^0-9]/g, '');
        
        // Check if this could be an ISBN (10 or 13 digits)
        if (digits.length === 10 || digits.length === 13) {
          console.log('Found potential ISBN:', digits);
          
          // Stop interval and send the detected ISBN
          clearInterval(recognitionIntervalRef.current);
          onDetected(digits);
          return;
        }
      }
      
      // If no ISBN found but we have numbers with 7+ digits, use the longest one
      if (matches.length > 0) {
        const longestMatch = matches.reduce((longest, current) => {
          const digitCount = current.replace(/[^0-9]/g, '').length;
          const longestDigitCount = longest.replace(/[^0-9]/g, '').length;
          return digitCount > longestDigitCount ? current : longest;
        }, matches[0]);
        
        const digits = longestMatch.replace(/[^0-9]/g, '');
        if (digits.length >= 7) {
          console.log('Using longest number sequence:', digits);
          onDetected(digits);
        }
      }
    }
  };
  
  // Toggle camera facing mode
  const toggleFacingMode = () => {
    setFacingMode(prev => prev === 'environment' ? 'user' : 'environment');
    // Restart camera with new facing mode
    setTimeout(startCamera, 300);
  };
  
  // Toggle flashlight
  const toggleTorch = () => {
    setTorch(prev => !prev);
    // Apply torch setting if stream is active
    if (streamRef.current) {
      const track = streamRef.current.getVideoTracks()[0];
      if (track && track.getCapabilities && track.getCapabilities().torch) {
        track.applyConstraints({ advanced: [{ torch: !torch }] });
      }
    }
  };
  
  // Call startCamera when component is ready
  useEffect(() => {
    if (status === 'ready') {
      startCamera();
    }
  }, [status, facingMode, torch]);
  
  return (
    <Paper
      elevation={3}
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        height: '100%',
        overflow: 'hidden',
        borderRadius: 2,
        bgcolor: 'background.paper',
      }}
    >
      {/* Status indicator and close button */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 10,
          display: 'flex',
          justifyContent: 'space-between',
          padding: 1,
          bgcolor: 'rgba(0,0,0,0.5)',
        }}
      >
        <Typography variant="body2" sx={{ color: 'white' }}>
          {recognizing ? (
            <>
              {translate('Recognizing')} ({progressPercent}%)
            </>
          ) : (
            translate('Point camera at text')
          )}
        </Typography>
        <IconButton
          size="small"
          onClick={onClose}
          sx={{ color: 'white' }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      
      {/* Video element */}
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
          bgcolor: 'black',
        }}
      >
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
        
        {/* Hidden canvas for processing */}
        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />
        
        {/* Recognition guide */}
        <Box
          sx={{
            position: 'absolute',
            border: `2px solid ${theme.palette.primary.main}`,
            width: '80%',
            height: '30%',
            top: '35%',
            left: '10%',
            boxShadow: '0 0 0 1000px rgba(0,0,0,0.3)',
            boxSizing: 'border-box',
            zIndex: 5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              position: 'absolute',
              bottom: '-25px',
              left: '50%',
              transform: 'translateX(-50%)',
              color: 'white',
              textAlign: 'center',
              width: '100%',
            }}
          >
            {translate('Align numbers within this box')}
          </Typography>
        </Box>
      </Box>
      
      {/* Camera controls */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'space-around',
          padding: 1,
          bgcolor: 'rgba(0,0,0,0.5)',
          zIndex: 10,
        }}
      >
        {hasMultipleCameras && (
          <IconButton
            onClick={toggleFacingMode}
            sx={{ color: 'white' }}
          >
            <SwitchCameraIcon />
          </IconButton>
        )}
        
        <IconButton
          onClick={toggleTorch}
          sx={{ color: 'white' }}
        >
          {torch ? <FlashOffIcon /> : <FlashOnIcon />}
        </IconButton>
        
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={captureAndRecognize}
          disabled={recognizing || status !== 'active'}
        >
          {recognizing ? 
            <CircularProgress size={24} color="inherit" /> : 
            translate('Scan Now')
          }
        </Button>
      </Box>
      
      {/* Loading/error overlay */}
      {(status === 'initializing' || status === 'error') && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            bgcolor: 'rgba(0,0,0,0.7)',
            zIndex: 20,
          }}
        >
          {status === 'initializing' ? (
            <>
              <CircularProgress color="primary" />
              <Typography variant="body1" sx={{ color: 'white', mt: 2 }}>
                {translate('Initializing text recognition...')}
              </Typography>
            </>
          ) : (
            <>
              <Typography variant="h6" sx={{ color: 'error.main' }}>
                {translate('Error')}
              </Typography>
              <Typography variant="body2" sx={{ color: 'white', mt: 1 }}>
                {errorMessage || translate('Failed to start text recognition')}
              </Typography>
            </>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default TextRecognizer; 