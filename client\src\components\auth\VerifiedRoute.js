import React, { useContext, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import LoadingScreen from '../common/LoadingScreen';

const VerifiedRoute = ({ children }) => {
  const { isAuthenticated, user, loading, refreshUserData, isRateLimited } = useContext(AuthContext);
  const [verificationChecking, setVerificationChecking] = useState(false);
  const [checkComplete, setCheckComplete] = useState(false);
  const location = useLocation();
  
  // Check localStorage first for verification status
  const isVerifiedInLocalStorage = localStorage.getItem('userEmailVerified') === '1';
  
  // Only refresh user data once when the component mounts, not on every render
  useEffect(() => {
    const checkVerification = async () => {
      // Skip check if we already know the user is verified from localStorage
      if (isVerifiedInLocalStorage) {
        setCheckComplete(true);
        return;
      }
      
      // Skip check if we already know the user is verified from context
      if (isAuthenticated && user && user.email_verified === 1) {
        localStorage.setItem('userEmailVerified', '1'); // Store for future reference
        setCheckComplete(true);
        return;
      }
      
      // Skip check if rate limited
      if (isRateLimited) {
        setCheckComplete(true);
        return;
      }
      
      if (isAuthenticated && user) {
        try {
          setVerificationChecking(true);
          const updatedUser = await refreshUserData();
          
          // If the user is verified, store it in localStorage
          if (updatedUser && updatedUser.email_verified === 1) {
            localStorage.setItem('userEmailVerified', '1');
          }
        } catch (err) {
          console.error('Error checking verification status:', err);
        } finally {
          setVerificationChecking(false);
          setCheckComplete(true);
        }
      } else {
        setCheckComplete(true);
      }
    };
    
    if (!checkComplete && !verificationChecking) {
      checkVerification();
    }
  }, [isAuthenticated, user, refreshUserData, isRateLimited, isVerifiedInLocalStorage, checkComplete, verificationChecking]);

  // Show loading screen only if actively checking or not complete yet
  if (loading || (verificationChecking && !checkComplete)) {
    return <LoadingScreen message="Checking verification status..." />;
  }

  if (!isAuthenticated) {
    // Redirect to login and save the current location they were trying to access
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user is verified - use both the user object and localStorage
  const isVerified = 
    isVerifiedInLocalStorage || 
    (user && user.email_verified === 1);
  
  if (!isVerified) {
    return <Navigate to="/verify-email" replace />;
  }

  return children;
};

export default VerifiedRoute; 